package com.sinitek.sirm.nocode.form.service.impl;

import static com.sinitek.sirm.nocode.form.constant.FormDataImportOrExportMessageCodeConstant.IMPORT_FILE_IS_EMPTY;
import static com.sinitek.sirm.nocode.form.constant.FormDataImportOrExportMessageCodeConstant.IMPORT_TASK_ADDED_DATA_NOT_EXIST;

import cn.hutool.core.collection.CollUtil;
import com.sinitek.sirm.common.attachment.service.IAttachmentExtService;
import com.sinitek.sirm.common.event.support.SiniCubeEventPublisher;
import com.sinitek.sirm.common.utils.JsonUtil;
import com.sinitek.sirm.framework.exception.BussinessException;
import com.sinitek.sirm.framework.frontend.dto.UploadDTO;
import com.sinitek.sirm.framework.frontend.dto.UploadFileDTO;
import com.sinitek.sirm.nocode.app.dto.ZdImportTaskCreateParamDTO;
import com.sinitek.sirm.nocode.app.dto.ZdImportTaskDeleteAddedParamDTO;
import com.sinitek.sirm.nocode.app.dto.ZdImportTaskRetryParamDTO;
import com.sinitek.sirm.nocode.form.constant.FormDataImportConstant;
import com.sinitek.sirm.nocode.form.constant.FormDataImportOrExportMessageCodeConstant;
import com.sinitek.sirm.nocode.form.dao.ZdImportTaskDAO;
import com.sinitek.sirm.nocode.form.dto.ZdFormDataBatchModelDTO;
import com.sinitek.sirm.nocode.form.dto.ZdImportDataParamDTO;
import com.sinitek.sirm.nocode.form.dto.ZdImportDataResultDTO;
import com.sinitek.sirm.nocode.form.dto.ZdImportTaskEventSourceDTO;
import com.sinitek.sirm.nocode.form.entity.ZdImportTask;
import com.sinitek.sirm.nocode.form.enumerate.ExportOrImportTaskStatusEnum;
import com.sinitek.sirm.nocode.form.event.ImportTaskCreateEvent;
import com.sinitek.sirm.nocode.form.event.ImportTaskRetryEvent;
import com.sinitek.sirm.nocode.form.service.IZdImportDataService;
import com.sinitek.sirm.nocode.form.service.IZdPageFormDataService;
import java.util.Collections;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @date 2025-07-21 15:46
 */
@Slf4j
@Service
public class ZdImportDataServiceImpl implements IZdImportDataService {

    @Autowired
    private ZdImportTaskDAO dao;

    @Autowired
    private SiniCubeEventPublisher eventPublisher;

    @Autowired
    private IAttachmentExtService attachmentExtService;

    @Autowired
    private IZdPageFormDataService zdPageFormDataService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void importData(ZdImportTaskCreateParamDTO param) {
        String formCode = param.getFormCode();
        UploadDTO upload = param.getUpload();
        String operatorId = param.getOperatorId();
        Date operateTime = param.getOperateTime();
        Integer importDataCreatorMode = param.getImportDataCreatorMode();

        List<UploadFileDTO> uploadFileList = upload.getUploadFileList();
        String importExcelName = null;
        if (CollUtil.isNotEmpty(uploadFileList)) {
            int size = uploadFileList.size();
            if (size > 1) {
                log.warn("操作人:{} 导入表单 {} 数据时,传入文件数量大于1,只取第一个文件",
                    operatorId, formCode);
            }
            UploadFileDTO uploadFileDTO = uploadFileList.get(0);
            uploadFileDTO.setType(FormDataImportConstant.IMPORT_ATTACHMENT_TYPE);
            uploadFileList = new LinkedList<>();
            uploadFileList.add(uploadFileDTO);

            importExcelName = uploadFileDTO.getName();
        } else {
            log.error("操作人:{} 导入表单 {} 数据时,传入文件为空", operatorId, formCode);
            throw new BussinessException(IMPORT_FILE_IS_EMPTY);
        }

        ZdImportTask task = new ZdImportTask();

        ZdImportDataParamDTO importParam = ZdImportDataParamDTO.builder()
            .importMode(param.getImportMode())
            .userColumnSettingField(param.getUserColumnSettingField())
            .importDataCreatorMode(importDataCreatorMode)
            .fieldValidFlag(param.getFieldValidFlag())
            .build();

        task.setFileName(importExcelName)
            .setStatus(ExportOrImportTaskStatusEnum.PROCESSING.getValue())
            .setOperatorId(param.getOperatorId())
            .setOperateTime(operateTime)
            .setParam(JsonUtil.toJsonString(importParam))
            .setFormCode(param.getFormCode());
        this.dao.save(task);

        Long taskId = task.getId();

        upload.setRemoveFileList(Collections.emptyList());
        upload.setUploadFileList(uploadFileList);
        upload.setRemoveFileList(Collections.emptyList());
        upload.setType(FormDataImportConstant.IMPORT_ATTACHMENT_TYPE);
        upload.setSourceEntity(FormDataImportConstant.IMPORT_ATTACHMENT_SOURCE_ENTITY);
        upload.setSourceId(taskId);

        this.attachmentExtService.saveAttachmentList(upload, taskId,
            FormDataImportConstant.IMPORT_ATTACHMENT_SOURCE_ENTITY);

        // 发布事件
        ZdImportTaskEventSourceDTO source = ZdImportTaskEventSourceDTO
            .builder()
            .taskId(taskId)
            .build();
        this.eventPublisher.publishEvent(new ImportTaskCreateEvent(source));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void retry(ZdImportTaskRetryParamDTO param) {
        Long taskId = param.getTaskId();

        ZdImportTask task = this.dao.getById(taskId);
        if (Objects.isNull(task)) {
            log.warn("导入任务[{}]不存在", taskId);
            throw new BussinessException(
                FormDataImportOrExportMessageCodeConstant.IMPORT_TASK_NOT_EXIST);
        }

        String operatorId = param.getOperatorId();
        Date operateTime = param.getOperateTime();

        if (Objects.equals(task.getStatus(), ExportOrImportTaskStatusEnum.PROCESSING.getValue())) {
            log.warn("导入任务[{}]正在处理,不在继续处理", taskId);
            throw new BussinessException(
                FormDataImportOrExportMessageCodeConstant.IMPORT_TASK_PROCESSING);
        }

        String formCode = task.getFormCode();
        String dataStr = task.getData();
        ZdImportDataResultDTO importDataResult = JsonUtil.toJavaObject(dataStr,
            ZdImportDataResultDTO.class);

        List<Long> addedIds;
        if (Objects.nonNull(importDataResult)) {
            addedIds = importDataResult.getAddedIds();
        } else {
            addedIds = Collections.emptyList();
        }

        if (CollUtil.isNotEmpty(addedIds)) {
            // 重试前删除新增数据
            ZdFormDataBatchModelDTO deleteBatchParam = new ZdFormDataBatchModelDTO();
            deleteBatchParam.setIdList(addedIds);
            deleteBatchParam.setFormCode(formCode);
            this.zdPageFormDataService.deleteBatch(deleteBatchParam);
            if (log.isInfoEnabled()) {
                log.info("导入任务[{}]重试,删除新增数据 {} 成功,共删除{}条数据", taskId,
                    JsonUtil.toJsonString(addedIds),
                    addedIds.size());
            }
        }

        task.setStatus(ExportOrImportTaskStatusEnum.PROCESSING.getValue());
        task.setOperatorId(operatorId);
        task.setOperateTime(operateTime);
        task.setData(null);
        task.setTotalCount(null);
        task.setSuccessCount(null);
        task.setFailedCount(null);
        task.setErrorMsg(null);
        this.dao.updateById(task);

        // 删除错误文件
        this.attachmentExtService.removeAttachmentList(
            FormDataImportConstant.IMPORT_ATTACHMENT_SOURCE_ENTITY, taskId,
            FormDataImportConstant.IMPORT_FAILURE_ATTACHMENT_TYPE);

        log.info("重新执行导入任务，taskId:{}, operatorId:{}，operateTime:{}", taskId,
            operatorId,
            operateTime);

        // 发布事件
        ZdImportTaskEventSourceDTO source = ZdImportTaskEventSourceDTO
            .builder()
            .taskId(taskId)
            .build();
        this.eventPublisher.publishEvent(new ImportTaskRetryEvent(source));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteAddedImportData(ZdImportTaskDeleteAddedParamDTO param) {
        Long taskId = param.getTaskId();

        ZdImportTask task = this.dao.getById(taskId);
        if (Objects.isNull(task)) {
            log.warn("导入任务[{}]不存在", taskId);
            throw new BussinessException(
                FormDataImportOrExportMessageCodeConstant.IMPORT_TASK_NOT_EXIST);
        }

        if (Objects.equals(task.getStatus(), ExportOrImportTaskStatusEnum.PROCESSING.getValue())) {
            log.warn("导入任务[{}]正在处理,不在继续处理", taskId);
            throw new BussinessException(
                FormDataImportOrExportMessageCodeConstant.IMPORT_TASK_PROCESSING);
        }

        String formCode = task.getFormCode();
        String dataStr = task.getData();
        ZdImportDataResultDTO importDataResult = JsonUtil.toJavaObject(dataStr,
            ZdImportDataResultDTO.class);

        List<Long> addedIds = importDataResult.getAddedIds();
        if (CollUtil.isNotEmpty(addedIds)) {
            // 重试前删除新增数据
            ZdFormDataBatchModelDTO deleteBatchParam = new ZdFormDataBatchModelDTO();
            deleteBatchParam.setIdList(addedIds);
            deleteBatchParam.setFormCode(formCode);
            boolean isDeletedSuccess = this.zdPageFormDataService.deleteBatch(deleteBatchParam);
            if (isDeletedSuccess) {
                if (log.isInfoEnabled()) {
                    log.info("导入任务[{}]删除新增数据 {} 成功,共删除{}条数据", taskId,
                        JsonUtil.toJsonString(addedIds),
                        addedIds.size());
                }
            } else {
                log.error("导入任务[{}]删除新增数据 {} 失败,可能是重复删除", taskId,
                    JsonUtil.toJsonString(addedIds));
                throw new BussinessException(
                    FormDataImportOrExportMessageCodeConstant.IMPORT_TASK_ADDED_DATA_DELETED);
            }
        } else {
            log.error("导入任务[{}]不存在新增数据,无需删除", taskId);
            throw new BussinessException(IMPORT_TASK_ADDED_DATA_NOT_EXIST);
        }
    }


}
