package com.sinitek.sirm.nocode.form.support.handler.parse.impl;

import static com.sinitek.sirm.nocode.form.constant.ComponentFieldConstant.OTHER_VALUE;

import com.sinitek.sirm.common.utils.JsonUtil;
import com.sinitek.sirm.nocode.form.constant.ComponentFieldConstant;
import com.sinitek.sirm.nocode.form.constant.FormSubmitFieldConstant;
import com.sinitek.sirm.nocode.form.dto.ZdPageComponentDTO;
import com.sinitek.sirm.nocode.form.enumerate.PageDataComponentTypeEnum;
import com.sinitek.sirm.nocode.form.support.ctx.ZdImportContext;
import com.sinitek.sirm.nocode.form.support.ctx.ZdParseComponentValueContext;
import com.sinitek.sirm.nocode.form.support.handler.parse.IZdComponentValueParser;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2025-07-29 09:27
 */
@Slf4j
public class ZdRadioComponentValueParser implements IZdComponentValueParser {

    @Override
    public String getMatchedComponentName() {
        return PageDataComponentTypeEnum.ZD_RADIO.getValue();
    }

    @Override
    public void parseComponentValueOnImport(ZdPageComponentDTO component,
        Map<String, Object> convertedData,
        Map<String, Object> sourceMap, ZdParseComponentValueContext ctx) {
        String ref = component.getRef();
        Object value = sourceMap.get(ref);
        Object matchedValue = this.parseZdRadioComponentValue(component,
            value);
        convertedData.put(ref, matchedValue);
    }

    @Override
    public Map<String, Object> toSubmitFileBlockOnImport(ZdPageComponentDTO pageComponent,
        Map<String, Object> valueMap, ZdImportContext ctx) {
        String ref = pageComponent.getRef();
        String label = pageComponent.getLabel();
        String componentName = pageComponent.getComponentName();

        Object value = MapUtils.getObject(valueMap, ref);

        // 前端定义的规则
        // 那就是说如果formData中对应的数据是null,submitField中就不会存在这个组件包括label,value,componentName等数据
        if (Objects.isNull(value)) {
            log.debug("当前组件 {} 对应数据为null,submitField中不应该出现这个组件的数据", ref);
            return null;
        }

        Map<String, Object> submitField = new HashMap<>();
        submitField.put(FormSubmitFieldConstant.REF, ref);
        submitField.put(FormSubmitFieldConstant.LABEL, label);
        submitField.put(FormSubmitFieldConstant.COMPONENT_NAME, componentName);
        submitField.put(FormSubmitFieldConstant.VALUE, value);

        Map<String, String> valueAndLabelOptionMap = pageComponent.getValueAndLableOptionMap();
        String valueStr = (String) value;
        String labelInOption = MapUtils.getString(valueAndLabelOptionMap, valueStr);
        String valueInSubmitField = null;
        if (StringUtils.isBlank(labelInOption)) {
            // 看看有没有其他选项
            if (valueAndLabelOptionMap.containsKey(OTHER_VALUE)) {
                // 存在其他选项
                valueInSubmitField = String.format("%s:%s", OTHER_VALUE, valueStr);
                labelInOption = valueStr;
            } else {
                log.warn("{} 组件,值 {} 在选项 {} 中没有找到对应的label", ref,
                    valueStr, JsonUtil.toJsonString(valueAndLabelOptionMap));
            }
        } else {
            valueInSubmitField = String.format("%s:%s", valueStr, labelInOption);
        }

        submitField.put(FormSubmitFieldConstant.VALUE, valueInSubmitField);
        submitField.put(FormSubmitFieldConstant.DISPLAY_VALUE, labelInOption);

        return submitField;
    }

    private Object parseZdRadioComponentValue(ZdPageComponentDTO component, Object value) {
        Object matchedValue;
        if (Objects.isNull(value)) {
            matchedValue = null;
        } else {
            String ref = component.getRef();
            Map<String, Object> optionMap = component.getLabelAndValueOptionMap();
            String labelFromExcel = (String) value;
            matchedValue = MapUtils.getObject(optionMap, labelFromExcel);
            if (Objects.isNull(matchedValue)) {
                // 可能是其他数据
                boolean hasOther = optionMap.containsKey(ComponentFieldConstant.OTHER_LABEL);
                if (hasOther) {
                    matchedValue = labelFromExcel;
                } else {
                    log.warn("{} 组件,值 {} 在选项 {} 中没有找到对应的label", ref,
                        labelFromExcel, JsonUtil.toJsonString(optionMap));
                    this.throwParseException(
                        String.format("选项 %s 没有对应的选项值", labelFromExcel));
                }
            }
        }
        return matchedValue;
    }
}
