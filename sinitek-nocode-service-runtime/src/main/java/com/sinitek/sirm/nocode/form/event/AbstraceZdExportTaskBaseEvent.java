package com.sinitek.sirm.nocode.form.event;

import com.sinitek.sirm.common.event.support.SiniCubeEvent;
import com.sinitek.sirm.nocode.form.dto.ZdExportTaskEventSourceDTO;
import io.swagger.annotations.ApiModel;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2025-07-14 09:07
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "导出任务基础事件")
public abstract class AbstraceZdExportTaskBaseEvent extends
    SiniCubeEvent<ZdExportTaskEventSourceDTO> {

    public AbstraceZdExportTaskBaseEvent(ZdExportTaskEventSourceDTO source) {
        super(source);
    }
}
