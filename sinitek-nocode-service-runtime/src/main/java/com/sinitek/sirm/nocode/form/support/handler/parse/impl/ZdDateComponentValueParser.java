package com.sinitek.sirm.nocode.form.support.handler.parse.impl;

import com.sinitek.sirm.nocode.common.utils.ZdDateUtil;
import com.sinitek.sirm.nocode.form.constant.FormSubmitFieldConstant;
import com.sinitek.sirm.nocode.form.dto.ZdPageComponentDTO;
import com.sinitek.sirm.nocode.form.enumerate.PageDataComponentTypeEnum;
import com.sinitek.sirm.nocode.form.support.ctx.ZdImportContext;
import com.sinitek.sirm.nocode.form.support.ctx.ZdParseComponentValueContext;
import com.sinitek.sirm.nocode.form.support.handler.parse.IZdComponentValueParser;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;

/**
 * <AUTHOR>
 * @date 2025-07-29 09:22
 */
@Slf4j
public class ZdDateComponentValueParser implements IZdComponentValueParser {

    @Override
    public String getMatchedComponentName() {
        return PageDataComponentTypeEnum.ZD_DATE.getValue();
    }

    @Override
    public void parseComponentValueOnImport(ZdPageComponentDTO component,
        Map<String, Object> convertedData,
        Map<String, Object> sourceMap, ZdParseComponentValueContext ctx) {
        String ref = component.getRef();
        Object value = sourceMap.get(ref);
        // 日期
        Long timestamp = this.parseZdDateComponentValue(component,
            value);
        convertedData.put(ref, timestamp);
    }

    @Override
    public Map<String, Object> toSubmitFileBlockOnImport(ZdPageComponentDTO pageComponent,
        Map<String, Object> valueMap, ZdImportContext ctx) {
        String ref = pageComponent.getRef();
        String label = pageComponent.getLabel();
        String componentName = pageComponent.getComponentName();

        Object value = MapUtils.getObject(valueMap, ref);

        // 前端定义的规则
        // 那就是说如果formData中对应的数据是null,submitField中就不会存在这个组件包括label,value,componentName等数据
        if (Objects.isNull(value)) {
            log.debug("当前组件 {} 对应数据为null,submitField中不应该出现这个组件的数据", ref);
            return null;
        }

        Map<String, Object> submitField = new HashMap<>();
        submitField.put(FormSubmitFieldConstant.REF, ref);
        submitField.put(FormSubmitFieldConstant.LABEL, label);
        submitField.put(FormSubmitFieldConstant.COMPONENT_NAME, componentName);
        submitField.put(FormSubmitFieldConstant.VALUE, value);

        // 日期
        String formatter = pageComponent.getFormatter();
        Long timestamp = MapUtils.getLong(valueMap, ref);
        String displayValue = ZdDateUtil.formatTimestamp(timestamp, formatter);
        submitField.put(FormSubmitFieldConstant.DISPLAY_VALUE, displayValue);

        return submitField;
    }

    private Long parseZdDateComponentValue(ZdPageComponentDTO component, Object value) {
        String formatter = component.getFormatter();
        String dateStr = null;
        if (value instanceof String) {
            dateStr = (String) value;
        }
        try {
            return ZdDateUtil.getTimestamp(dateStr, formatter);
        } catch (Exception e) {
            log.error("日期字符串 {} 根据 pattern [{}] 格式化失败: {} ", dateStr, formatter,
                e.getMessage(), e);
            this.throwParseException(
                String.format("【%s】解析失败，不符合 %s 格式", dateStr, formatter));
        }
        return null;
    }
}
