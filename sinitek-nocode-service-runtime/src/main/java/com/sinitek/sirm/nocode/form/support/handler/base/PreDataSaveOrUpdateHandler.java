package com.sinitek.sirm.nocode.form.support.handler.base;

import com.sinitek.sirm.nocode.common.enumerate.SaveOrUpdateEnum;
import com.sinitek.sirm.nocode.form.entity.ZdPageFormData;

/**
 * 前置数据处理接口
 *
 * <AUTHOR>
 * @version 2025.0702
 * @since 1.0.0-SNAPSHOT
 */

public interface PreDataSaveOrUpdateHandler {
    /**
     * 前置数据处理
     *
     * @param formData 表单数据
     * @param type     处理类型
     */
    void handler(ZdPageFormData formData, SaveOrUpdateEnum type);
}
