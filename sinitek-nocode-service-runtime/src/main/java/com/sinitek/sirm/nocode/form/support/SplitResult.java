package com.sinitek.sirm.nocode.form.support;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025-05-20 17:54
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("拆分结果")
public class SplitResult {

    @ApiModelProperty("原始值")
    private String rawName;

    @ApiModelProperty("名称(括号外)")
    private String orgName;

    @ApiModelProperty("其他(括号内)")
    private String otherName;

    @ApiModelProperty("需要拆分标识")
    private Boolean needSplitFlag;

}
