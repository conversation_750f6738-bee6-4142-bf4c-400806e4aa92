package com.sinitek.sirm.nocode.form.util;

import cn.hutool.core.collection.CollUtil;
import com.sinitek.sirm.nocode.app.dto.ZdExportTaskSearchParamDTO;
import com.sinitek.sirm.nocode.app.dto.ZdExportTaskSearchResultDTO;
import com.sinitek.sirm.nocode.form.po.ZdExportTaskSearchParamPO;
import com.sinitek.sirm.nocode.form.po.ZdExportTaskSearchResultPO;
import java.util.List;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import org.springframework.beans.BeanUtils;

/**
 * <AUTHOR>
 * @date 2025-07-08 16:37
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class ZdExportTaskConvertUtil {

    public static ZdExportTaskSearchParamPO makeSearchParamDTO2PO(
        ZdExportTaskSearchParamDTO dto) {

        ZdExportTaskSearchParamPO po = new ZdExportTaskSearchParamPO();
        po.setFormCode(dto.getFormCode());
        po.setFileName(dto.getFileName());
        po.setStatus(dto.getStatus());
        po.setOperatorIds(dto.getOperatorIds());

        List<String> operateTimes = dto.getOperateTimes();
        if (CollUtil.isNotEmpty(operateTimes)) {
            po.setOperateTimeStart(operateTimes.get(0));
            if (operateTimes.size() > 1) {
                po.setOperateTimeEnd(operateTimes.get(1));
            }
        }

        // 分页查询参数
        po.setOrderName(dto.getOrderName());
        po.setOrderType(dto.getOrderType());
        po.setPageIndex(dto.getPageIndex());
        po.setPageSize(dto.getPageSize());

        return po;
    }

    public static ZdExportTaskSearchResultDTO makeSearchResultPO2DTO(
        ZdExportTaskSearchResultPO po) {
        ZdExportTaskSearchResultDTO dto = new ZdExportTaskSearchResultDTO();
        BeanUtils.copyProperties(po, dto);
        return dto;
    }

}
