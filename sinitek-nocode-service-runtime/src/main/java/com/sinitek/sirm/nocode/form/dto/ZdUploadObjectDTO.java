package com.sinitek.sirm.nocode.form.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @date 2025-07-08 14:41
 */
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("组织结构对象")
@EqualsAndHashCode
public class ZdUploadObjectDTO {

    @ApiModelProperty("附件来源实体名称")
    private String sourceEntity;

    @ApiModelProperty("附件来源实体ID")
    private Long sourceId;

    @ApiModelProperty("附件类型")
    private Integer type;

    @ApiModelProperty("主表单数据实例ID")
    private Long id;

    @ApiModelProperty("子表单数据实例ID")
    private String childId;

    @ApiModelProperty("文件序列")
    private Integer fileIndex;
}
