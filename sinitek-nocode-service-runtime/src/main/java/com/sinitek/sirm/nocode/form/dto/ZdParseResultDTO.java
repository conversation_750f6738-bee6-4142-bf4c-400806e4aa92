package com.sinitek.sirm.nocode.form.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @date 2025-07-22 13:21
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("解析结果")
public class ZdParseResultDTO {

    @ApiModelProperty("表头字段")
    private List<ZdImportFormHeaderFieldDTO> headFields;

    @ApiModelProperty("提交字段")
    private List<? extends AbstractZdSubmitFieldBaseDTO> submitFields;

    @ApiModelProperty("组件ref与组件map")
    private Map<String, ZdPageComponentDTO> refAndComponentMap;

}
