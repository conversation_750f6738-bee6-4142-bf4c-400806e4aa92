package com.sinitek.sirm.nocode.form.util;

import cn.hutool.core.collection.CollUtil;
import com.sinitek.sirm.nocode.form.dto.ZdFormFieldDTO;
import java.util.ArrayList;
import java.util.List;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025-07-23 13:15
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class ZdImportAndExportUtil {

    public static List<Object> getFilledList(Object value,
        int repeatCount) {
        List<Object> list = new ArrayList<>(repeatCount);
        for (int i = 0; i < repeatCount; i++) {
            list.add(value);
        }
        return list;
    }

    public static int calAllCoumntCount(List<ZdFormFieldDTO> exportFields) {
        return exportFields.stream().mapToInt(item -> {
            List<ZdFormFieldDTO> children = item.getChildren();
            if (CollUtil.isNotEmpty(children)) {
                return children.size();
            } else {
                return 1;
            }
        }).sum();
    }

    public static void fillList(List<List<Object>> expandedRows, Object value,
        int repeatCount) {
        for (int i = 0; i < repeatCount; i++) {
            List<Object> rows = expandedRows.get(i);
            rows.add(value);
        }
    }

    public static void fillListWithList(List<List<Object>> expandedRows, List<Object> list) {
        int expandedRowSize = expandedRows.size();
        int listSize = list.size();
        for (int i = 0; i < expandedRowSize; i++) {
            List<Object> rows = expandedRows.get(i);
            if (i < listSize) {
                rows.add(list.get(i));
            } else {
                rows.add(null);
            }
        }
    }

}
