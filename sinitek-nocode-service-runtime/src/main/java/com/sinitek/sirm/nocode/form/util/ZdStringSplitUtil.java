package com.sinitek.sirm.nocode.form.util;

import com.sinitek.sirm.nocode.form.support.SplitResult;
import java.util.Objects;
import java.util.Stack;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2025-05-20 17:54
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class ZdStringSplitUtil {

    private static final int DEFAULT_END_INDEX = 2;

    public static SplitResult splitOrgName(String originalName) {
        if (StringUtils.isBlank(originalName)) {
            return new SplitResult(originalName, originalName, null, false);
        }

        if (originalName.endsWith(")") || originalName.endsWith("）")) {
            Integer lastParenthesis = findLastParenthesis(originalName);
            if (Objects.nonNull(lastParenthesis)) {
                String chineseName = originalName.substring(0, lastParenthesis);
                String englishName = originalName.substring(lastParenthesis + 1,
                    originalName.length() - 1);
                if (StringUtils.isNotBlank(chineseName)
                    && StringUtils.isNotBlank(englishName)) {
                    return new SplitResult(originalName, chineseName,
                        englishName, true);
                }
            }
        }

        // 不符合拆分条件，返回原名称
        return new SplitResult(originalName, originalName, null, false);
    }

    private static Integer findLastParenthesis(String originalName) {
        Stack<Integer> symbolStack = new Stack<>();
        for (int i = originalName.length() - DEFAULT_END_INDEX; i >= 0; i--) {
            char c = originalName.charAt(i);
            if (c == ')' || c == '）') {
                symbolStack.push(i);
            } else if (c == '(' || c == '（') {
                if (symbolStack.isEmpty()) {
                    return i;
                } else {
                    symbolStack.pop();
                }
            }
        }
        return null;
    }
}
