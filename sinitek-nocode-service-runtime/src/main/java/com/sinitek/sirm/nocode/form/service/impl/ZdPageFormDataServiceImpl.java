package com.sinitek.sirm.nocode.form.service.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.lang.Pair;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.segments.MergeSegments;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sinitek.sirm.common.encryption.algorithm.asymmetric.IAsymmetricEncryption;
import com.sinitek.sirm.common.user.factory.CurrentUserFactory;
import com.sinitek.sirm.common.utils.IdUtil;
import com.sinitek.sirm.framework.exception.BussinessException;
import com.sinitek.sirm.framework.frontend.support.ITableResultFormat;
import com.sinitek.sirm.framework.frontend.support.PageDataParam;
import com.sinitek.sirm.framework.frontend.support.TableResult;
import com.sinitek.sirm.nocode.app.constant.AppConstant;
import com.sinitek.sirm.nocode.app.event.AppDeleteEvent;
import com.sinitek.sirm.nocode.common.constant.ZdCommonConstant;
import com.sinitek.sirm.nocode.common.dto.OrderItemDTO;
import com.sinitek.sirm.nocode.common.enumerate.SaveOrUpdateEnum;
import com.sinitek.sirm.nocode.common.enumerate.StatusEnum;
import com.sinitek.sirm.nocode.common.utils.BeanUtilsEx;
import com.sinitek.sirm.nocode.common.utils.ConvertUtil;
import com.sinitek.sirm.nocode.common.utils.CopyUtil;
import com.sinitek.sirm.nocode.common.utils.ZdDateUtil;
import com.sinitek.sirm.nocode.common.utils.ZdOrgUtil;
import com.sinitek.sirm.nocode.form.constant.ZdPgSqlConstant;
import com.sinitek.sirm.nocode.form.dao.ZdPageFormDataDAO;
import com.sinitek.sirm.nocode.form.dto.CustomOrderItemDTO;
import com.sinitek.sirm.nocode.form.dto.ZdFormDataBatchModelDTO;
import com.sinitek.sirm.nocode.form.dto.ZdFormDataModelDTO;
import com.sinitek.sirm.nocode.form.dto.ZdFormDataScopeCustomConditionDTO;
import com.sinitek.sirm.nocode.form.dto.ZdFormDataSearchFieldDTO;
import com.sinitek.sirm.nocode.form.dto.ZdFormDataSearchParamDTO;
import com.sinitek.sirm.nocode.form.dto.ZdPageFormConfigDTO;
import com.sinitek.sirm.nocode.form.dto.ZdPageFormDataDTO;
import com.sinitek.sirm.nocode.form.dto.ZdPageFormDataFillReportDTO;
import com.sinitek.sirm.nocode.form.dto.ZdPageFormDataFillerDTO;
import com.sinitek.sirm.nocode.form.entity.ZdPageFormData;
import com.sinitek.sirm.nocode.form.enumerate.LogicOperatorEnum;
import com.sinitek.sirm.nocode.form.enumerate.OperatorEnum;
import com.sinitek.sirm.nocode.form.enumerate.ValueTypeEnum;
import com.sinitek.sirm.nocode.form.mapstruct.ZdPageFormDataMapstruct;
import com.sinitek.sirm.nocode.form.service.IZdPageFormConfigService;
import com.sinitek.sirm.nocode.form.service.IZdPageFormDataService;
import com.sinitek.sirm.nocode.form.support.datascope.CustomFilterDataScope;
import com.sinitek.sirm.nocode.form.support.datascope.base.DataScopeConditionSetter;
import com.sinitek.sirm.nocode.form.support.handler.base.AfterDataSaveOrUpdateHandler;
import com.sinitek.sirm.nocode.form.support.handler.base.DeleteDataHandler;
import com.sinitek.sirm.nocode.form.support.handler.base.PreDataSaveOrUpdateHandler;
import com.sinitek.sirm.nocode.page.dto.ZdPageAuthDTO;
import com.sinitek.sirm.nocode.page.dto.ZdPageSceneDTO;
import com.sinitek.sirm.nocode.page.enumerate.DataScopeEnum;
import com.sinitek.sirm.nocode.page.enumerate.SceneRepeatTypeEnum;
import com.sinitek.sirm.nocode.page.enumerate.SceneSubmitEnum;
import com.sinitek.sirm.nocode.page.enumerate.SceneTypeEnum;
import com.sinitek.sirm.nocode.page.service.IZdPageAuthService;
import com.sinitek.sirm.nocode.page.service.IZdPageSceneService;
import com.sinitek.sirm.nocode.page.service.IZdPageSceneTaskService;
import com.sinitek.sirm.nocode.support.mybatis.conditions.query.LamWrapper;
import com.sinitek.sirm.org.entity.Employee;
import com.sinitek.sirm.workflow.dto.WorkflowStartParamDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;


@Service("pageFormDataService")
@RequiredArgsConstructor
@Slf4j
public class ZdPageFormDataServiceImpl implements IZdPageFormDataService, ITableResultFormat<ZdPageFormDataDTO> {
    private final ZdPageFormDataDAO dao;
    private final IZdPageFormConfigService formConfigService;
    private final IZdPageSceneService zdPageSceneService;
    private final IZdPageSceneTaskService zdPageSceneTaskService;
    private final IAsymmetricEncryption asymmetricEncryption;
    private final IZdPageAuthService authService;
    private final ZdOrgUtil orgUtil;
    private final ZdPageFormDataMapstruct mapstruct;


    @Resource
    private ObjectProvider<List<PreDataSaveOrUpdateHandler>> preDataSaveOrUpdateHandlers;

    @Resource
    private ObjectProvider<List<AfterDataSaveOrUpdateHandler>> afterDataSaveOrUpdateHandlers;
    @Resource
    private ObjectProvider<List<DeleteDataHandler>> deleteDataHandlers;
    //private final WorkflowUtil workflowUtil;
    @Resource
    private Map<String, DataScopeConditionSetter> dataScopeConditionSetterMap;

    @Override
    public TableResult<ZdPageFormDataDTO> search(ZdFormDataSearchParamDTO param) {
        QueryWrapper<ZdPageFormData> queryWrapper = dataScopeQuery(param.getZdPageAuthDTO(), param.getCurrentOrgId());
        // 表单日期查询
        LocalDate reportDate = param.getReportDate();
        if (Objects.nonNull(reportDate)) {
            // 一天的头尾
            Pair<LocalDateTime, LocalDateTime> day = ZdDateUtil.day(reportDate);
            // 查询这一天的数据
            queryWrapper.lambda().between(ZdPageFormData::getReportDate, day.getKey(), day.getValue());
        }

        // 搜索条件
        List<ZdFormDataSearchFieldDTO> searchField = param.getSearchField();
        LogicOperatorEnum logicOperator = param.getLogicOperator();
        // 全文搜索
        String allText = param.getAllText();
        if (StringUtils.isNotBlank(allText)) {
            if (CollectionUtils.isEmpty(searchField)) {
                searchField = new ArrayList<>();
            }
            ZdFormDataSearchFieldDTO like = new ZdFormDataSearchFieldDTO();
            like.setKey(ZdPgSqlConstant.ASTERISK);
            like.setOperator(OperatorEnum.LIKE);
            like.setValueType(ValueTypeEnum.STRING);
            like.setValue(allText);
            searchField.add(like);
        }
        if (CollectionUtils.isNotEmpty(searchField)) {
            ZdFormDataScopeCustomConditionDTO zdFromDataScopeCustomConditionDTO = new ZdFormDataScopeCustomConditionDTO();
            zdFromDataScopeCustomConditionDTO.setConditions(searchField);
            zdFromDataScopeCustomConditionDTO.setLogicOperator(logicOperator);
            // 设置搜索条件
            CustomFilterDataScope.setCondition(queryWrapper, zdFromDataScopeCustomConditionDTO);
        }

        List<OrderItemDTO> orderItemList = param.getOrderItemList();
        boolean hasDefaultOrder = false;
        if (CollectionUtils.isNotEmpty(orderItemList)) {
            hasDefaultOrder = true;
            // 多个字段排序
            orderItemList.forEach(orderItem -> queryWrapper.orderBy(true, PageDataParam.ASC.equalsIgnoreCase(orderItem.getOrderType()), orderItem.getOrderName()));
        }
        List<CustomOrderItemDTO> customOrderItemList = param.getCustomOrderItemList();
        // 自定义字段排序
        CustomFilterDataScope.order(queryWrapper, customOrderItemList);
        if (CollectionUtils.isNotEmpty(customOrderItemList)) {
            hasDefaultOrder = true;
        }
        if (!hasDefaultOrder) {
            // 没有默认排序，按更新时间讲下排序
            queryWrapper.lambda().orderByDesc(ZdPageFormData::getUpdateTimeStamp);

        }
        Page<ZdPageFormData> page = param.buildPage();
        String tableName = formConfigService.getTableNameByFormCode(param.getFormCode());
        Page<ZdPageFormData> zdPageFormDataPage = dao.getBaseMapper().selectPage(page, queryWrapper, tableName);
        return param.build(ConvertUtil.pageCopy(zdPageFormDataPage, mapstruct::toDTOList), this);
    }

    /**
     * 数据权限查询
     *
     * @param zdPageAuthDTO 权限
     * @param currentOrgId  当前登陆人id
     * @return 查询条件
     */
    private QueryWrapper<ZdPageFormData> dataScopeQuery(ZdPageAuthDTO zdPageAuthDTO, String currentOrgId) {
        // 假如是空的话，不加条件
        if (Objects.isNull(zdPageAuthDTO)) {
            return Wrappers.query();
        }
        QueryWrapper<ZdPageFormData> queryWrapper = Wrappers.query();
        List<String> dataScope = zdPageAuthDTO.getDataScopeList();
        queryWrapper.and(t -> {
            Set<String> orgIdSet = new HashSet<>();

            List<DataScopeEnum> dataScopeEnums = DataScopeEnum.fromList(dataScope);
            // 并集
            if (!dataScopeEnums.contains(DataScopeEnum.ALL)) {
                dataScopeEnums.forEach(s -> {
                    String beanName = s.beanName("DataScope");
                    DataScopeConditionSetter dataScopeConditionSetter = dataScopeConditionSetterMap.get(beanName);
                    if (Objects.nonNull(dataScopeConditionSetter)) {
                        dataScopeConditionSetter.setCondition(t, zdPageAuthDTO, currentOrgId, orgIdSet);
                    }
                });
            }
            if (!orgIdSet.isEmpty()) {
                // 删除查找不到的用户id
                orgIdSet.remove(ZdCommonConstant.NO_PERSON_ORG_ID);
                if (!orgIdSet.isEmpty()) {
                    // 并集
                    t.or().lambda().in(ZdPageFormData::getCreatorId, orgIdSet);
                } else {
                    t.or().ne("1", 1);
                }
            }
            MergeSegments expression = t.getExpression();
            String sqlSegment = expression.getSqlSegment();
            // 假如为空的话，那么新增一条常量数据，不然报错
            if (StringUtils.isBlank(sqlSegment)) {
                t.eq("1", 1);
            }
        });
        return queryWrapper;
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long saveOrUpdate(ZdPageFormDataDTO zdPageFormDataDTO) {
        ZdPageFormConfigDTO formConfigDTO = formConfigService.getByFormCode(zdPageFormDataDTO.getFormCode());
        zdPageFormDataDTO.setTableName(formConfigDTO.getTableName());
        String messageCode = check(zdPageFormDataDTO);
        if (Objects.nonNull(messageCode)) {
            throw new BussinessException(messageCode);
        }
        zdPageFormDataDTO.setStatus(StatusEnum.ENABLE);
        // 有流程配置
        String processCode = formConfigDTO.getProcesscode();
        Long id = saveOrUpdatePrivate(zdPageFormDataDTO);
        if (StringUtils.isNotBlank(processCode)) {
            // todo 走流程
            WorkflowStartParamDTO workflowStartParamDTO = new WorkflowStartParamDTO();
            workflowStartParamDTO.setSourceName(zdPageFormDataDTO.getTableName());
            workflowStartParamDTO.setSourceId(id);
            workflowStartParamDTO.setSysCode(processCode);
            //workflowUtil.commonAutoSubmit(workflowStartParamDTO);
        }
        return id;
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.NESTED)
    @Override
    public Long saveOrUpdateWithNestedTransactional(ZdPageFormDataDTO zdPageFormDataDTO) {
        return saveOrUpdate(zdPageFormDataDTO);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateBatchCreatorIdAndUpdaterId(String formCode, List<ZdPageFormDataDTO> list) {
        if (CollectionUtils.isNotEmpty(list)) {
            String tableName = formConfigService.getTableNameByFormCode(formCode);
            list.forEach(zdPageFormDataDTO -> dao.getBaseMapper().updateOrgId(tableName, zdPageFormDataDTO.getId(), zdPageFormDataDTO.getCreatorId(), zdPageFormDataDTO.getUpdaterId()));
        }
    }

    @Override
    public boolean delete(ZdFormDataModelDTO zdFormDataModelDTO) {
        String formCode = zdFormDataModelDTO.getFormCode();
        String tableName = formConfigService.getTableNameByFormCode(formCode);
        ZdPageFormData zdPageFormData = dao.removeById(zdFormDataModelDTO.getId(), tableName);
        boolean b = Objects.nonNull(zdPageFormData);
        if (b) {
            zdPageFormData.setTableName(tableName);
            zdPageFormData.setFormCode(formCode);
            delete(Collections.singletonList(zdPageFormData), false);
        }
        return b;
    }

    @Override
    public boolean deleteBatch(ZdFormDataBatchModelDTO zdFormDataModelDTO) {
        String formCode = zdFormDataModelDTO.getFormCode();
        String tableName = formConfigService.getTableNameByFormCode(formCode);
        List<ZdPageFormData> list = dao.removeByIds(zdFormDataModelDTO.getIdList(), tableName);
        list.forEach(zdPageFormData -> {
            zdPageFormData.setTableName(tableName);
            zdPageFormData.setFormCode(formCode);
        });
        delete(list, false);
        return !list.isEmpty();
    }

    @Override
    public ZdPageFormDataDTO getById(Long id, String formCode) {
        ZdPageFormData one = dao.getById(id, formConfigService.getTableNameByFormCode(formCode));
        return mapstruct.toDTO(one);
    }

    @Override
    public boolean dataOwnerCheck(ZdFormDataModelDTO zdFormDataModelDTO) {
        String formCode = zdFormDataModelDTO.getFormCode();
        String tableName = formConfigService.getTableNameByFormCode(formCode);
        LamWrapper<ZdPageFormData> query = LamWrapper.ins(tableName);
        query = query.eq(ZdPageFormData::getId, zdFormDataModelDTO.getId())
                .select(ZdPageFormData::getCreatorId);
        return Objects.equals(dao.stringValue(query), CurrentUserFactory.getOrgId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long temporarySave(ZdPageFormDataDTO zdPageFormDataDTO) {
        zdPageFormDataDTO.setTableName(formConfigService.getTableNameByFormCode(zdPageFormDataDTO.getFormCode()));
        String messageCode = check(zdPageFormDataDTO);
        if (Objects.nonNull(messageCode)) {
            throw new BussinessException(messageCode);
        }
        // 设置暂存状态
        zdPageFormDataDTO.setStatus(StatusEnum.DISABLE);
        // 假如是暂存的话，那么需要判断是否已经存在暂存数据，假如有的话，那么就覆盖
        ZdPageFormDataDTO temporary = getTemporary(zdPageFormDataDTO.getFormCode(), zdPageFormDataDTO.getCreatorId());
        if (Objects.nonNull(temporary)) {
            zdPageFormDataDTO.setId(temporary.getId());
        }
        return saveOrUpdatePrivate(zdPageFormDataDTO);
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.NESTED)
    @Override
    public Long temporarySaveWithNestedTransactional(ZdPageFormDataDTO zdPageFormDataDTO) {
        return temporarySave(zdPageFormDataDTO);
    }

    @Override
    public ZdPageFormDataDTO getTemporary(String formCode, String orgId) {
        LambdaQueryWrapper<ZdPageFormData> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(ZdPageFormData::getCreatorId, orgId)
                .eq(ZdPageFormData::getStatus, StatusEnum.DISABLE);
        ZdPageFormData one = dao.getOne(queryWrapper, formConfigService.getTableNameByFormCode(formCode));
        return mapstruct.toDTO(one);
    }

    /**
     * 保存或更新
     *
     * @param zdPageFormDataDTO 数据
     * @return id
     */
    private Long saveOrUpdatePrivate(ZdPageFormDataDTO zdPageFormDataDTO) {
        Long id = zdPageFormDataDTO.getId();
        ZdPageFormData org = null;
        if (IdUtil.isDataId(id)) {
            org = dao.getById(id, zdPageFormDataDTO.getTableName());
        }
        SaveOrUpdateEnum type = Objects.isNull(org) ? SaveOrUpdateEnum.SAVE : SaveOrUpdateEnum.UPDATE;
        if (Objects.equals(type, SaveOrUpdateEnum.SAVE)) {
            org = mapstruct.toEntity(zdPageFormDataDTO);
        } else {
            org = CopyUtil.copyEntityProperties(zdPageFormDataDTO, org, "reportDate", "formId");
        }
        ZdPageFormData result = org;
        preDataSaveOrUpdateHandlers.ifAvailable(handlers -> handlers.forEach(handler -> handler.handler(result, type)));
        boolean saveOrUpdate = type == SaveOrUpdateEnum.SAVE ? dao.save(org) : dao.updateById(org);
        if (saveOrUpdate) {
            // 更新成功的情况
            zdPageFormDataDTO.setId(org.getId());
            afterDataSaveOrUpdateHandlers.ifAvailable(handlers -> handlers.forEach(handler -> handler.handler(result, type)));
        }
        return org.getId();


    }

    @Override
    public ZdPageFormDataDTO view(String formCode, Long id) {
        ZdPageFormConfigDTO zdPageFormConfigDTO = formConfigService.getByFormCode(formCode);
        ZdPageFormData zdPageFormData = dao.getById(id, zdPageFormConfigDTO.getTableName());
        if (Objects.isNull(zdPageFormData)) {
            return null;
        }
        zdPageFormData.setFormCode(formCode);
        zdPageFormData.setProcesscode(zdPageFormConfigDTO.getProcesscode());
        ZdPageFormDataDTO dto = mapstruct.toDTO(zdPageFormData);
        // 设置人员名称
        format(Collections.singletonList(dto));
        return dto;
    }

    @Override
    public List<ZdPageFormDataDTO> list(String formCode, List<Long> idList) {
        List<ZdPageFormDataDTO> list = mapstruct.toDTOList(dao.listByIds(idList, formConfigService.getTableNameByFormCode(formCode)));
        format(list);
        list.forEach(dto -> dto.setFormCode(formCode));
        return list;
    }

    @Override
    public List<Long> existsId(String formCode, List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return new ArrayList<>();
        }
        LamWrapper<ZdPageFormData> query = LamWrapper.<ZdPageFormData>ins(formConfigService.getTableNameByFormCode(formCode))
                .select(ZdPageFormData::getId)
                .in(ZdPageFormData::getId, idList);
        return dao.listLong(query);
    }

    @Override
    public ZdPageFormDataFillReportDTO pageFormDataFillReport(String formCode, String reportParam) {
        ZdPageFormDataFillReportDTO report = new ZdPageFormDataFillReportDTO();
        // 获取页面场景

        // 发送日期
        LocalDate reportDate = getReportDate(reportParam);
        // 这个是发送的人员。
        List<Employee> employeeList = zdPageSceneTaskService.findEmployeeByPageCode(formCode, reportDate);
        Map<String, Employee> employeeMap = employeeList.stream().collect(Collectors.toMap(Employee::getId, employee -> employee, (v1, v2) -> v1));
        Map<String, String> empPhotosMap = orgUtil.getEmpPhotosByIds(new ArrayList<>(employeeMap.keySet()));


        report.setTotal(employeeList.size());

        report.setReportDate(reportDate);
        String tableName = formConfigService.getTableNameByFormCode(formCode);
        LamWrapper<ZdPageFormData> query = dao.query(tableName)
                .eq(ZdPageFormData::getReportDate, reportDate)
                .select(ZdPageFormData::getCreatorId, ZdPageFormData::getCreateTimeStamp, ZdPageFormData::getId);
        // 所有的填报人员
        Map<String, ZdPageFormData> map = dao.list(query).stream()
                .filter(BeanUtilsEx.distinctByKey(ZdPageFormData::getCreatorId))
                .collect(Collectors.toMap(ZdPageFormData::getCreatorId, a -> a));

        // 没有填报的人员
        List<ZdPageFormDataFillerDTO> unFilledList = new ArrayList<>();
        report.setUnFilledList(unFilledList);
        employeeList.forEach(employee -> {
            if (!map.containsKey(employee.getId())) {
                ZdPageFormDataFillerDTO convert = convert(employee, empPhotosMap);
                unFilledList.add(convert);
            }
        });
        // 填报人员
        List<ZdPageFormDataFillerDTO> filledList = new ArrayList<>();
        report.setFilledList(filledList);
        map.forEach((k, v) -> {
            Employee employee = employeeMap.get(k);
            if (Objects.nonNull(employee)) {
                ZdPageFormDataFillerDTO convert = convert(employee, empPhotosMap);
                convert.setSubmitTime(v.getCreateTimeStamp());
                convert.setId(v.getId());
                filledList.add(convert);
            }
        });

        return report;
    }


    @EventListener(condition = "#appDeleteEvent.next.equals('" + AppConstant.DE_FORM_DATA + "')")
    @Transactional(rollbackFor = Exception.class)
    public void delete(AppDeleteEvent appDeleteEvent) {
        List<String> tableNameList = appDeleteEvent.getCodeList();
        Map<String, Object> map = appDeleteEvent.getMap();
        if (CollectionUtils.isNotEmpty(tableNameList)) {
            // 假如不为空的话，删除这个表单的所有数据
            tableNameList.forEach(tableName -> {
                List<ZdPageFormData> list = dao.removeAll(tableName);
                list.forEach(x -> {
                    x.setTableName(tableName);
                    x.setFormCode((String) map.get(tableName));
                });
                delete(list, true);
            });

        }
        log.info("清空数据完成:{}", tableNameList);
    }


    /**
     * 删除数据
     * 额外数据删除
     *
     * @param list      列表
     * @param deleteAll 是否删除所有
     */
    private void delete(List<ZdPageFormData> list, boolean deleteAll) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        // 过滤掉空的数据
        list = list.stream().filter(Objects::nonNull).collect(Collectors.toList());
        List<ZdPageFormData> finalList = list;
        if (CollectionUtils.isEmpty(finalList)) {
            return;
        }
        deleteDataHandlers.ifAvailable(a -> a.forEach(handler -> handler.handler(finalList, deleteAll)));

    }

    /**
     * 场景检测
     *
     * @param zdPageFormDataDTO 表单数据
     * @return 异常信息编码
     */
    private String check(ZdPageFormDataDTO zdPageFormDataDTO) {
        Long id = zdPageFormDataDTO.getId();
        if (IdUtil.isNotDataId(id)) {
            return checkNewData(zdPageFormDataDTO);
        }
        return null;
    }

    /**
     * 检查新数据的场景限制
     *
     * @param zdPageFormDataDTO 表单数据
     * @return 异常信息编码
     */
    private String checkNewData(ZdPageFormDataDTO zdPageFormDataDTO) {
        String creatorId = zdPageFormDataDTO.getCreatorId();
        if (StringUtils.isBlank(creatorId)) {
            // 没有登陆状态
            return null;
        }

        // 新增的情况下，需要判断场景
        ZdPageSceneDTO pageScene = zdPageSceneService.getPageScene(zdPageFormDataDTO.getFormCode());
        if (Objects.isNull(pageScene)) {
            return null;
        }

        // 处理收集表场景
        SceneTypeEnum sceneType = pageScene.getSceneType();
        if (Objects.equals(sceneType, SceneTypeEnum.COLLECTION_FORM)) {
            return checkCollectionForm(zdPageFormDataDTO);
        }

        // 检查提交类型限制
        return checkSubmitType(zdPageFormDataDTO, pageScene);
    }

    /**
     * 检查收集表场景
     *
     * @param zdPageFormDataDTO 表单数据
     * @return 异常信息编码
     */
    private String checkCollectionForm(ZdPageFormDataDTO zdPageFormDataDTO) {
        String reportParam = zdPageFormDataDTO.getReportParam();
        if (StringUtils.isBlank(reportParam)) {
            // The reportParam parameter is missing!
            //return "3000015";
            // todo 临时保存
            return null;
        }
        // 设置报告日期。
        zdPageFormDataDTO.setReportDate(getReportDate(reportParam));
        return null;
    }

    /**
     * 检查提交类型限制
     *
     * @param zdPageFormDataDTO 表单数据
     * @param pageScene         页面场景
     * @return 异常信息编码
     */
    private String checkSubmitType(ZdPageFormDataDTO zdPageFormDataDTO, ZdPageSceneDTO pageScene) {
        SceneSubmitEnum submitType = pageScene.getSubmitType();
        // 假如是无限制的话，直接返回
        if (Objects.equals(submitType, SceneSubmitEnum.NONE)) {
            return null;
        }

        LamWrapper<ZdPageFormData> query = LamWrapper.<ZdPageFormData>ins(zdPageFormDataDTO.getTableName())
                .eq(ZdPageFormData::getCreatorId, zdPageFormDataDTO.getCreatorId());
        SceneTypeEnum sceneType = pageScene.getSceneType();

        if (Objects.equals(sceneType, SceneTypeEnum.COMMON)) {
            // 普通模式的话，
            if (dao.exists(query)) {
                return "3000012";
            }
        } else {
            return checkRepeatType(zdPageFormDataDTO, pageScene, query);
        }
        return null;
    }

    /**
     * 检查重复提交类型限制
     *
     * @param zdPageFormDataDTO 表单数据
     * @param pageScene         页面场景
     * @param query             查询条件
     * @return 异常信息编码
     */
    private String checkRepeatType(ZdPageFormDataDTO zdPageFormDataDTO, ZdPageSceneDTO pageScene, LamWrapper<ZdPageFormData> query) {
        // 周期内只能一次
        SceneRepeatTypeEnum repeatType = pageScene.getRepeatType();
        LocalDate now = LocalDate.now();
        switch (repeatType) {
            case DATE:
                Pair<LocalDateTime, LocalDateTime> day = ZdDateUtil.day(now);
                query.between(ZdPageFormData::getCreateTimeStamp, day.getKey(), day.getValue());
                // 每天只能提交一次
                break;
            case WEEK:
                // 每周只能提交一次
                Pair<LocalDateTime, LocalDateTime> week = ZdDateUtil.week(now);
                query.between(ZdPageFormData::getCreateTimeStamp, week.getKey(), week.getValue());
                break;
            case MONTH:
                // 每月只能提交一次
                Pair<LocalDateTime, LocalDateTime> month = ZdDateUtil.month(now);
                query.between(ZdPageFormData::getCreateTimeStamp, month.getKey(), month.getValue());
                break;
        }
        if (dao.exists(query)) {
            return "3000013";
        }
        return null;
    }

    /**
     * 解密参数,获取报告日期
     *
     * @param param 参数
     * @return 报告日期
     */
    private LocalDate getReportDate(String param) {
        String s = asymmetricEncryption.decryptByPrivateKey(param);
        return ZdDateUtil.format(s, DatePattern.PURE_DATE_PATTERN);
    }

    private static ZdPageFormDataFillerDTO convert(Employee employee, Map<String, String> empPhotosMap) {
        ZdPageFormDataFillerDTO zdPageFormDataFillerDTO = new ZdPageFormDataFillerDTO();
        zdPageFormDataFillerDTO.setOrgId(employee.getId());
        zdPageFormDataFillerDTO.setOrgName(employee.getEmpName());
        zdPageFormDataFillerDTO.setPhoto(empPhotosMap.get(employee.getId()));
        return zdPageFormDataFillerDTO;
    }

    @Override
    public List<ZdPageFormDataDTO> format(List<ZdPageFormDataDTO> data) {
        if (CollectionUtils.isNotEmpty(data)) {
            orgUtil.setOrgName(data, ZdPageFormDataDTO::getCreatorId, ZdPageFormDataDTO::setCreator);
        }
        return data;
    }
}
