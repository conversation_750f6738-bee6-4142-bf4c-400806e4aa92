package com.sinitek.sirm.nocode.form.event;

import com.sinitek.sirm.common.event.support.SiniCubeEvent;
import com.sinitek.sirm.nocode.form.dto.ZdExportResourceTaskExecParamDTO;
import io.swagger.annotations.ApiModel;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2025-07-08 13:38
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "导出任务导出资源创建事件")
public class ExportResourceTaskCreateEvent extends
    SiniCubeEvent<ZdExportResourceTaskExecParamDTO> {

    public ExportResourceTaskCreateEvent(ZdExportResourceTaskExecParamDTO source) {
        super(source);
    }

}
