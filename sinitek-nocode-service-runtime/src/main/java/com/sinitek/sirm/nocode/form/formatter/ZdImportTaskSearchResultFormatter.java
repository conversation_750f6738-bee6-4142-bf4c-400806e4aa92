package com.sinitek.sirm.nocode.form.formatter;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.sinitek.sirm.framework.frontend.support.ITableResultFormat;
import com.sinitek.sirm.nocode.app.dto.ZdImportTaskSearchResultDTO;
import com.sinitek.sirm.nocode.form.enumerate.ExportOrImportTaskStatusEnum;
import com.sinitek.sirm.org.service.IOrgService;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2025-07-08 16:30
 */
@Slf4j
@Component
public class ZdImportTaskSearchResultFormatter implements
    ITableResultFormat<ZdImportTaskSearchResultDTO> {

    @Autowired
    private IOrgService orgService;

    @Override
    public List<ZdImportTaskSearchResultDTO> format(List<ZdImportTaskSearchResultDTO> data) {
        List<String> opOrgId = data.stream().map(ZdImportTaskSearchResultDTO::getOperatorId)
            .distinct().collect(Collectors.toList());
        Map<String, String> orgIdAndOrgNameMap = this.orgService.getOrgNameMapByOrgIdList(
            opOrgId);

        data.forEach(item -> {
            Integer status = item.getStatus();

            ExportOrImportTaskStatusEnum enumItem = ExportOrImportTaskStatusEnum.fromValue(status);
            if (Objects.nonNull(enumItem)) {
                item.setStatusName(enumItem.getLabel());
            } else {
                log.warn("ExportOrImportTaskStatusEnum 未找到导入状态[{}]名称", status);
            }

            String operatorId = item.getOperatorId();
            String orgName = MapUtils.getString(orgIdAndOrgNameMap, operatorId);
            item.setOperatorName(orgName);
        });

        return data;
    }
}
