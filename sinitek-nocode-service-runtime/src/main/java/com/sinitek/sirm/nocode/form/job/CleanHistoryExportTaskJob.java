package com.sinitek.sirm.nocode.form.job;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import com.sinitek.sirm.nocode.form.service.IZdExportTaskService;

/**
 * 删除30天前的已完成任务记录
 *
 * <AUTHOR>
 * @date 2025-07-16 10:07
 */
@Component
public class CleanHistoryExportTaskJob {

    private static final int DEFAULT_DAYS = 30;

    @Autowired
    private IZdExportTaskService exportTaskService;

    /**
     * 每天凌晨2点执行
     * 
     * 删除30天前的已完成任务记录
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void cleanHistoryExportTask() {
        this.exportTaskService.cleanHistoryExportTask(DEFAULT_DAYS);
    }

}
