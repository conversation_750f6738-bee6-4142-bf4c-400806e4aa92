package com.sinitek.sirm.nocode.form.service.impl;

import static com.sinitek.sirm.nocode.form.constant.FormDataImportOrExportMessageCodeConstant.IMPORT_FILE_IS_EMPTY;
import static com.sinitek.sirm.nocode.form.constant.FormDataImportOrExportMessageCodeConstant.IMPORT_GET_UPLOAD_FILE_FAILED;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.enums.CellExtraTypeEnum;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.excel.write.builder.ExcelWriterSheetBuilder;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sinitek.sirm.common.attachment.dto.AttachmentDTO;
import com.sinitek.sirm.common.attachment.service.IAttachmentExtService;
import com.sinitek.sirm.common.utils.FileUtil;
import com.sinitek.sirm.common.utils.IOUtil;
import com.sinitek.sirm.common.utils.IdUtil;
import com.sinitek.sirm.common.utils.JsonUtil;
import com.sinitek.sirm.framework.exception.BussinessException;
import com.sinitek.sirm.framework.frontend.dto.UploadDTO;
import com.sinitek.sirm.framework.frontend.dto.UploadFileDTO;
import com.sinitek.sirm.framework.utils.UploadCommonUtils;
import com.sinitek.sirm.nocode.app.dto.ZdImportPreParseParamDTO;
import com.sinitek.sirm.nocode.app.dto.ZdImportPreParseResultDTO;
import com.sinitek.sirm.nocode.app.dto.ZdImportTaskSearchParamDTO;
import com.sinitek.sirm.nocode.app.dto.ZdImportTaskSearchResultDTO;
import com.sinitek.sirm.nocode.common.constant.GlobalSettingConstant;
import com.sinitek.sirm.nocode.common.enumerate.StatusEnum;
import com.sinitek.sirm.nocode.common.properties.ZdGlobalProperties;
import com.sinitek.sirm.nocode.common.utils.ZdTempFileUtil;
import com.sinitek.sirm.nocode.form.constant.ExportAndImportTaskExecutorConstant;
import com.sinitek.sirm.nocode.form.constant.FormDataConstant;
import com.sinitek.sirm.nocode.form.constant.FormDataExtraFieldConstant;
import com.sinitek.sirm.nocode.form.constant.FormDataImportConstant;
import com.sinitek.sirm.nocode.form.constant.FormDataImportOrExportMessageCodeConstant;
import com.sinitek.sirm.nocode.form.constant.FormSubmitFieldConstant;
import com.sinitek.sirm.nocode.form.dao.ZdImportTaskDAO;
import com.sinitek.sirm.nocode.form.dto.AbstractZdSubmitFieldBaseDTO;
import com.sinitek.sirm.nocode.form.dto.ZdDoloadImportTemplateParam;
import com.sinitek.sirm.nocode.form.dto.ZdDownloadResourceResultDTO;
import com.sinitek.sirm.nocode.form.dto.ZdExcelMergeInfoDTO;
import com.sinitek.sirm.nocode.form.dto.ZdExportOrImportLoadDetailParamDTO;
import com.sinitek.sirm.nocode.form.dto.ZdFailureReportInfoDTO;
import com.sinitek.sirm.nocode.form.dto.ZdFormFieldDTO;
import com.sinitek.sirm.nocode.form.dto.ZdGenerateImportExcelTemplateResultDTO;
import com.sinitek.sirm.nocode.form.dto.ZdImportDataErrorMsgDTO;
import com.sinitek.sirm.nocode.form.dto.ZdImportDataParamDTO;
import com.sinitek.sirm.nocode.form.dto.ZdImportDataResultDTO;
import com.sinitek.sirm.nocode.form.dto.ZdImportDataTaskExecParamDTO;
import com.sinitek.sirm.nocode.form.dto.ZdImportFormHeaderFieldDTO;
import com.sinitek.sirm.nocode.form.dto.ZdPageComponentDTO;
import com.sinitek.sirm.nocode.form.dto.ZdPageFormDTO;
import com.sinitek.sirm.nocode.form.dto.ZdPageFormDataDTO;
import com.sinitek.sirm.nocode.form.dto.ZdParseResultDTO;
import com.sinitek.sirm.nocode.form.entity.ZdImportTask;
import com.sinitek.sirm.nocode.form.enumerate.DownloadImportTempalteTypeEnum;
import com.sinitek.sirm.nocode.form.enumerate.ExportOrImportTaskStatusEnum;
import com.sinitek.sirm.nocode.form.enumerate.ImportDataCreatorModeEnum;
import com.sinitek.sirm.nocode.form.enumerate.ImportModeEnum;
import com.sinitek.sirm.nocode.form.enumerate.PageDataComponentTypeEnum;
import com.sinitek.sirm.nocode.form.po.ZdImportTaskSearchParamPO;
import com.sinitek.sirm.nocode.form.po.ZdImportTaskSearchResultPO;
import com.sinitek.sirm.nocode.form.service.IZdDownloadService;
import com.sinitek.sirm.nocode.form.service.IZdImportTaskService;
import com.sinitek.sirm.nocode.form.service.IZdPageFormDataService;
import com.sinitek.sirm.nocode.form.service.IZdPageFormService;
import com.sinitek.sirm.nocode.form.support.ctx.ZdImportContext;
import com.sinitek.sirm.nocode.form.support.ctx.ZdParseComponentValueContext;
import com.sinitek.sirm.nocode.form.support.handler.ZdOnceAbsoluteMergeStrategy;
import com.sinitek.sirm.nocode.form.support.handler.parse.IZdComponentValueParser;
import com.sinitek.sirm.nocode.form.support.handler.parse.ZdComponentValueParserContainer;
import com.sinitek.sirm.nocode.form.support.listener.ImportExcelParseListener;
import com.sinitek.sirm.nocode.form.support.listener.ImportExcelPreParseListener;
import com.sinitek.sirm.nocode.form.util.ZdImportAndExportUtil;
import com.sinitek.sirm.nocode.form.util.ZdImportTaskConvertUtil;
import com.sinitek.sirm.nocode.form.util.ZdPageDataUtil;
import com.sinitek.sirm.nocode.page.service.IZdPageService;
import com.sinitek.sirm.org.dto.OrgObjectDTO;
import com.sinitek.sirm.org.enumerate.OrgType;
import com.sinitek.sirm.org.service.IOrgService;
import com.sinitek.sirm.setting.service.ISettingExtService;
import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import reactor.util.function.Tuple2;

/**
 * 导入任务Service实现
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
@Service
public class ZdImportTaskServiceImpl implements IZdImportTaskService {

    @Autowired
    private ZdImportTaskDAO dao;

    @Autowired
    private IZdPageFormService pageFormService;

    @Autowired
    private IZdPageService pageSerVice;

    @Autowired
    private IAttachmentExtService attachmentExtService;

    @Autowired
    private ISettingExtService settingExtService;

    @Autowired
    private ZdGlobalProperties globalProperties;

    @Autowired
    private IZdPageFormDataService zdPageFormDataService;

    @Autowired
    private IOrgService orgService;

    @Autowired
    private UploadCommonUtils uploadCommonUtils;

    @Autowired
    private IZdDownloadService zdDownloadService;

    @Autowired
    private ZdComponentValueParserContainer parserContainer;

    // TODO 这些初始值后面改为可配置
    private static final int DEFAULT_ORG_NAME_AND_ORG_MAP_INIT_SIZE = 1000;
    private static final int DEFAULT_DEPT_NAME_AND_ORG_MAP_INIT_SIZE = 200;
    private static final int DEFAULT_POSITION_NAME_AND_ORG_MAP_INIT_SIZE = 200;
    private static final int DEFAULT_TEAM_NAME_AND_ORG_MAP_INIT_SIZE = 200;
    private static final int DEFAULT_ROLE_NAME_AND_ORG_MAP_INIT_SIZE = 200;

    @Lazy
    @Autowired
    private IZdImportTaskService self;

    private static final String IMPORT_EXCEL_DEFAULT_SHEET_NAME = "导入数据";

    @Override
    public IPage<ZdImportTaskSearchResultDTO> search(ZdImportTaskSearchParamDTO param) {
        ZdImportTaskSearchParamPO searchParamPO =
            ZdImportTaskConvertUtil.makeSearchParamDTO2PO(param);
        IPage<ZdImportTaskSearchResultPO> search = this.dao.search(searchParamPO);
        return search.convert(ZdImportTaskConvertUtil::makeSearchResultPO2DTO);
    }

    @Override
    public ZdGenerateImportExcelTemplateResultDTO generateImportExcelTemplate(
        ZdDoloadImportTemplateParam param)
            throws Exception {
        String formCode = param.getFormCode();

        ZdPageFormDTO pageForm = this.pageFormService.getPublishedForm(formCode);
        if (Objects.isNull(pageForm)) {
            log.error("表单不存在，formCode:{}", formCode);
            throw new BussinessException(
                FormDataImportOrExportMessageCodeConstant.EXPORT_FORM_CONFIG_NOT_FOUND);
        }

        String pageData = pageForm.getPageData();
        if (StringUtils.isBlank(pageData)) {
            log.error("表单配置为空，formCode:{}", formCode);
            throw new BussinessException(
                FormDataImportOrExportMessageCodeConstant.EXPORT_FORM_CONFIG_NOT_FOUND);
        }

        String name = this.pageSerVice.getNameByCode(formCode);
        Integer templateType = param.getTemplateType();

        DownloadImportTempalteTypeEnum downloadImportTempalteTypeEnum = DownloadImportTempalteTypeEnum.fromValue(
            templateType);
        if (Objects.isNull(downloadImportTempalteTypeEnum)) {
            downloadImportTempalteTypeEnum = DownloadImportTempalteTypeEnum.ALL_FIELD;
        }
        ZdParseResultDTO result = ZdPageDataUtil.parsePageData(
            JsonUtil.toMap(pageData), downloadImportTempalteTypeEnum);
        List<ZdImportFormHeaderFieldDTO> fields = result.getHeadFields();
        Map<String, ZdPageComponentDTO> refAndComponentMap = result.getRefAndComponentMap();
        List<List<String>> headList = fields.stream().map(item -> {
            String ref = item.getRef();
            String currentLabel = item.getLabel();
            String parentLabel = item.getParentLabel();
            List<String> head = new ArrayList<>(2);
            if (StringUtils.isNoneBlank(parentLabel)) {
                head.add(parentLabel);
            }
            ZdPageComponentDTO component = refAndComponentMap.get(ref);

            boolean requiredFlag = false;
            if (Objects.nonNull(component)) {
                requiredFlag = component.getRequiredFlag();
            }
            if (requiredFlag) {
                currentLabel = "*" + currentLabel;
            }
            head.add(currentLabel);
            return head;
        }).collect(Collectors.toList());

        File tempFile = ZdTempFileUtil.createExcelTempFile("import_template_");

        ExcelWriterBuilder write = EasyExcel.write(tempFile);

        write.head(headList);

        ExcelWriterSheetBuilder sheetBuilder = write.sheet(IMPORT_EXCEL_DEFAULT_SHEET_NAME);

        sheetBuilder.doWrite(Collections.emptyList());
        return ZdGenerateImportExcelTemplateResultDTO.builder().formName(name).file(tempFile)
                .build();
    }

    @Override
    public ZdImportPreParseResultDTO preParse(ZdImportPreParseParamDTO param) throws Exception {
        UploadDTO upload = param.getUpload();
        String formCode = param.getFormCode();

        List<UploadFileDTO> uploadFileList = upload.getUploadFileList();
        if (CollUtil.isEmpty(uploadFileList)) {
            log.error("表单 {} 导入数据时,传入文件为空", formCode);
            throw new BussinessException(IMPORT_FILE_IS_EMPTY);
        }

        ZdPageFormDTO pageForm = this.pageFormService.getPublishedForm(formCode);
        if (Objects.isNull(pageForm)) {
            log.error("导入数据时,表单不存在，formCode:{}", formCode);
            throw new BussinessException(
                FormDataImportOrExportMessageCodeConstant.EXPORT_FORM_CONFIG_NOT_FOUND);
        }

        String pageData = pageForm.getPageData();
        if (StringUtils.isBlank(pageData)) {
            log.error("导入数据时,表单配置为空，formCode:{}", formCode);
            throw new BussinessException(
                FormDataImportOrExportMessageCodeConstant.EXPORT_FORM_CONFIG_NOT_FOUND);
        }

        ZdParseResultDTO parseResult = ZdPageDataUtil.parsePageData(
            JsonUtil.toMap(pageData));
        List<ZdImportFormHeaderFieldDTO> fields = parseResult.getHeadFields();

        UploadFileDTO uploadFile = uploadFileList.get(0);
        File tempFile = ZdTempFileUtil.createExcelTempFile("import_pre_parse_");

        try (InputStream inputStream = this.attachmentExtService.getInputStreamByUpdateFile(
            uploadFile);
            FileOutputStream outputStream = new FileOutputStream(tempFile)) {
            IOUtil.copy(inputStream, outputStream);
        } catch (Exception e) {
            log.error("表单 {} 导入数据时,获取上传文件失败", formCode, e);
            throw new BussinessException(IMPORT_GET_UPLOAD_FILE_FAILED);
        }

        if (!tempFile.exists()) {
            log.error("导入数据时,临时文件不存在，file:{}", tempFile.getAbsolutePath());
            throw new BussinessException(IMPORT_GET_UPLOAD_FILE_FAILED);
        }

        ImportExcelPreParseListener listener = new ImportExcelPreParseListener(fields);
        EasyExcel.read(tempFile, listener)
            .extraRead(CellExtraTypeEnum.MERGE).sheet()
            .doRead();

        List<ZdFormFieldDTO> excelHeader = listener.findExcelHeader();
        Map<Integer, ?> cellExtraData = listener.getCellExtraData();
        List<Map<String, Object>> preParseDataList = listener.findParseResultList();
        List<Map<String, Object>> firstRowDataList = listener.findFirstRowDataList();

        ZdImportPreParseResultDTO result = new ZdImportPreParseResultDTO();
        result.setExcelHeader(excelHeader);
        result.setParseResultList(preParseDataList);
        result.setCellExtraData(cellExtraData);
        result.setAllFields(fields);
        result.setPreParsefields(firstRowDataList);
        return result;
    }

    @Async(ExportAndImportTaskExecutorConstant.DEFAULT_IMPORT_TASK_EXECUTOR_NAME)
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void runImportTaskAsync(ZdImportDataTaskExecParamDTO param) {
        Long taskId = param.getTaskId();

        ZdImportTask task = this.dao.getById(taskId);
        if (Objects.isNull(task)) {
            log.warn("导入任务[{}]不存在", taskId);
            throw new BussinessException(
                FormDataImportOrExportMessageCodeConstant.IMPORT_TASK_NOT_EXIST);
        }
        try {
            this.doRunImportTaskAsync(task, param);
        } catch (Exception e) {
            log.error("导入任务执行失败，taskId:{}", taskId, e);
            this.self.updateFailureTaskStatusWithNewTransaction(taskId,
                ZdImportDataErrorMsgDTO.builder().msg(e.getMessage()).build());
        }
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    @Override
    public void updateFailureTaskStatusWithNewTransaction(Long taskId,
        ZdImportDataErrorMsgDTO param) {
        ZdImportTask task = this.dao.getById(taskId);
        if (Objects.isNull(task)) {
            log.warn("导入任务[{}]不存在", taskId);
            throw new BussinessException(
                FormDataImportOrExportMessageCodeConstant.IMPORT_TASK_NOT_EXIST);
        }

        task.setStatus(ExportOrImportTaskStatusEnum.FAILED.getValue());
        task.setTotalCount(0);
        task.setSuccessCount(0);
        task.setFailedCount(0);
        task.setErrorMsg(JsonUtil.toJsonString(param));
        this.dao.updateById(task);
    }

    @Override
    public String loadImportResourceAttachmentId(ZdExportOrImportLoadDetailParamDTO param) {
        Long taskId = param.getTaskId();
        AttachmentDTO attachment = this.attachmentExtService.getAttachment(
            FormDataImportConstant.IMPORT_ATTACHMENT_SOURCE_ENTITY,
            taskId,
            FormDataImportConstant.IMPORT_ATTACHMENT_TYPE);
        if (Objects.nonNull(attachment)) {
            return attachment.getId();
        } else {
            log.warn("导入任务[{}]未找到导入资源附件", taskId);
        }
        return null;
    }

    @Override
    public String loadImportFailureAttachmentId(ZdExportOrImportLoadDetailParamDTO param) {
        Long taskId = param.getTaskId();
        AttachmentDTO attachment = this.attachmentExtService.getAttachment(
            FormDataImportConstant.IMPORT_ATTACHMENT_SOURCE_ENTITY,
            taskId,
            FormDataImportConstant.IMPORT_FAILURE_ATTACHMENT_TYPE);
        if (Objects.nonNull(attachment)) {
            return attachment.getId();
        } else {
            log.warn("导入任务[{}]未找到导入失败附件", taskId);
        }
        return null;
    }

    @Override
    public ZdImportDataErrorMsgDTO getImportTaskErrorDetail(Long taskId) {
        ZdImportTask task = this.dao.getById(taskId);
        if (Objects.isNull(task)) {
            log.warn("导入任务[{}]不存在", taskId);
            return null;
        }
        return JsonUtil.toJavaObject(task.getErrorMsg(), ZdImportDataErrorMsgDTO.class);
    }

    private static final String ERR_MSG = "errMsg";

    private static final String ERR_REASON = "失败原因";

    private void doRunImportTaskAsync(ZdImportTask task, ZdImportDataTaskExecParamDTO param)
        throws Exception {
        Long taskId = param.getTaskId();
        String formCode = task.getFormCode();
        String fileName = task.getFileName();
        String operatorId = task.getOperatorId();
        String plantformAdminOrgIdFromSetting = this.getPlantformAdminOrgIdFromSetting();
        if (StringUtils.isBlank(plantformAdminOrgIdFromSetting)) {
            log.warn("导入数据时,找到平台管理员账户为空,taskId:{}", taskId);
        }

        ZdImportContext ctx = new ZdImportContext();
        ctx.setFormCode(formCode);
        ctx.setTaskId(taskId);

        this.initOrgCache(ctx);

        String importParamStr = task.getParam();
        ZdImportDataParamDTO importParam = JsonUtil.toJavaObject(importParamStr,
            ZdImportDataParamDTO.class);

        String creatorFromConfig = null;
        boolean isUseExcelCreatorField = false;
        Integer importDataCreatorMode = importParam.getImportDataCreatorMode();
        if (Objects.equals(ImportDataCreatorModeEnum.CURRENT_USER.getValue(),
            importDataCreatorMode)) {
            creatorFromConfig = operatorId;
        } else if (Objects.equals(ImportDataCreatorModeEnum.PLANTFORM_ADMIN.getValue(),
            importDataCreatorMode)) {
            creatorFromConfig = plantformAdminOrgIdFromSetting;
        } else {
            isUseExcelCreatorField = true;
        }
        ctx.setUseExcelCreatorFieldFlag(isUseExcelCreatorField);

        ZdParseResultDTO result = this.getParseResult(formCode);
        List<ZdImportFormHeaderFieldDTO> fields = result.getHeadFields();
        List<? extends AbstractZdSubmitFieldBaseDTO> submitFields = result.getSubmitFields();

        Map<String, ZdPageComponentDTO> refAndComponentMap = result.getRefAndComponentMap();
        ctx.setRefAndComponentMap(refAndComponentMap);

        List<ZdImportFormHeaderFieldDTO> headFields = result.getHeadFields();
        // 需要字段校验的数据
        Map<String, ZdImportFormHeaderFieldDTO> needValidateFieldMap = headFields.stream()
            .filter(
                field -> {
                    String ref = field.getRef();
                    ZdPageComponentDTO component = refAndComponentMap.get(ref);
                    if (Objects.isNull(component)) {
                        return false;
                    }
                    return component.getRequiredFlag();
                }).collect(Collectors.toMap(
                ZdImportFormHeaderFieldDTO::getRef, field -> field));
        ctx.setNeedValidateFieldMap(needValidateFieldMap);

        List<String> associationFormCodeList = new LinkedList<>();
        result.getRefAndComponentMap().values().forEach(component -> {
            String componentName = component.getComponentName();
            if (Objects.equals(componentName,
                PageDataComponentTypeEnum.ZD_ASSOCIATION.getValue())) {
                String formCodeInAssociationForm = component.getFormCodeInAssociationForm();
                associationFormCodeList.add(formCodeInAssociationForm);
            }
        });

        String submitFieldJsonStr = JsonUtil.toJsonString(submitFields);

        File tempFile = this.getImportFile(taskId, formCode);

        // 解析excel
        ImportExcelParseListener listener = new ImportExcelParseListener(fields,
            importParam.getUserColumnSettingField());
        EasyExcel.read(tempFile, listener)
            .extraRead(CellExtraTypeEnum.MERGE).sheet()
            .doRead();

        Integer importMode = importParam.getImportMode();
        Integer fieldValidFlag = importParam.getFieldValidFlag();
        ctx.setImportMode(importMode);
        ctx.setFieldValidFlag(fieldValidFlag);
        List<Map<String, Object>> parseResultList = listener.findParseResultList();
        List<ZdFormFieldDTO> excelHeader = listener.findExcelHeader();

        List<ZdPageFormDataDTO> needSaveList = new LinkedList<>();
        List<ZdPageFormDataDTO> needUpdateList = new LinkedList<>();
        List<Map<String, Object>> failureDataList = new LinkedList<>();

        ctx.setFailureDataList(failureDataList);

        int totalSize = parseResultList.size();
        ctx.setTotalSize(totalSize);
        log.info("导入excel解析数据 {} 条", totalSize);

        List<Map<String, Object>> convertedDataList = new LinkedList<>();

        List<Long> updateIdList = new LinkedList<>();
        // 收集数据
        this.collectData(parseResultList, updateIdList, ctx);

        // 获取表中已存在数据
        Map<Long, ZdPageFormDataDTO> idAndDataMap = this.getExistsedDataMap(updateIdList,
            formCode);
        List<ZdPageFormDataDTO> existedDataList = new ArrayList<>(idAndDataMap.values());

        // 转换数据
        this.transFomrData(parseResultList, convertedDataList, refAndComponentMap,
            idAndDataMap,
            ctx);

        this.downloadAllResource(taskId, ctx);

        // 处理转换数据
        this.processConvertData(parseResultList, convertedDataList, needSaveList,
            needUpdateList,
            failureDataList, idAndDataMap,
            submitFieldJsonStr, creatorFromConfig, ctx);

        List<Long> newSavedIdList = new LinkedList<>();
        List<Long> updatedIdList = new LinkedList<>();

        // 保存或更新导入数据
        this.saveOrUpdateImportData(needSaveList, newSavedIdList, needUpdateList, updatedIdList,
            ctx);

        // 保存附件
        this.saveAttachment(ctx);

        List<ZdImportFormHeaderFieldDTO> headerFields = listener.findFields();

        // 保存错误报告
        ZdOnceAbsoluteMergeStrategy sheetWriteHandler = new ZdOnceAbsoluteMergeStrategy();
        if (CollUtil.isNotEmpty(failureDataList)) {
            this.saveFailureReport(failureDataList, headerFields, excelHeader,
                sheetWriteHandler,
                fileName, ctx);
        }

        List<ZdExcelMergeInfoDTO> mergeInfoList = sheetWriteHandler.getMergeInfoList();
        int successCount = newSavedIdList.size() + updatedIdList.size();

        ZdImportDataResultDTO importDataResult = ZdImportDataResultDTO.builder()
            .addedIds(newSavedIdList)
            .updatedIds(updatedIdList)
            .failureDataList(failureDataList)
            .beforeUpdateDataList(existedDataList)
            .failureReportInfo(ZdFailureReportInfoDTO.builder()
                .headerFields(headerFields)
                .excelHeader(excelHeader)
                .excelMergeInfoList(mergeInfoList)
                .build())
            .build();

        task.setStatus(ExportOrImportTaskStatusEnum.SUCCESS.getValue());
        task.setTotalCount(totalSize);
        task.setSuccessCount(successCount);
        task.setFailedCount(failureDataList.size());
        task.setErrorMsg(null);
        task.setData(JsonUtil.toJsonString(importDataResult));
        this.dao.updateById(task);
    }

    private void initOrgCache(ZdImportContext ctx) {
        // 当前框架版本 userName 为空
        List<OrgObjectDTO> allOrgObjects = this.orgService.findAllOrgObjects();

        // username 是 登录名
        // orgname 是 员工姓名
        Map<String, List<OrgObjectDTO>> orgNameAndOrgIdMap = new HashMap<>(
            DEFAULT_ORG_NAME_AND_ORG_MAP_INIT_SIZE);
        ctx.setOrgNameAndOrgIdMap(orgNameAndOrgIdMap);
        Map<String, List<OrgObjectDTO>> deptNameAndOrgIdMap = new HashMap<>(
            DEFAULT_DEPT_NAME_AND_ORG_MAP_INIT_SIZE);
        ctx.setDeptNameAndOrgIdMap(deptNameAndOrgIdMap);
        Map<String, List<OrgObjectDTO>> positionNameAndOrgIdMap = new HashMap<>(
            DEFAULT_POSITION_NAME_AND_ORG_MAP_INIT_SIZE);
        ctx.setPositionNameAndOrgIdMap(positionNameAndOrgIdMap);
        Map<String, List<OrgObjectDTO>> teamNameAndOrgIdMap = new HashMap<>(
            DEFAULT_TEAM_NAME_AND_ORG_MAP_INIT_SIZE);
        ctx.setTeamNameAndOrgIdMap(teamNameAndOrgIdMap);
        Map<String, List<OrgObjectDTO>> roleNameAndOrgIdMap = new HashMap<>(
            DEFAULT_ROLE_NAME_AND_ORG_MAP_INIT_SIZE);
        ctx.setRoleNameAndOrgIdMap(roleNameAndOrgIdMap);

        for (OrgObjectDTO orgObject : allOrgObjects) {
            int orgType = orgObject.getOrgType();
            if (Objects.equals(OrgType.EMPLOYEE.getEnumItemValue(), orgType)) {
                // 人员
                this.putOrgCache(orgNameAndOrgIdMap, orgObject);
            } else if (Objects.equals(OrgType.DEPT.getEnumItemValue(), orgType)) {
                // 部门
                this.putOrgCache(deptNameAndOrgIdMap, orgObject);
            } else if (Objects.equals(OrgType.POSITION.getEnumItemValue(), orgType)) {
                // 岗位
                this.putOrgCache(positionNameAndOrgIdMap, orgObject);
            } else if (Objects.equals(OrgType.TEAM.getEnumItemValue(), orgType)) {
                // 小组
                this.putOrgCache(teamNameAndOrgIdMap, orgObject);
            } else if (Objects.equals(OrgType.ROLE.getEnumItemValue(), orgType)) {
                // 角色
                this.putOrgCache(roleNameAndOrgIdMap, orgObject);
            }
        }
    }

    private void putOrgCache(Map<String, List<OrgObjectDTO>> map, OrgObjectDTO orgObject) {
        String orgName = orgObject.getOrgName();
        List<OrgObjectDTO> list = map.get(orgName);
        if (Objects.isNull(list)) {
            LinkedList<OrgObjectDTO> listInner = new LinkedList<>();
            listInner.add(orgObject);
            map.put(orgName, listInner);
        } else {
            list.add(orgObject);
        }
    }

    private void saveAttachment(ZdImportContext ctx) {
        String formCode = ctx.getFormCode();
        Map<ZdPageFormDataDTO, List<String>> needSaveOrUpdateDataAndUKMap = ctx.getNeedSaveOrUpdateDataAndUKMap();
        if (CollUtil.isNotEmpty(needSaveOrUpdateDataAndUKMap)) {
            needSaveOrUpdateDataAndUKMap.forEach((data, uks) -> {
                uks.forEach(uk -> {
                    List<String> downloadUrls = ctx.getDownloadUrlByUK(uk);

                    Tuple2<Long, Integer> tuple = ctx.parseAttachmentTypeAndSource(uk);
                    Long sourceId = tuple.getT1();
                    Integer type = tuple.getT2();

                    this.attachmentExtService.removeAttachmentList(formCode, sourceId, type);

                    List<UploadFileDTO> uploadFileList = downloadUrls.stream()
                        .map(ctx::getDownloadResult)
                        .map(ZdDownloadResourceResultDTO::getUploaderFile)
                        .peek(item -> item.setType(type))
                        .collect(Collectors.toList());
                    UploadDTO upload = new UploadDTO();
                    upload.setUploadFileList(uploadFileList);
                    upload.setRemoveFileList(Collections.emptyList());
                    upload.setType(type);
                    upload.setSourceEntity(formCode);
                    upload.setSourceId(sourceId);
                    List<AttachmentDTO> attachmentList = this.attachmentExtService.saveAttachmentList(
                        upload, upload.getSourceId(),
                        upload.getSourceEntity());
                    if (CollUtil.isEmpty(attachmentList)) {
                        log.warn("保存附件失败, sourceId: {}, type: {}", sourceId, type);
                    }
                });
            });
        }
    }

    public static List<List<String>> partition(List<String> list, int size) {
        if (size < 1) {
            return Collections.singletonList(list);
        }
        List<List<String>> partitionedLists = new ArrayList<>();
        for (int i = 0; i < list.size(); i += size) {
            // 计算当前分片的结束索引
            int end = Math.min(i + size, list.size());
            // 提取当前分片
            List<String> sublist = new ArrayList<>(list.subList(i, end));
            // 添加到结果列表中
            partitionedLists.add(sublist);
        }

        return partitionedLists;
    }

    private void downloadAllResource(Long taskId, ZdImportContext ctx)
        throws Exception {
        List<String> allNeedDownloadUrl = ctx.getAllNeedDownloadUrl();
        int allNeedDownloadSize = allNeedDownloadUrl.size();
        Map<String, ZdDownloadResourceResultDTO> downloadUrlAndResultMap;
        if (CollUtil.isEmpty(allNeedDownloadUrl)) {
            log.info("当前上传 {} 任务没有下载任务", taskId);
            downloadUrlAndResultMap = Collections.emptyMap();
        } else {
            Integer size = this.globalProperties.getSingleThreadDownloadSize();
            List<List<String>> partition = this.partition(allNeedDownloadUrl, size);
            int partSize = partition.size();
            List<CompletableFuture<List<ZdDownloadResourceResultDTO>>> collect = partition.stream()
                .map(this.zdDownloadService::downloadFromUrl).collect(Collectors.toList());

            log.info(
                "当前上传 {} 任务 开始等待下载任务完成,共 {} 个下载资源,划分为 {} 个异步下载任务",
                taskId,
                allNeedDownloadSize, partSize);
            CompletableFuture<Void> allFutures = CompletableFuture.allOf(
                collect.toArray(new CompletableFuture[0]));
            log.info("当前上传 {} 任务 所有下载任务完成", taskId);

            CompletableFuture<List<ZdDownloadResourceResultDTO>> allResultsFuture = allFutures.thenApply(
                v ->
                    collect.stream()
                        .map(CompletableFuture::join)
                        .flatMap(Collection::stream)
                        .collect(Collectors.toList()));

            List<ZdDownloadResourceResultDTO> zdDownloadResourceResults = allResultsFuture.get();

            downloadUrlAndResultMap = zdDownloadResourceResults.stream()
                .collect(Collectors.toMap(ZdDownloadResourceResultDTO::getDownloadUrl, v -> v));
        }

        ctx.setDownloadUrlAndResultMap(downloadUrlAndResultMap);
    }

    private Map<Long, ZdPageFormDataDTO> getExistsedDataMap(List<Long> updateIdList,
        String formCode) {
        if (CollUtil.isEmpty(updateIdList)) {
            return Collections.emptyMap();
        }
        List<ZdPageFormDataDTO> list = this.zdPageFormDataService.list(formCode, updateIdList);
        return list.stream()
            .collect(Collectors.toMap(ZdPageFormDataDTO::getId, item -> item));
    }

    // 获取解析结果
    private ZdParseResultDTO getParseResult(String formCode) {
        ZdPageFormDTO pageForm = this.pageFormService.getPublishedForm(formCode);
        if (Objects.isNull(pageForm)) {
            log.error("导入数据时,表单不存在，formCode:{}", formCode);
            throw new BussinessException(
                FormDataImportOrExportMessageCodeConstant.EXPORT_FORM_CONFIG_NOT_FOUND);
        }

        String pageDataJsonStr = pageForm.getPageData();
        if (StringUtils.isBlank(pageDataJsonStr)) {
            log.error("导入数据时,表单配置为空，formCode:{}", formCode);
            throw new BussinessException(
                FormDataImportOrExportMessageCodeConstant.EXPORT_FORM_CONFIG_NOT_FOUND);
        }

        Map<String, Object> pageData = JsonUtil.toMap(pageDataJsonStr);
        return ZdPageDataUtil.parsePageData(
            pageData);

    }

    // 获取导入文件资源
    private File getImportFile(Long taskId, String formCode) {
        AttachmentDTO attachment = this.attachmentExtService.getAttachment(
            FormDataImportConstant.IMPORT_ATTACHMENT_SOURCE_ENTITY, taskId,
            FormDataImportConstant.IMPORT_ATTACHMENT_TYPE);
        if (Objects.isNull(attachment)) {
            log.error("导入数据时,未找到导入文件，taskId:{}", taskId);
            throw new BussinessException(
                FormDataImportOrExportMessageCodeConstant.IMPORT_GET_UPLOAD_FILE_FAILED);
        }

        File tempFile = ZdTempFileUtil.createExcelTempFile("import_");

        try (InputStream inputStream = this.attachmentExtService.getAttachmentAsInputStream(
            attachment.getObjId());
            FileOutputStream outputStream = new FileOutputStream(tempFile)) {
            if (Objects.isNull(inputStream)) {
                log.error("根据附件Objid {} 获取输入流为空", attachment.getObjId());
                throw new BussinessException(IMPORT_GET_UPLOAD_FILE_FAILED);
            }

            IOUtil.copy(inputStream, outputStream);
        } catch (Exception e) {
            log.error("表单 {} 导入数据时,获取上传文件失败", formCode, e);
            throw new BussinessException(IMPORT_GET_UPLOAD_FILE_FAILED);
        }

        if (!tempFile.exists()) {
            log.error("导入数据时,临时文件不存在，file:{}", tempFile.getAbsolutePath());
            throw new BussinessException(IMPORT_GET_UPLOAD_FILE_FAILED);
        }

        return tempFile;
    }

    // 转换数据
    private void collectData(List<Map<String, Object>> parseResultList,
        List<Long> updateIdList, ZdImportContext ctx) {
        int totalSize = ctx.getTotalSize();
        Integer importMode = ctx.getImportMode();
        for (int i = 0; i < totalSize; i++) {
            Map<String, Object> parseResult = parseResultList.get(i);

            Long id = MapUtils.getLong(parseResult, FormDataConstant.ID);
            boolean isSaveMode = Objects.equals(ImportModeEnum.ONLY_NEW.getValue(), importMode)
                || IdUtil.isNotDataId(id);

            if (!isSaveMode) {
                // 更新模式
                updateIdList.add(id);
            }
        }
    }

    // 转换数据
    private void transFomrData(List<Map<String, Object>> parseResultList,
        List<Map<String, Object>> convertedDataList,
        Map<String, ZdPageComponentDTO> refAndComponentMap,
        Map<Long, ZdPageFormDataDTO> idAndDataMap,
        ZdImportContext ctx) {
        int totalSize = ctx.getTotalSize();
        boolean validateField = ctx.isValidateField();
        Map<String, ZdImportFormHeaderFieldDTO> needValidateFieldMap = ctx.getNeedValidateFieldMap();
        for (int dataIndex = 0; dataIndex < totalSize; dataIndex++) {
            Map<String, Object> parseResult = parseResultList.get(dataIndex);
            Map<String, Object> convertedData = new HashMap<>(parseResult.size());

            Long id = MapUtils.getLong(parseResult, FormDataConstant.ID);

            ZdPageFormDataDTO data = idAndDataMap.get(id);
            Map<String, Object> existedFormDataFromDB;
            if (Objects.nonNull(data)) {
                String formDataJsonStr = data.getFormData();
                existedFormDataFromDB = JsonUtil.toMap(formDataJsonStr);
            } else {
                existedFormDataFromDB = Collections.emptyMap();
            }

            try {
                this.doParseComponentValue(parseResult, refAndComponentMap, convertedData,
                    existedFormDataFromDB, dataIndex, ctx);

                if (validateField) {
                    this.validateField(convertedData, needValidateFieldMap);
                }
            } catch (BussinessException e) {
                log.error("第 {} 条数据,转换excel中读取的数据为formData时发生异常, {}", dataIndex,
                    e.getMessage());
                ctx.getDataIndexAndErrMsg().put(dataIndex, e.getMessage());
            } catch (Exception e) {
                log.error("第 {} 条数据,转换excel中读取的数据为formData时发生异常, {}", dataIndex,
                    e.getMessage(), e);
                ctx.getDataIndexAndErrMsg().put(dataIndex, "数据解析异常");
            }

            // 即使解析错误,也需要在convertedDataList添加占位数据,防止数据对应错误
            convertedDataList.add(convertedData);
        }
    }

    private void validateField(Map<String, Object> convertedData,
        Map<String, ZdImportFormHeaderFieldDTO> needValidateFieldMap) {
        List<ZdImportFormHeaderFieldDTO> notFoundFieldList = new LinkedList<>();
        for (Entry<String, ZdImportFormHeaderFieldDTO> entry : needValidateFieldMap.entrySet()) {
            String ref = entry.getKey();
            ZdImportFormHeaderFieldDTO field = entry.getValue();
            String parentRef = field.getParentRef();

            if (StringUtils.isNotBlank(parentRef)) {
                List<Map<String, Object>> list = (List<Map<String, Object>>) convertedData.get(
                    parentRef);
                // 子表单可以没数据，但是只要有数据就必须符合必填规则
                if (CollUtil.isNotEmpty(list)) {
                    boolean hasNoValueField = list.stream()
                        .anyMatch(map -> Objects.isNull(map.get(ref)));
                    if (hasNoValueField) {
                        notFoundFieldList.add(field);
                    }
                }
            } else {
                Object value = convertedData.get(ref);
                if (Objects.isNull(value)) {
                    notFoundFieldList.add(field);
                }
            }
            if (CollUtil.isNotEmpty(notFoundFieldList)) {
                String requiredFields = notFoundFieldList.stream()
                    .map(item -> {
                        String label = item.getLabel();
                        String parentLabel = item.getParentLabel();
                        if (StringUtils.isBlank(parentLabel)) {
                            return label;
                        } else {
                            return String.format("%s/%s", parentLabel, label);
                        }
                    })
                    .collect(Collectors.joining(","));
                throw new BussinessException(
                    true,
                    "以下字段必填: " + requiredFields);
            }
        }
    }

    private void doParseComponentValue(Map<String, Object> parseResult,
        Map<String, ZdPageComponentDTO> refAndComponentMap,
        Map<String, Object> convertedData, Map<String, Object> existedFormDataFromDB,
        int dataIndex, ZdImportContext ctx) throws Exception {
        for (Entry<String, Object> entry : parseResult.entrySet()) {
            String ref = entry.getKey();
            Object value = entry.getValue();

            ZdPageComponentDTO component = refAndComponentMap.get(ref);
            if (Objects.isNull(value)) {
                convertedData.put(ref, value);
            } else {
                if (Objects.nonNull(component)) {
                    List<ZdPageComponentDTO> children = component.getChildren();
                    if (CollUtil.isNotEmpty(children)) {
                        List<Map<String, Object>> existedDataList = (List<Map<String, Object>>) existedFormDataFromDB.get(
                            ref);
                        Map<String, Map<String, Object>> childFormIdAndDataMap;
                        if (CollUtil.isNotEmpty(existedDataList)) {
                            childFormIdAndDataMap = existedDataList.stream()
                                .collect(Collectors.toMap(
                                    item -> MapUtils.getString(item, FormDataConstant.ID),
                                    item -> item));
                        } else {
                            childFormIdAndDataMap = Collections.emptyMap();
                        }

                        List<Map<String, Object>> childFormDataSourceList = (List<Map<String, Object>>) value;
                        List<Map<String, Object>> childFormDataConvertedList = new LinkedList<>();
                        if (CollUtil.isNotEmpty(childFormDataSourceList)) {
                            for (Map<String, Object> childFormSourceDataMap : childFormDataSourceList) {
                                Map<String, Object> childFormConvertedDataMap = new HashMap<>();
                                String idStr = MapUtils.getString(childFormSourceDataMap,
                                    FormDataConstant.ID);

                                Map<String, Object> existedData;
                                if (StringUtils.isNotBlank(idStr)) {
                                    childFormConvertedDataMap.put(FormDataConstant.ID, idStr);
                                    existedData = childFormIdAndDataMap.get(
                                        idStr);
                                } else {
                                    existedData = Collections.emptyMap();
                                }

                                for (ZdPageComponentDTO child : children) {
                                    this.parseComponentValue(dataIndex, child,
                                        childFormConvertedDataMap,
                                        childFormSourceDataMap, existedData, ctx);
                                }

                                String childDataId = MapUtils.getString(
                                    childFormConvertedDataMap,
                                    FormDataConstant.ID);
                                if (StringUtils.isBlank(childDataId)) {
                                    childFormConvertedDataMap.put(FormDataConstant.ID,
                                        RandomStringUtils.random(4, true, true));
                                }
                                childFormDataConvertedList.add(childFormConvertedDataMap);
                            }
                        }
                        convertedData.put(ref, childFormDataConvertedList);
                    } else {
                        // 非子表单
                        this.parseComponentValue(dataIndex, component, convertedData,
                            parseResult,
                            existedFormDataFromDB, ctx);
                    }
                } else {
                    // 没有匹配的组件
                    if (Objects.equals(FormDataExtraFieldConstant.ID_FIELD_KEY, ref)
                        || Objects.equals(FormDataExtraFieldConstant.CREATOR_FIELD_KEY, ref)
                        || Objects.equals(FormDataExtraFieldConstant.CREATE_TIMESTAMP_FIELD_KEY,
                        ref)
                        || Objects.equals(FormDataExtraFieldConstant.UPDATE_TIMESTAMP_FIELD_KEY,
                        ref)) {
                        convertedData.put(ref, value);
                    }
                }
            }
        }
        ;
    }

    // 针对组件特点对数据进行处理
    private void parseComponentValue(int dataIndex, ZdPageComponentDTO component,
        Map<String, Object> convertedData, Map<String, Object> sourceMap,
        Map<String, Object> existedFormDataFromDB,
        ZdImportContext ctx) throws Exception {
        String componentName = component.getComponentName();

        IZdComponentValueParser parser = this.parserContainer.getParser(componentName);
        parser.parseComponentValueOnImport(component, convertedData, sourceMap,
            ZdParseComponentValueContext.builder().dataIndex(dataIndex)
                .existedFormDataFromDB(existedFormDataFromDB).ctx(ctx).build());
    }



    // 处理转换的数据
    private void processConvertData(List<Map<String, Object>> parseResultList,
        List<Map<String, Object>> convertedDataList, List<ZdPageFormDataDTO> needSaveList,
        List<ZdPageFormDataDTO> needUpdateList,
        List<Map<String, Object>> failureDataList,
        Map<Long, ZdPageFormDataDTO> idAndDataMap,
        String submitFieldJsonStr, String creatorFromConfig, ZdImportContext ctx) {
        Integer totalSize = ctx.getTotalSize();
        String formCode = ctx.getFormCode();
        boolean isUseExcelCreatorField = ctx.getUseExcelCreatorFieldFlag();
        Integer importMode = ctx.getImportMode();
        Map<String, List<OrgObjectDTO>> orgNameAndOrgIdMap = ctx.getOrgNameAndOrgIdMap();

        Map<ZdPageFormDataDTO, List<String>> needSaveOrUpdateDataAndUKMap = new HashMap<>();
        Map<Integer, String> dataIndexAndErrMsgMap = ctx.getDataIndexAndErrMsg();
        for (int dataIndex = 0; dataIndex < totalSize; dataIndex++) {
            log.info("开始处理第 {} 条数据", dataIndex + 1);

            ZdPageFormDataDTO pageFormData;
            Map<String, Object> formData;
            List<Object> submitFields;

            // 解析数据
            Map<String, Object> parseResult = parseResultList.get(dataIndex);
            // 转换后数据
            Map<String, Object> convertedData = convertedDataList.get(dataIndex);
            Long id = MapUtils.getLong(convertedData, FormDataConstant.ID);
            try {
                String creatorFromExcel = MapUtils.getString(convertedData,
                    FormDataExtraFieldConstant.CREATOR_FIELD_KEY);

                String errMsg = dataIndexAndErrMsgMap.get(dataIndex);
                if (StringUtils.isNotBlank(errMsg)) {
                    this.appendFailureDataList(failureDataList, parseResult, errMsg);
                    log.info("第 {} 条数据已解析失败,不再继续解析", dataIndex + 1);
                    continue;
                }

                String creatorOrgIdFromExcel = null;

                if (isUseExcelCreatorField) {
                    creatorOrgIdFromExcel = this.getCreatorOrgId(creatorFromExcel,
                        orgNameAndOrgIdMap, failureDataList, parseResult);
                    if (Objects.isNull(creatorOrgIdFromExcel)) {
                        log.debug(
                            "导入数据时,当前使用excel中创建人,创建人对应OrgId为空,跳过当前数据");
                        continue;
                    }
                }

                // 当前为仅新增或没有id时为保存模式
                boolean isSaveMode = Objects.equals(ImportModeEnum.ONLY_NEW.getValue(), importMode)
                    || IdUtil.isNotDataId(id);

                pageFormData = new ZdPageFormDataDTO();

                pageFormData.setId(id);
                pageFormData.setFormCode(formCode);

                if (isSaveMode) {
                    // 构建submitFields
                    List<Object> submitFieldsInner = JsonUtil.toList(
                        submitFieldJsonStr);

                    // 保存模式下 需要设置创建人与更信人
                    if (isUseExcelCreatorField) {
                        pageFormData.setCreatorId(creatorOrgIdFromExcel);
                        pageFormData.setUpdaterId(creatorOrgIdFromExcel);
                    } else {
                        pageFormData.setCreatorId(creatorFromConfig);
                        pageFormData.setUpdaterId(creatorFromConfig);
                    }

                    // 设置formData
                    // 移除框架字段
                    convertedData.remove(FormDataExtraFieldConstant.ID_FIELD_KEY);
                    convertedData.remove(FormDataExtraFieldConstant.CREATOR_FIELD_KEY);
                    convertedData.remove(FormDataExtraFieldConstant.CREATE_TIMESTAMP_FIELD_KEY);
                    convertedData.remove(FormDataExtraFieldConstant.UPDATE_TIMESTAMP_FIELD_KEY);

                    // 构建submitFields
                    for (Object o : submitFieldsInner) {
                        Map<String, Object> submitField
                            = (Map<String, Object>) o;
                        String ref = MapUtils.getString(submitField, FormSubmitFieldConstant.REF);
                        ZdPageComponentDTO pageComponent = ctx.getPageComponent(ref);
                        Map<String, Object> submitFileBlock = this.toSubmitFileBlock(pageComponent,
                            convertedData, ctx);
                        if (Objects.nonNull(submitFileBlock)) {
                            submitField.putAll(submitFileBlock);
                        }
                    }

                    formData = convertedData;
                    submitFields = submitFieldsInner;
                } else {
                    ZdPageFormDataDTO existedData = idAndDataMap.get(id);

                    if (Objects.isNull(existedData)) {
                        log.warn("根据实例ID没有找到对应数据,实例ID:{}", id);
                        this.appendFailureDataList(failureDataList, parseResult,
                            "根据实例ID没有找到对应数据");
                        continue;
                    }

                    pageFormData = JsonUtil.jsonCopy(existedData, ZdPageFormDataDTO.class);

                    // 更新模式下仅设置更新人
                    if (isUseExcelCreatorField) {
                        pageFormData.setUpdaterId(creatorOrgIdFromExcel);
                    } else {
                        pageFormData.setUpdaterId(creatorFromConfig);
                    }

                    String formDataJsonstr = pageFormData.getFormData();
                    String submitFieldJsonstr = pageFormData.getSubmitField();

                    Map<String, Object> formDataExisted = JsonUtil.toMap(formDataJsonstr);

                    // 更新formData
                    for (Entry<String, Object> entry : formDataExisted.entrySet()) {
                        String key = entry.getKey();
                        Object value = convertedData.get(key);
                        entry.setValue(value);
                    }

                    List<Object> submitFieldsExisted = JsonUtil.toList(submitFieldJsonstr);
                    for (Object o : submitFieldsExisted) {
                        Map<String, Object> submitField
                            = (Map<String, Object>) o;
                        String ref = MapUtils.getString(submitField, FormSubmitFieldConstant.REF);
                        ZdPageComponentDTO pageComponent = ctx.getPageComponent(ref);
                        Map<String, Object> submitFileBlock = this.toSubmitFileBlock(pageComponent,
                            formDataExisted, ctx);
                        if (Objects.nonNull(submitFileBlock)) {
                            submitField.putAll(submitFileBlock);
                        }
                    }

                    formData = formDataExisted;
                    submitFields = submitFieldsExisted;
                }
            } catch (BussinessException e) {
                log.error("第 {} 条数据,处理数据转为submitField时出现异常, {}", dataIndex,
                    e.getMessage());
                this.appendFailureDataList(failureDataList, parseResult, e.getMessage());
                continue;
            } catch (Exception e) {
                log.error("第 {} 条数据,处理数据转为submitField时出现异常, {}", dataIndex,
                    e.getMessage(), e);
                this.appendFailureDataList(failureDataList, parseResult, "数据转化异常");
                continue;
            }

            pageFormData.setFormData(JsonUtil.toJsonString(formData));
            pageFormData.setSubmitField(JsonUtil.toJsonString(submitFields));

            List<String> uks = ctx.getUKsFromIndex(dataIndex);
            List<String> downloadUrls;
            if (CollUtil.isNotEmpty(uks)) {
                downloadUrls = uks.stream().map(ctx::getDownloadUrlByUK)
                    .filter(CollUtil::isNotEmpty).flatMap(Collection::stream).collect(
                        Collectors.toList());
            } else {
                downloadUrls = Collections.emptyList();
            }

            boolean isNeesDownload = CollUtil.isNotEmpty(downloadUrls);
            if (isNeesDownload) {
                String downloadFailUrls = downloadUrls.stream().filter(downloadUrl -> {
                    ZdDownloadResourceResultDTO downloadResult = ctx.getDownloadResult(downloadUrl);
                    if (Objects.isNull(downloadResult)) {
                        return false;
                    } else {
                        return !Objects.equals(Boolean.TRUE, downloadResult.getFlag());
                    }
                }).collect(Collectors.joining(","));
                if (StringUtils.isNotBlank(downloadFailUrls)) {
                    String msg = String.format("以下链接下载失败: %s", downloadFailUrls);
                    this.appendFailureDataList(failureDataList, parseResult,
                        msg);
                    log.warn("第 {} 条解析数据,{}", dataIndex + 1, msg);
                    return;
                } else {
                    log.info("第 {} 条解析数据待下载的链接全部下载成功", dataIndex + 1);
                }
            } else {
                log.info("第 {} 条解析数据没有待下载的链接", dataIndex + 1);
            }

            pageFormData.setFormCode(formCode);
            if (Objects.equals(ImportModeEnum.ONLY_NEW.getValue(), importMode)) {
                // 只新增数据
                pageFormData.setId(null);
                needSaveList.add(pageFormData);
                log.info("第 {} 条解析数据为新增数据", dataIndex + 1);
            } else if (Objects.equals(ImportModeEnum.UPDATE_EXISTING.getValue(), importMode)) {
                // 只更新数据
                needUpdateList.add(pageFormData);
                log.info("第 {} 条解析数据为更新数据", dataIndex + 1);
            } else if (Objects.equals(ImportModeEnum.BOTH.getValue(), importMode)) {
                // 导入新增和更新数据
                if (IdUtil.isDataId(id)) {
                    needUpdateList.add(pageFormData);
                    log.info("第 {} 条解析数据为更新数据", dataIndex + 1);
                } else {
                    pageFormData.setId(null);
                    needSaveList.add(pageFormData);
                    log.info("第 {} 条解析数据为新增数据", dataIndex + 1);
                }
            }

            if (isNeesDownload) {
                needSaveOrUpdateDataAndUKMap.put(pageFormData, uks);
            }
        }
        ctx.setNeedSaveOrUpdateDataAndUKMap(needSaveOrUpdateDataAndUKMap);
    }

    private Map<String, Object> toSubmitFileBlock(ZdPageComponentDTO pageComponent,
        Map<String, Object> valueMap, ZdImportContext ctx) {
        String componentName = pageComponent.getComponentName();
        IZdComponentValueParser parser = parserContainer.getParser(componentName);
        return parser.toSubmitFileBlockOnImport(pageComponent, valueMap, ctx);
    }

    private String getCreatorOrgId(String creatorFromExcel,
        Map<String, List<OrgObjectDTO>> orgNameAndOrgIdMap,
        List<Map<String, Object>> failureDataList,
        Map<String, Object> parseResult) {
        if (StringUtils.isBlank(creatorFromExcel)) {
            log.warn("导入数据时,创建人为空,请检查导入数据,人员姓名:{}", creatorFromExcel);
            this.appendFailureDataList(failureDataList, parseResult,
                "创建人为空");
            return null;
        }

        List<OrgObjectDTO> orgObjectList = orgNameAndOrgIdMap.get(creatorFromExcel);
        if (CollUtil.isEmpty(orgObjectList)) {
            log.warn("导入数据时,创建人不存在,请检查导入数据,人员姓名:{}",
                creatorFromExcel);
            this.appendFailureDataList(failureDataList, parseResult,
                "创建人不存在");
            return null;
        }

        if (orgObjectList.size() == 1) {
            OrgObjectDTO orgObject = orgObjectList.get(0);
            if (Objects.isNull(orgObject)) {
                log.warn("导入数据时,创建人不存在,请检查导入数据,人员姓名:{}",
                    creatorFromExcel);
                this.appendFailureDataList(failureDataList, parseResult,
                    "创建人没有匹配的数据");
                return null;
            }
            return orgObject.getOrgId();
        } else {
            // 有多条数据
            log.warn(
                "导入数据时,存在多个同名人员,无法确定创建人,请检查导入数据,人员姓名:{}",
                creatorFromExcel);
            this.appendFailureDataList(failureDataList, parseResult,
                "创建人存在同名人员,无法确定数据");
            return null;
        }
    }

    // 保存或更新导入数据
    private void saveOrUpdateImportData(List<ZdPageFormDataDTO> needSaveList,
        List<Long> newSavedIdList,
        List<ZdPageFormDataDTO> needUpdateList,
        List<Long> updatedIdList, ZdImportContext ctx) {
        Long taskId = ctx.getTaskId();
        String formCode = ctx.getFormCode();

        List<ZdPageFormDataDTO> needUpdateCreatorIdOrUpdaterIdList = new LinkedList<>();

        if (CollUtil.isNotEmpty(needSaveList)) {
            int size = needSaveList.size();
            log.info("导入任务 {} 共有 {} 条数据需要新增", taskId, size);
            for (int i = 0; i < size; i++) {
                ZdPageFormDataDTO needSaveData = needSaveList.get(i);

                ZdPageFormDataDTO zdPageFormData = new ZdPageFormDataDTO();
                zdPageFormData.setCreatorId(needSaveData.getCreatorId());
                zdPageFormData.setUpdaterId(needSaveData.getUpdaterId());
                log.info("待新增数据中,新增第 {} 条数据", i + 1);
                this.zdPageFormDataService.saveOrUpdateWithNestedTransactional(needSaveData);

                Long id = needSaveData.getId();
                newSavedIdList.add(id);

                needSaveData.setId(id);
                zdPageFormData.setId(id);
                needUpdateCreatorIdOrUpdaterIdList.add(zdPageFormData);
            }
        } else {
            log.info("当前导入任务 {} 没有需要新增的数据", taskId);
        }

        if (CollUtil.isNotEmpty(needUpdateList)) {
            int size = needUpdateList.size();
            log.info("当前导入任务 {} 共有 {} 条数据需要更新", taskId, size);
            for (int i = 0; i < size; i++) {
                ZdPageFormDataDTO needUpdateData = needUpdateList.get(i);

                ZdPageFormDataDTO zdPageFormData = new ZdPageFormDataDTO();
                zdPageFormData.setUpdaterId(needUpdateData.getUpdaterId());

                Long id = needUpdateData.getId();
                updatedIdList.add(id);
                zdPageFormData.setId(id);

                StatusEnum status = needUpdateData.getStatus();
                if (Objects.equals(StatusEnum.DISABLE, status)) {
                    log.info("待更新数据中,暂存第 {} 条数据", i + 1);
                    this.zdPageFormDataService.temporarySaveWithNestedTransactional(needUpdateData);
                } else {
                    log.info("待更新数据中,更新第 {} 条数据", i + 1);
                    this.zdPageFormDataService.saveOrUpdateWithNestedTransactional(needUpdateData);
                }

                needUpdateCreatorIdOrUpdaterIdList.add(zdPageFormData);
            }
        } else {
            log.info("当前导入任务 {} 没有需要更新的数据", taskId);
        }

        if (CollUtil.isNotEmpty(needUpdateCreatorIdOrUpdaterIdList)) {
            this.zdPageFormDataService.updateBatchCreatorIdAndUpdaterId(formCode,
                needUpdateCreatorIdOrUpdaterIdList);
        }
    }

    // 保存错误报告
    private void saveFailureReport(List<Map<String, Object>> failureDataList,
        List<ZdImportFormHeaderFieldDTO> headerFields, List<ZdFormFieldDTO> excelHeader,
        ZdOnceAbsoluteMergeStrategy sheetWriteHandler,
        String importExcelFileName, ZdImportContext ctx) {
        Long taskId = ctx.getTaskId();
        File failureReportExcel = this.generateFailureReportExcel(failureDataList, headerFields,
            excelHeader, sheetWriteHandler);
        UploadFileDTO uploadTempFile = this.uploadCommonUtils.uploadTempFile(
            failureReportExcel);

        String fileNamePrefix = FileUtil.getFileNameWithoutSuffix(importExcelFileName);
        uploadTempFile.setName(String.format("%s_错误报告.xlsx", fileNamePrefix));
        uploadTempFile.setType(FormDataImportConstant.IMPORT_FAILURE_ATTACHMENT_TYPE);

        UploadDTO upload = new UploadDTO();
        upload.setUploadFileList(Collections.singletonList(uploadTempFile));
        upload.setRemoveFileList(Collections.emptyList());
        upload.setType(FormDataImportConstant.IMPORT_FAILURE_ATTACHMENT_TYPE);
        upload.setSourceEntity(FormDataImportConstant.IMPORT_ATTACHMENT_SOURCE_ENTITY);
        upload.setSourceId(taskId);

        this.attachmentExtService.saveAttachmentList(upload, taskId,
            FormDataImportConstant.IMPORT_ATTACHMENT_SOURCE_ENTITY);
    }

    private void appendFailureDataList(List<Map<String, Object>> failureDataList,
        Map<String, Object> parseResult, String errMsg) {
        parseResult.put(ERR_MSG, errMsg);
        failureDataList.add(parseResult);
    }

    // 获取系统管理员
    private String getPlantformAdminOrgIdFromSetting() {
        String adminFromZD = this.settingExtService.getSettingValueByModuleAndName(
            GlobalSettingConstant.DEFAULT_MODULE, GlobalSettingConstant.ADMIN_ORG_ID, null);

        if (StringUtils.isBlank(adminFromZD)) {
            log.debug("智搭平台配置的管理员账户为空,尝试获取配置文件中配置的管理员账户");
            String adminOrgIdFromConfigFile = this.globalProperties.getAdminOrgId();
            if (StringUtils.isBlank(adminOrgIdFromConfigFile)) {
                log.debug("智搭平台配置的管理员账户为空,尝试获取框架配置的管理员账户");
                return this.settingExtService.getSettingValueByModuleAndName(
                    GlobalSettingConstant.COMMON_MODULE, GlobalSettingConstant.ADMINUSER, null);
            } else {
                return adminOrgIdFromConfigFile;
            }
        } else {
            return adminFromZD;
        }
    }

    // 生成错误报告
    // excelHeaderList 包含表头 第一行与第二行
    // headerFields 仅包含第二行或者只包含第一行
    private File generateFailureReportExcel(List<Map<String, Object>> failureDataList,
        List<ZdImportFormHeaderFieldDTO> headerFields,
        List<ZdFormFieldDTO> excelHeaderList,
        ZdOnceAbsoluteMergeStrategy sheetWriteHandler) {

        List<ZdFormFieldDTO> realExcelHeaderList = new LinkedList<>(excelHeaderList);
        realExcelHeaderList.add(ZdFormFieldDTO.builder().ref(ERR_MSG).label(ERR_REASON).build());
        List<List<String>> headList = new ArrayList<>();

        List<String> headRef = new LinkedList<>();
        realExcelHeaderList.forEach(item -> {
            String itemRef = item.getRef();
            List<ZdFormFieldDTO> children = item.getChildren();
            if (CollUtil.isNotEmpty(children)) {
                children.forEach(child -> {
                    String childRef = child.getRef();
                    headRef.add(childRef);
                });
            } else {
                headRef.add(itemRef);
            }
        });

        int headCount = 1;
        for (ZdImportFormHeaderFieldDTO headField : headerFields) {
            String ref = headField.getRef();
            if (headRef.contains(ref)) {
                List<String> head = new ArrayList<>(2);
                String label = headField.getLabel();
                String parentLabel = headField.getParentLabel();
                if (StringUtils.isNotBlank(parentLabel)) {
                    head.add(parentLabel);
                    headCount = 2;
                }
                head.add(label);
                headList.add(head);
            }
        }

        headList.add(Collections.singletonList("错误原因"));

        List<List<Object>> expandedRows = new LinkedList<>();
        int currentFailureDataRowCount = headCount;
        for (int i = 0; i < failureDataList.size(); i++) {
            log.info("处理第 {} 条失败数据", i + 1);
            Map<String, Object> failureData = failureDataList.get(i);
            Integer maxRow = failureData.values().stream().map(item -> {
                if (item instanceof List) {
                    return ((List) item).size();
                } else {
                    return 1;
                }
            }).reduce(Integer::max).get();

            List<List<Object>> expandedList = this.buildExpandedRows(realExcelHeaderList,
                failureData,
                maxRow,
                currentFailureDataRowCount + 1,
                sheetWriteHandler);
            expandedRows.addAll(expandedList);

            currentFailureDataRowCount += maxRow;
        }

        File tempFile = ZdTempFileUtil.createExcelTempFile("import_failure_report_");

        ExcelWriterBuilder write = EasyExcel.write(tempFile);

        write.head(headList);

        ExcelWriterSheetBuilder sheetBuilder = write.sheet("错误报告");
        sheetBuilder.registerWriteHandler(sheetWriteHandler);
        sheetBuilder.doWrite(expandedRows);

        return tempFile;
    }

    private List<List<Object>> buildExpandedRows(List<ZdFormFieldDTO> exportFields,
        Map<String, Object> pageData,
        Integer maxRow,
        int currentDataIndex,
        ZdOnceAbsoluteMergeStrategy sheetWriteHandler) {
        // allRowCount 总行数
        // 总列数
        int allColumnCount = ZdImportAndExportUtil.calAllCoumntCount(exportFields);

        List<List<Object>> expandedRows = new LinkedList<>();
        for (int row = 0; row < maxRow; row++) {
            expandedRows.add(new ArrayList<>(allColumnCount));
        }

        int currentExportFieldIndex = 0;
        for (ZdFormFieldDTO exportField : exportFields) {
            String ref = exportField.getRef();
            List<ZdFormFieldDTO> children = exportField.getChildren();

            if (CollUtil.isNotEmpty(children)) {
                // 获取子表单数据列表，每个元素是一条子表单记录
                List<Map<String, Object>> childFormDataList = (List<Map<String, Object>>) MapUtils.getObject(
                    pageData, ref);

                // 遍历每个字段
                for (int childColumnIndex = 0; childColumnIndex < children.size();
                    childColumnIndex++) {
                    ZdFormFieldDTO child = children.get(childColumnIndex);
                    // 遍历子表单
                    List<Object> columnList = new LinkedList<>();
                    for (int childDataIndex = 0; childDataIndex < childFormDataList.size();
                        childDataIndex++) {
                        // 子表单数据
                        Map<String, Object> item = childFormDataList.get(childDataIndex);

                        int currentChildFormMaxRow = maxRow;

                        String childRef = child.getRef();
                        if (CollUtil.isNotEmpty(childFormDataList)) {
                            Object childFormFieldData = MapUtils.getObject(
                                item, childRef);
                            if (childFormFieldData instanceof List) {
                                columnList.addAll((List<Object>) childFormFieldData);
                            } else {
                                columnList.addAll(
                                    ZdImportAndExportUtil.getFilledList(childFormFieldData,
                                        currentChildFormMaxRow));

                                if (currentChildFormMaxRow > 1) {
                                    ZdExcelMergeInfoDTO excelMergeInfo = new ZdExcelMergeInfoDTO(
                                        currentDataIndex + childDataIndex,
                                        currentDataIndex + childDataIndex + currentChildFormMaxRow
                                            - 1,
                                        childColumnIndex + currentExportFieldIndex,
                                        childColumnIndex + currentExportFieldIndex);
                                    sheetWriteHandler.addMergeInfo(excelMergeInfo);
                                }
                            }
                        }
                    }
                    ZdImportAndExportUtil.fillListWithList(expandedRows, columnList);
                }

                currentExportFieldIndex += children.size();
            } else {
                // 处理非子表单字段（普通字段、附件字段）
                Object value = MapUtils.getObject(pageData, ref);

                if (value instanceof List) {
                    // 附件字段：List类型，需要按行展开
                    List<Object> listValue = (List<Object>) value;
                    ZdImportAndExportUtil.fillListWithList(expandedRows, listValue);
                } else {
                    // 普通字段：单值，在所有行中重复显示
                    ZdImportAndExportUtil.fillList(expandedRows, value, maxRow);

                    if (maxRow > 1) {
                        ZdExcelMergeInfoDTO excelMergeInfo = new ZdExcelMergeInfoDTO(
                            currentDataIndex, currentDataIndex + maxRow - 1,
                            currentExportFieldIndex,
                            currentExportFieldIndex);
                        sheetWriteHandler.addMergeInfo(excelMergeInfo);
                    }
                }
                currentExportFieldIndex++;
            }
        }
        return expandedRows;
    }
}