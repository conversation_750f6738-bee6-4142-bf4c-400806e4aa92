package com.sinitek.sirm.nocode.form.service.impl;

import com.alibaba.excel.util.StringUtils;
import com.sinitek.sirm.framework.frontend.dto.UploadFileDTO;
import com.sinitek.sirm.framework.utils.UploadCommonUtils;
import com.sinitek.sirm.nocode.common.properties.ZdGlobalProperties;
import com.sinitek.sirm.nocode.common.utils.ZdHttpUtil;
import com.sinitek.sirm.nocode.common.utils.ZdTempFileUtil;
import com.sinitek.sirm.nocode.form.constant.ExportAndImportTaskExecutorConstant;
import com.sinitek.sirm.nocode.form.dto.ZdDownloadResourceResultDTO;
import com.sinitek.sirm.nocode.form.service.IZdDownloadService;
import java.io.File;
import java.io.FileOutputStream;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.io.FilenameUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025-07-24 13:14
 */
@Slf4j
@Service
public class ZdDownloadServiceImpl implements IZdDownloadService {

    private static final String ID_IN_RESPONSE = "id";

    @Autowired
    private ZdGlobalProperties globalProperties;

    @Autowired
    private UploadCommonUtils uploadCommonUtils;

    @Async(ExportAndImportTaskExecutorConstant.DEFAULT_DOWNLOAD_RESOURCE_EXECUTOR_NAME)
    @Override
    @SuppressWarnings("unchecked")
    public CompletableFuture<List<ZdDownloadResourceResultDTO>> downloadFromUrl(List<String> urls) {
        return CompletableFuture.completedFuture(
                urls.stream().map(this::doDownloadFromUrl).collect(Collectors.toList()));
    }

    public ZdDownloadResourceResultDTO doDownloadFromUrl(String url) {
        log.info("从url下载资源, url: {}", url);
        File tempFile = ZdTempFileUtil.createTempFile("download-file_",
                String.format(".%s", this.globalProperties.getTempFileExtention()));
        Integer downloadTimeout = this.globalProperties.getDownloadTimeout();
        try (FileOutputStream outputStream = new FileOutputStream(tempFile)) {
            String fileName = ZdHttpUtil.download(url, outputStream, downloadTimeout);

            if (StringUtils.isBlank(fileName)) {
                fileName = FilenameUtils.getName(url);
            }

            if (StringUtils.isBlank(fileName)) {
                log.warn("下载文件后,无法获取文件名, url: {}", url);
                return ZdDownloadResourceResultDTO.builder()
                    .flag(false).downloadUrl(url).uploaderFile(null)
                    .errMes(String.format("无法从链接 %s 获取文件名", url))
                    .build();
            }

            UploadFileDTO uploadFile = this.uploadCommonUtils.uploadTempFile(tempFile);
            Map<String, Object> response = uploadFile.getResponse();
            String idInResponse = MapUtils.getString(response, ID_IN_RESPONSE);
            if (StringUtils.isNotBlank(idInResponse)) {
                uploadFile.setName(fileName);
                return ZdDownloadResourceResultDTO.builder()
                        .flag(true).downloadUrl(url).uploaderFile(uploadFile).build();
            } else {
                log.warn("上传文件后,响应中没有id,上传失败, url: {}", url);
                return ZdDownloadResourceResultDTO.builder()
                    .flag(false).downloadUrl(url).uploaderFile(null)
                    .errMes("保存下载资源失败")
                        .build();
            }
        } catch (Exception e) {
            log.error("从url下载资源失败, url: {}", url, e);
            return ZdDownloadResourceResultDTO.builder()
                .flag(false).downloadUrl(url).uploaderFile(null)
                .errMes("下载失败")
                    .build();
        }
    }
}
