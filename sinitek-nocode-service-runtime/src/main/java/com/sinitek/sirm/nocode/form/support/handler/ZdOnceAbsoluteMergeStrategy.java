package com.sinitek.sirm.nocode.form.support.handler;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import com.sinitek.sirm.nocode.form.dto.ZdExcelMergeInfoDTO;
import io.swagger.annotations.ApiModel;
import java.util.LinkedList;
import java.util.List;
import lombok.Data;
import org.apache.poi.ss.util.CellRangeAddress;

/**
 * <AUTHOR>
 * @date 2025-07-24 09:03
 */
@Data
@ApiModel("智搭单元格合并策略")
public class ZdOnceAbsoluteMergeStrategy implements SheetWriteHandler {

    private List<ZdExcelMergeInfoDTO> mergeInfoList;

    public ZdOnceAbsoluteMergeStrategy() {
        this.mergeInfoList = new LinkedList<>();
    }

    public void addMergeInfo(ZdExcelMergeInfoDTO mergeInfo) {
        this.mergeInfoList.add(mergeInfo);
    }

    @Override
    public void afterSheetCreate(
        WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
        if (CollUtil.isNotEmpty(mergeInfoList)) {
            mergeInfoList.forEach(mergeInfo -> {
                CellRangeAddress cellRangeAddress =
                    new CellRangeAddress(mergeInfo.getFirstRowIndex(), mergeInfo.getLastRowIndex(),
                        mergeInfo.getFirstColumnIndex(),
                        mergeInfo.getLastColumnIndex());
                writeSheetHolder.getSheet().addMergedRegionUnsafe(cellRangeAddress);
            });
        }
    }
}
