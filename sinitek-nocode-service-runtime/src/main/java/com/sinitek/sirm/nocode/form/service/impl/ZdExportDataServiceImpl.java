package com.sinitek.sirm.nocode.form.service.impl;

import com.sinitek.sirm.common.event.support.SiniCubeEventPublisher;
import com.sinitek.sirm.common.utils.JsonUtil;
import com.sinitek.sirm.framework.exception.BussinessException;
import com.sinitek.sirm.nocode.form.constant.FormDataImportOrExportMessageCodeConstant;
import com.sinitek.sirm.nocode.form.dao.ZdExportTaskDAO;
import com.sinitek.sirm.nocode.form.dto.ZdExportTaskEventSourceDTO;
import com.sinitek.sirm.nocode.form.dto.ZdFormDataExportParamDTO;
import com.sinitek.sirm.nocode.form.dto.ZdFormDataExportResultDTO;
import com.sinitek.sirm.nocode.form.dto.ZdFormDataExportRetryDTO;
import com.sinitek.sirm.nocode.form.entity.ZdExportTask;
import com.sinitek.sirm.nocode.form.enumerate.ExportOrImportTaskStatusEnum;
import com.sinitek.sirm.nocode.form.event.ExportTaskCreateEvent;
import com.sinitek.sirm.nocode.form.event.ExportTaskRetryEvent;
import com.sinitek.sirm.nocode.form.service.IZdExportDataService;
import com.sinitek.sirm.nocode.form.service.IZdExportTaskService;
import com.sinitek.sirm.nocode.form.util.ZdExportUtil;
import com.sinitek.sirm.nocode.page.service.IZdPageService;
import java.util.Date;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
public class ZdExportDataServiceImpl implements IZdExportDataService {

    @Autowired
    private ZdExportTaskDAO zdExportTaskDAO;

    @Autowired
    private IZdPageService pageSerVice;

    @Autowired
    private IZdExportTaskService exportTaskService;

    @Autowired
    private SiniCubeEventPublisher eventPublisher;

    /**
     * 根据导出参数生成导出任务
     *
     * <AUTHOR>
     * @version 1.0.0
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ZdFormDataExportResultDTO export(ZdFormDataExportParamDTO param) {
        Date operateTime = param.getOperateTime();
        String formCode = param.getFormCode();
        String formName = this.pageSerVice.getNameByCode(formCode);
        ZdExportTask task = new ZdExportTask();
        String exportExcelName = ZdExportUtil.getExportExcelFileName(formName, operateTime);
        task.setFileName(exportExcelName)
            .setStatus(ExportOrImportTaskStatusEnum.PROCESSING.getValue())
            .setOperatorId(param.getOperatorId())
            .setOperateTime(operateTime)
            .setExportParams(JsonUtil.toJsonString(param))
            .setFormCode(param.getFormCode());
        this.zdExportTaskDAO.save(task);

        Long taskId = task.getId();

        // 发布事件
        ZdExportTaskEventSourceDTO source = ZdExportTaskEventSourceDTO
            .builder()
            .taskId(taskId)
            .requestHost(param.getRequestHost())
            .accessToken(param.getAccessToken())
            .build();
        this.eventPublisher.publishEvent(new ExportTaskCreateEvent(source));

        return ZdFormDataExportResultDTO.builder().taskId(taskId).fileName(exportExcelName)
            .build();
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void retry(ZdFormDataExportRetryDTO param) {
        Long taskId = param.getTaskId();

        ZdExportTask task = this.zdExportTaskDAO.getById(taskId);
        if (Objects.isNull(task)) {
            log.warn("导出任务[{}]不存在", taskId);
            throw new BussinessException(
                FormDataImportOrExportMessageCodeConstant.EXPORT_TASK_NOT_EXIST);
        }

        String operatorId = param.getOperatorId();
        Date operateTime = param.getOperateTime();

        if (Objects.equals(task.getStatus(), ExportOrImportTaskStatusEnum.PROCESSING.getValue())) {
            log.warn("导出任务[{}]正在处理,不在继续处理", taskId);
            throw new BussinessException(
                FormDataImportOrExportMessageCodeConstant.EXPORT_TASK_PROCESSING);
        }
        String formCode = task.getFormCode();
        String formName = this.pageSerVice.getNameByCode(formCode);
        String exportExcelName = ZdExportUtil.getExportExcelFileName(formName, operateTime);
        
        task.setFileName(exportExcelName);
        task.setStatus(ExportOrImportTaskStatusEnum.PROCESSING.getValue());
        task.setOperatorId(operatorId);
        task.setOperateTime(operateTime);
        task.setFileCount(null);
        task.setErrorMsg(null);
        this.zdExportTaskDAO.updateById(task);

        log.info("重新执行导出任务，taskId:{}, operatorId:{}，operateTime:{}", taskId,
            operatorId,
            operateTime);

        // 发布事件
        ZdExportTaskEventSourceDTO source = ZdExportTaskEventSourceDTO
            .builder()
            .taskId(taskId)
            .requestHost(param.getRequestHost())
            .accessToken(param.getAccessToken())
            .build();
        this.eventPublisher.publishEvent(new ExportTaskRetryEvent(source));
    }
}
