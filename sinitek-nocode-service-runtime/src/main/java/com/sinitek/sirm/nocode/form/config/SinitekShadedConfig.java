package com.sinitek.sirm.nocode.form.config;

import com.sinitek.sirm.common.action.service.IActionExService;
import com.sinitek.sirm.nocode.form.service.impl.ActionExServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 导出任务线程池配置
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
@Configuration
public class SinitekShadedConfig {

    /**
     * 避免框架报错
     */
    @Bean
    @ConditionalOnMissingBean
    public IActionExService registShadedActionExService() {
        log.info("注册空的 IActionExService 实现");
        return new ActionExServiceImpl();
    }
}
