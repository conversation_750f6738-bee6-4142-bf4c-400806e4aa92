package com.sinitek.sirm.nocode.form.support.ctx;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.sinitek.sirm.common.attachment.dto.AttachmentDTO;
import com.sinitek.sirm.common.attachment.service.IAttachmentExtService;
import com.sinitek.sirm.nocode.form.util.ZdExportUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2025-07-11 11:23
 */
@Data
@Accessors(chain = true)
@ApiModel("数据导出上下文")
@EqualsAndHashCode
public class ZdFormDataExportContext {

    @ApiModelProperty(value = "表单code")
    private String formCode;

    @ApiModelProperty(value = "导出实例")
    private Boolean exportId;

    @ApiModelProperty(value = "导出附件标识")
    private Boolean exportAttachment;

    @ApiModelProperty(value = "用户token")
    private String accessToken;

    @ApiModelProperty(value = "请求地址(用于拼接下载地址)")
    private String requestHost;

    @ApiModelProperty(value = "附件缓存")
    private Map<String, List<AttachmentDTO>> attachmentsCache;

    @ApiModelProperty(value = "附件与主表单数据实例ID缓存")
    private Map<String, Long> attachmentsIdCache;

    @ApiModelProperty(value = "附件子表单数据实例ID缓存")
    private Map<String, String> attachmentsChildIdCache;

    @JsonIgnore
    @ApiModelProperty(value = "附件实例")
    private IAttachmentExtService attachmentExtService;

    public String getDirectConcatRequestHost() {
        if (requestHost.endsWith("/")) {
            return requestHost.substring(0, requestHost.length() - 1);
        } else {
            return requestHost;
        }
    }

    public boolean isExportId() {
        return Boolean.TRUE.equals(this.exportId);
    }

    public boolean isExportAttachment() {
        return Boolean.TRUE.equals(this.exportAttachment);
    }

    public ZdFormDataExportContext() {
        this.attachmentsCache = new HashMap<>();
        this.attachmentsIdCache = new HashMap<>();
        this.attachmentsChildIdCache = new HashMap<>();
    }

    public void putAttachments(Long sourceId, String sourceEntity, Integer type,
        List<AttachmentDTO> attachments) {
        String key = ZdExportUtil.getAttachmentCacheKey(sourceId, sourceEntity, type);
        this.attachmentsCache.put(key, attachments);
    }

    public void putAttachmentsId(Long sourceId, String sourceEntity, Integer type, Long id, String childId) {
        String key = ZdExportUtil.getAttachmentCacheKey(sourceId, sourceEntity, type);
        this.attachmentsIdCache.put(key, id);
        this.attachmentsChildIdCache.put(key, childId);
    }

    public List<AttachmentDTO> getAttachments(Long sourceId, String sourceEntity, Integer type) {
        String key = ZdExportUtil.getAttachmentCacheKey(sourceId, sourceEntity, type);
        return this.attachmentsCache.get(key);
    }

    public List<AttachmentDTO> getAllAttachments() {
        return this.attachmentsCache.values().stream().flatMap(Collection::stream).collect(
                Collectors.toList());
    }
}
