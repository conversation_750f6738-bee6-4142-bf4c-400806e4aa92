package com.sinitek.sirm.nocode.form.event;

import com.sinitek.sirm.nocode.form.dto.ZdExportTaskEventSourceDTO;
import io.swagger.annotations.ApiModel;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2025-07-08 13:38
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "导出任务创建事件")
public class ExportTaskCreateEvent extends AbstraceZdExportTaskBaseEvent {

    public ExportTaskCreateEvent(ZdExportTaskEventSourceDTO source) {
        super(source);
    }
    
}
