package com.sinitek.sirm.nocode.form.event;

import com.sinitek.sirm.nocode.form.dto.ZdImportTaskEventSourceDTO;
import io.swagger.annotations.ApiModel;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2025-07-08 13:38
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "导入任务重试事件")
public class ImportTaskRetryEvent extends AbstraceZdImportTaskBaseEvent {

    public ImportTaskRetryEvent(ZdImportTaskEventSourceDTO source) {
        super(source);
    }

}
