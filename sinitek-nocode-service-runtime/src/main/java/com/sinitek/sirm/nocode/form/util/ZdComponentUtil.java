package com.sinitek.sirm.nocode.form.util;

import com.sinitek.sirm.nocode.form.enumerate.PageDataComponentTypeEnum;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2025-07-16 15:30
 */
@Slf4j
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class ZdComponentUtil {

    public static boolean isContainComponent(String componentName) {
        return PageDataComponentTypeEnum.ZD_GRID.getValue().equals(componentName)
            || PageDataComponentTypeEnum.ZD_GRID_ITEM.getValue().equals(componentName)
            || PageDataComponentTypeEnum.ZD_FORM.getValue().equals(componentName)
            || PageDataComponentTypeEnum.ZD_PAGE.getValue().equals(componentName);
    }

    public static boolean isChildFormComponent(String componentName) {
        return PageDataComponentTypeEnum.ZD_CHILD_FORM.getValue().equals(componentName);
    }
}
