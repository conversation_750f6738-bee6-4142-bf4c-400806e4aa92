package com.sinitek.sirm.nocode.form.util;

import cn.hutool.core.date.DateUtil;
import java.util.Date;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2025-07-08 16:37
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class ZdExportUtil {

    public static String getExportExcelFileName(String formName, Date opTime) {
        return String.format("%s_导出数据_%s.xlsx", formName,
            DateUtil.format(opTime, "yyyyMMddHHmmss"));
    }

    public static String getExportResourceZipFileName(String formName, Date operationTime,
        Integer fileIndex, int allFileChunkSize) {
        if (allFileChunkSize > 1) {
            return String.format("%s_导出文件_%s_%s.zip", formName, fileIndex,
                DateUtil.format(operationTime, "yyyyMMddHHmmss"));
        }
        return String.format("%s_导出文件_%s.zip", formName,
            DateUtil.format(operationTime, "yyyyMMddHHmmss"));
    }

    public static String getAttachmentCacheKey(Long sourceId, String sourceEntity, Integer type) {
        return String.format("%s-%s-%s", sourceId, sourceEntity, type);
    }

    public static String getFileHyperlink(Long id, String childId, Integer fileType,
        String fileName,
            String fileTypeName) {
        if (StringUtils.isNotBlank(fileTypeName)) {
            fileName = String.format("%s.%s", fileName, fileTypeName);
        }
        if (StringUtils.isNotBlank(childId)) {
            return String.format("%s/%s/%s/%s", id, childId, fileType, fileName);
        } else {
            return String.format("%s/%s/%s", id, fileType, fileName);
        }
    }
}
