package com.sinitek.sirm.nocode.form.support.listener;

import static com.sinitek.sirm.nocode.form.constant.FormDataImportOrExportMessageCodeConstant.EXCEL_DATA_IS_EMPTY;
import static com.sinitek.sirm.nocode.form.constant.FormDataImportOrExportMessageCodeConstant.EXCEL_HEAD_IS_EMPTY;
import static com.sinitek.sirm.nocode.form.constant.FormDataImportOrExportMessageCodeConstant.EXCEL_IMPORT_DATA_IS_EMPTY;
import static com.sinitek.sirm.nocode.form.constant.FormDataImportOrExportMessageCodeConstant.EXCEL_PARSE_FAILED;
import static com.sinitek.sirm.nocode.form.constant.FormDataImportOrExportMessageCodeConstant.EXCEL_SUB_FORM_MERGE_DATA_NOT_ALLOWED;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.enums.CellExtraTypeEnum;
import com.alibaba.excel.metadata.CellExtra;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.read.metadata.holder.ReadSheetHolder;
import com.sinitek.sirm.common.utils.JsonUtil;
import com.sinitek.sirm.framework.exception.BussinessException;
import com.sinitek.sirm.nocode.form.dto.ZdFormFieldDTO;
import com.sinitek.sirm.nocode.form.dto.ZdImportFormHeaderFieldDTO;
import com.sinitek.sirm.nocode.form.dto.ZdPreParseFirstRowDTO;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

/**
 * 预解析
 *
 * <AUTHOR>
 * @date 2025-07-17 16:13
 */
@Slf4j
public class ImportExcelPreParseListener implements ReadListener<Map<Integer, Object>> {

    // 导入数据最大条数 2表头+5000条数据
    private static final int MAX_ROW_COUNT = 5002;

    // 预解析默认条数
    private static final int PRE_PARSE_COUNT = 12;

    // 表格原始数据
    private Map<Integer, Map<Integer, Object>> tableData;

    // 合并信息
    private Map<Integer, Map<Integer, CellExtra>> cellExtraData;

    // 表头名称与ref映射
    // key: labelUK
    private Map<String, String> headNameAndRefMap;

    // 表头名称与父级ref映射
    // key: labelUK
    private Map<String, String> headNameAndParentRefMap;

    // 表头
    private Integer headRowCount;

    // 列号与ref
    private Map<Integer, String> columnIndexAndRefMap;

    // 列号与父级ref映射（只有子表单中列才会有该数据）
    private Map<Integer, String> columnIndexAndParentRefMap;

    // 列号与label映射
    private Map<Integer, String> columnIndexAndLabelMap;

    // 列号与父级label映射
    private Map<Integer, String> columnIndexAndParentLabelMap;

    // 解析结果
    private List<Map<String, Object>> parseResultList;

    // 表头
    private List<ZdImportFormHeaderFieldDTO> fields;

    // ref与组件名称map
    private Map<String, ZdImportFormHeaderFieldDTO> refAndFieldMap;

    // 预解析条数
    private Integer preParseCount;

    // 表头
    private List<ZdFormFieldDTO> headers;

    // 预解析第一行数据
    private List<ZdPreParseFirstRowDTO> firstRowDataList;

    public ImportExcelPreParseListener(List<ZdImportFormHeaderFieldDTO> fields) {
        this(fields, PRE_PARSE_COUNT);
    }

    public ImportExcelPreParseListener(List<ZdImportFormHeaderFieldDTO> fields,
        int preParseCount) {
        this.tableData = new HashMap<>(MAX_ROW_COUNT);
        int fieldSize = fields.size();
        this.headNameAndRefMap = new HashMap<>(fieldSize);
        this.headNameAndParentRefMap = new HashMap<>(fieldSize);
        this.refAndFieldMap = new HashMap<>(fieldSize);
        this.cellExtraData = new HashMap<>(MAX_ROW_COUNT);
        this.fields = fields;
        this.fields.forEach(field -> {
            String label = field.getLabel();
            String parentLabel = field.getParentLabel();
            String parentRef = field.getParentRef();
            String ref = field.getRef();
            String labelUK = this.getLabelUK(label, parentLabel);
            this.headNameAndRefMap.put(labelUK, ref);
            this.headNameAndParentRefMap.put(labelUK, parentRef);
            this.refAndFieldMap.put(ref, field);
        });
        this.preParseCount = preParseCount;
    }


    @Override
    public void invokeHead(Map<Integer, ReadCellData<?>> headMap, AnalysisContext context) {
        ReadSheetHolder readSheetHolder = context.readSheetHolder();
        Integer rowIndex = readSheetHolder.getRowIndex();

        headMap.forEach((columnIndex, cellData) -> {
            String value = cellData.getStringValue();
            this.putTableData(rowIndex, columnIndex, value);
        });
    }

    @Override
    public void invoke(Map<Integer, Object> data, AnalysisContext context) {
        if (this.preParseCount <= this.tableData.size()) {
            log.debug("预解析条数 {} 达到上限,不再继续解析", this.tableData.size());
            return;
        }
        ReadSheetHolder readSheetHolder = context.readSheetHolder();
        Integer rowIndex = readSheetHolder.getRowIndex();
        data.forEach(
            (columnIndex, value) -> this.putTableData(rowIndex, columnIndex, value));
    }

    @Override
    public void onException(Exception exception, AnalysisContext context) throws Exception {
        if (exception instanceof BussinessException) {
            throw exception;
        } else {
            ReadSheetHolder readSheetHolder = context.readSheetHolder();
            if (Objects.nonNull(readSheetHolder)) {
                Integer rowIndex = readSheetHolder.getRowIndex();
                String sheetName = readSheetHolder.getSheetName();
                log.error("Excel解析失败, sheetName: {}, rowIndex: {}", sheetName, rowIndex,
                    exception);
            } else {
                log.error("Excel解析失败", exception);
            }
            throw new BussinessException(EXCEL_PARSE_FAILED);
        }

    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        if (CollUtil.isEmpty(this.tableData)) {
            log.error("Excel数据为空,解析失败不再继续");
            throw new BussinessException(EXCEL_DATA_IS_EMPTY);
        }

        // 确定表头
        this.buildHead();

        // 构建表头数据
        this.parseExcelHeaderData();

        // 构建数据
        int dataCount = this.getDataCount();
        log.debug("表头行数: {},共有 {} 行数据", this.headRowCount, dataCount);
        if (dataCount < 1) {
            log.error("Excel中导入数据为空,解析失败不再继续");
            throw new BussinessException(EXCEL_IMPORT_DATA_IS_EMPTY);
        }

        this.parseResultList = new LinkedList<>();

        // 子表单合并行数,每执行一行数据减1
        int mergedCount = 0;
        // 当前子表单合并行数,数据一直不变
        int maxMergedCount = 0;
        for (Entry<Integer, Map<Integer, Object>> tableDataEntry : this.tableData.entrySet()) {
            int rowIndex = tableDataEntry.getKey();
            if (rowIndex < this.headRowCount) {
                log.debug("第 {} 行为表头,跳过处理", rowIndex);
                continue;
            }
            Map<Integer, Object> columnAndDataMap = tableDataEntry.getValue();
            Map<Integer, CellExtra> columnAndExtraMap = this.cellExtraData.get(rowIndex);
            if (Objects.nonNull(columnAndDataMap)) {
                if (CollUtil.isEmpty(columnAndExtraMap) && mergedCount == 0) {
                    // 当前行没有合并数据
                    Map<String, Object> rowData = new HashMap<>(columnAndDataMap.size());
                    for (Entry<Integer, Object> entry : columnAndDataMap.entrySet()) {
                        Integer columnIndex = entry.getKey();
                        Object value = entry.getValue();
                        String ref = this.columnIndexAndRefMap.get(columnIndex);
                        if (StringUtils.isBlank(ref)) {
                            log.warn("第 {} 行,第 {} 列,数据没有对应的ref", rowIndex,
                                columnIndex);
                        }
                        rowData.put(String.valueOf(columnIndex), value);
                    }
                    this.parseResultList.add(rowData);
                } else {
                    // 存在合并数据
                    Map<Integer, CellExtra> columnAndExtraDataMap = this.cellExtraData.get(
                        rowIndex);
                    int allColumnCount = columnAndDataMap.size();
                    Map<String, Object> rowData = new HashMap<>(allColumnCount);
                    boolean isInMergedRow = mergedCount > 0;
                    if (isInMergedRow) {
                        log.debug("第 {} 行数据为子表单第 {} 条数据", rowIndex,
                            maxMergedCount - mergedCount + 1);
                    }

                    for (Entry<Integer, Object> entry : columnAndDataMap.entrySet()) {
                        Integer columnIndex = entry.getKey();
                        Object data = entry.getValue();
                        String ref = this.columnIndexAndRefMap.get(columnIndex);

                        if (StringUtils.isBlank(ref)) {
                            log.warn("第 {} 行,第 {} 列,数据没有对应的ref,跳过处理", rowIndex,
                                columnIndex);
                            continue;
                        }

                        CellExtra cellExtra = null;
                        if (CollUtil.isNotEmpty(columnAndExtraDataMap)) {
                            cellExtra = columnAndExtraDataMap.get(columnIndex);
                        }
                        String parentLabel = this.columnIndexAndParentLabelMap.get(columnIndex);
                        if (StringUtils.isNotBlank(parentLabel) && Objects.nonNull(cellExtra)) {
                            log.error("首行首列编号从1开始,第 {} 行,第 {} 列,子表单不允许合并数据",
                                rowIndex + 1, columnIndex + 1);
                            throw new BussinessException(EXCEL_SUB_FORM_MERGE_DATA_NOT_ALLOWED,
                                rowIndex + 1, columnIndex + 1);
                        }

                        if (Objects.nonNull(cellExtra)) {
                            // 主表单中数据
                            // 子表单中不存在合并信息
                            Integer firstRowIndex = cellExtra.getFirstRowIndex();
                            Integer lastRowIndex = cellExtra.getLastRowIndex();
                            int mergeCount = lastRowIndex - firstRowIndex + 1;
                            mergedCount = Math.max(mergeCount, mergedCount);
                            maxMergedCount = mergedCount;
                            rowData.put(String.valueOf(columnIndex), data);
                        } else {
                            // 子表单中数据
                            // 或者非最左上角的主表单数据
                            String parentRef = this.columnIndexAndParentRefMap.get(columnIndex);
                            if (StringUtils.isBlank(parentRef)) {
                                if (isInMergedRow) {
                                    log.debug("当前行 {},列 {} 为合并列中非第一行",
                                        rowIndex,
                                        columnIndex);
                                    // 合并列中非第一行，需要取第一行的数据
                                    Map<Integer, Object> firstRowData = this.tableData.get(
                                        rowIndex);
                                    Object firstRowValue = firstRowData.get(columnIndex);
                                    rowData.put(String.valueOf(columnIndex), firstRowValue);
                                } else {
                                    log.error(
                                        "第[{}]行[{}]列没有对应的父级ref,表单信息与Excel不匹配,columnIndexAndParentRefMap: {}",
                                        rowIndex, columnIndex,
                                        JsonUtil.toJsonString(this.columnIndexAndParentRefMap));
                                }
                            } else {
                                rowData.put(String.valueOf(columnIndex), data);
                            }
                        }
                    }

                    mergedCount--;
                    this.parseResultList.add(rowData);

                }
            } else {
                // 如果当前行所有数据被合并,会出现这种情况
                log.warn("第 {} 行没有数据,已忽略处理", rowIndex);
            }
        }

        log.debug("解析数据 {} 条", this.parseResultList.size());
    }

    public Map<Integer, ?> getCellExtraData() {
        return this.cellExtraData;
    }

    public List<ZdFormFieldDTO> findExcelHeader() {
        return this.headers;
    }

    private void parseExcelHeaderData() {
        List<ZdPreParseFirstRowDTO> firstRows = new LinkedList<>();
        List<ZdFormFieldDTO> list = new LinkedList<>();
        int headCount = this.columnIndexAndLabelMap.size();
        Map<String, ZdFormFieldDTO> parentRefAndFieldMap = new HashMap<>(headCount);
        // 单行表头
        int index = 0;
        for (int columnIndex = 0; columnIndex < headCount; columnIndex++) {
            String label = this.columnIndexAndLabelMap.get(columnIndex);
            String parentLabel = this.columnIndexAndParentLabelMap.get(columnIndex);
            String labelUK = this.getLabelUK(label, parentLabel);
            String ref = this.headNameAndRefMap.get(labelUK);
            String parentRef = this.headNameAndParentRefMap.get(labelUK);
            if (StringUtils.isNotBlank(parentRef)) {
                ZdFormFieldDTO parentField = parentRefAndFieldMap.get(parentRef);
                if (Objects.isNull(parentField)) {
                    parentField = new ZdFormFieldDTO();
                    parentRefAndFieldMap.put(parentRef, parentField);

                    parentField.setRef(parentRef);
                    parentField.setLabel(parentLabel);
                    parentField.setChildren(new LinkedList<>());

                    list.add(parentField);
                }
                List<ZdFormFieldDTO> children = parentField.getChildren();
                children.add(
                    ZdFormFieldDTO.builder().ref(String.valueOf(index++)).label(label).build());
                firstRows.add(ZdPreParseFirstRowDTO.builder()
                    .ref(ref)
                    .label(label)
                    .parentRef(parentRef)
                    .parentLabel(parentLabel)
                    .build());
            } else {
                list.add(
                    ZdFormFieldDTO.builder().ref(String.valueOf(index++)).label(label).build());
                firstRows.add(ZdPreParseFirstRowDTO.builder()
                    .ref(ref)
                    .label(label)
                    .parentRef(parentRef)
                    .parentLabel(parentLabel)
                    .build());
            }
        }
        this.headers = list;
        this.firstRowDataList = firstRows;
    }

    public int getHeadCount() {
        return this.headRowCount;
    }

    public int getDataCount() {
        return this.tableData.size() - this.headRowCount;
    }

    public List<Map<String, Object>> findParseResultList() {
        return this.parseResultList;
    }

    public List<Map<String, Object>> findFirstRowDataList() {
        Map<String, Object> result = new HashMap<>(this.firstRowDataList.size());
        for (int i = 0; i < this.firstRowDataList.size(); i++) {
            result.put(String.valueOf(i), this.firstRowDataList.get(i));
        }
        return Collections.singletonList(result);
    }

    @Override
    public void extra(CellExtra extra, AnalysisContext context) {
        if (Objects.equals(extra.getType(), CellExtraTypeEnum.MERGE)) {
            Integer firstRowIndex = extra.getFirstRowIndex();
            if (firstRowIndex > this.preParseCount) {
                return;
            }
            this.putExtraData(firstRowIndex, extra.getFirstColumnIndex(), extra);
        }
    }

    private void buildHead() {
        Map<Integer, Object> columnAndValueMap = this.tableData.get(0);
        int allColumnCount = columnAndValueMap.size();

        this.columnIndexAndRefMap = new HashMap<>(allColumnCount);
        this.columnIndexAndParentLabelMap = new HashMap<>(allColumnCount);
        this.columnIndexAndParentRefMap = new HashMap<>(allColumnCount);
        this.columnIndexAndLabelMap = new HashMap<>(allColumnCount);
        Map<Integer, CellExtra> headMergeData = this.cellExtraData.get(0);
        boolean hasHeadMerge = CollUtil.isNotEmpty(headMergeData);
        if (hasHeadMerge) {
            // 处理表头合并
            for (int columnIndex = 0; columnIndex < allColumnCount; columnIndex++) {
                // 当前列合并数据
                CellExtra currentColumnCellExtra = headMergeData.get(columnIndex);

                if (Objects.nonNull(currentColumnCellExtra)) {
                    Integer firstColumnIndex = currentColumnCellExtra.getFirstColumnIndex();
                    Integer lastColumnIndex = currentColumnCellExtra.getLastColumnIndex();

                    if (Objects.equals(firstColumnIndex, lastColumnIndex)) {
                        // 列合并
                        // 第一行数据
                        String label = this.getStringCellValue(0, columnIndex);
                        this.checkHead(1, columnIndex, label);
                        String labelUK = this.getLabelUK(label, null);
                        String ref = this.headNameAndRefMap.get(labelUK);
                        this.columnIndexAndRefMap.put(columnIndex, ref);
                        this.columnIndexAndLabelMap.put(columnIndex, label);
                    } else {
                        // 行合并
                        // 子表单中的第一列
                        boolean isFirmColumnInMergedRow = Objects.equals(columnIndex,
                            firstColumnIndex);
                        if (isFirmColumnInMergedRow) {
                            String firstRowLabel = this.getStringCellValue(0,
                                firstColumnIndex);
                            for (int i = firstColumnIndex; i <= lastColumnIndex; i++) {
                                this.columnIndexAndParentLabelMap.put(i, firstRowLabel);
                            }
                        }
                        String parentLabel = this.getStringCellValue(0, columnIndex);
                        if (StringUtils.isBlank(parentLabel)) {
                            parentLabel = this.columnIndexAndParentLabelMap.get(columnIndex);
                        }

                        String label = this.getStringCellValue(1, columnIndex);
                        this.checkHead(2, columnIndex, label);
                        String labelUK = this.getLabelUK(label, parentLabel);
                        String ref = this.headNameAndRefMap.get(labelUK);
                        this.columnIndexAndRefMap.put(columnIndex, ref);
                        this.columnIndexAndLabelMap.put(columnIndex, label);

                        String parentRef = this.headNameAndParentRefMap.get(labelUK);
                        this.columnIndexAndParentRefMap.put(columnIndex, parentRef);
                    }
                } else {
                    // 子表单中的非第一列
                    String parentLabel = this.columnIndexAndParentLabelMap.get(columnIndex);
                    if (StringUtils.isBlank(parentLabel)) {
                        // 可能是子表单下只有一列
                        parentLabel = this.getStringCellValue(0, columnIndex);
                        this.columnIndexAndParentLabelMap.put(columnIndex, parentLabel);
                    }
                    String label = this.getStringCellValue(1, columnIndex);
                    this.checkHead(2, columnIndex, label);
                    String labelUK = this.getLabelUK(label, parentLabel);
                    String ref = this.headNameAndRefMap.get(labelUK);
                    this.columnIndexAndRefMap.put(columnIndex, ref);
                    this.columnIndexAndLabelMap.put(columnIndex, label);

                    String parentRef = this.headNameAndParentRefMap.get(labelUK);
                    this.columnIndexAndParentRefMap.put(columnIndex, parentRef);
                }
            }

            this.headRowCount = 2;
        } else {
            // 表头不合并，表头只有一行
            columnAndValueMap.forEach((columnIndex, value) -> {
                String label = null;
                if (Objects.nonNull(value)) {
                    label = String.valueOf(value);
                }
                this.checkHead(1, columnIndex, label);
                String labelUK = this.getLabelUK(label, null);
                String ref = this.headNameAndRefMap.get(labelUK);
                this.columnIndexAndRefMap.put(columnIndex, ref);
                this.columnIndexAndLabelMap.put(columnIndex, label);
            });

            // 单行表头
            this.headRowCount = 1;
        }
    }

    private void checkHead(int rowIndex, int columnIndex, String value) {
        if (StringUtils.isBlank(value)) {
            log.error("表头第[{}]行,第[{}]列存在空值", rowIndex, columnIndex);
            throw new BussinessException(EXCEL_HEAD_IS_EMPTY);
        }
    }

    private void putTableData(Integer rowIndex, Integer columnIndex, Object value) {
        Map<Integer, Object> row = MapUtils.getObject(this.tableData, rowIndex);
        if (Objects.isNull(row)) {
            row = new HashMap<>();
            this.tableData.put(rowIndex, row);
        }
        row.put(columnIndex, value);
    }

    private void putExtraData(Integer rowIndex, Integer columnIndex, CellExtra value) {
        Map<Integer, CellExtra> row = MapUtils.getObject(this.cellExtraData, rowIndex);
        if (Objects.isNull(row)) {
            row = new HashMap<>();
            this.cellExtraData.put(rowIndex, row);
        }
        row.put(columnIndex, value);
    }

    private String getLabelUK(String label, String parentLabel) {
        if (StringUtils.isNotBlank(parentLabel)) {
            return String.format("%s@%s", label, parentLabel);
        }
        return label;
    }

    private String getStringCellValue(Integer rowIndex, Integer columnIndex) {
        Map<Integer, Object> row = MapUtils.getObject(this.tableData, rowIndex);
        if (Objects.isNull(row)) {
            return null;
        }
        Object value = row.get(columnIndex);
        if (Objects.isNull(value)) {
            return null;
        }
        return String.valueOf(value);
    }
}
