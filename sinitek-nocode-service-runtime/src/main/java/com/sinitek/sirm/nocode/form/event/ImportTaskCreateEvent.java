package com.sinitek.sirm.nocode.form.event;

import com.sinitek.sirm.nocode.form.dto.ZdImportTaskEventSourceDTO;
import io.swagger.annotations.ApiModel;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2025-07-08 13:38
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "导入任务创建事件")
public class ImportTaskCreateEvent extends AbstraceZdImportTaskBaseEvent {

    public ImportTaskCreateEvent(ZdImportTaskEventSourceDTO source) {
        super(source);
    }

}
