package com.sinitek.sirm.nocode.form.dto;

import static com.sinitek.sirm.nocode.form.constant.ComponentFieldConstant.MULTIPLE_IN_PROPS;

import cn.hutool.core.collection.CollUtil;
import com.sinitek.sirm.nocode.form.constant.ComponentFieldConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.apache.commons.collections4.MapUtils;

/**
 * <AUTHOR>
 * @date 2025-07-22 13:30
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "页面组件")
public class ZdPageComponentDTO {

    @ApiModelProperty(value = "组件的唯一标识")
    private String ref;

    @ApiModelProperty(value = "组件标题")
    private String label;

    @ApiModelProperty(value = "组件类型")
    private String componentName;

    @ApiModelProperty(value = "组件属性")
    private Map<String, Object> props;

    @ApiModelProperty(value = "子组件")
    private List<ZdPageComponentDTO> children;

    public boolean getRequiredFlag() {
        if (CollUtil.isNotEmpty(this.props)) {
            Map<String, Object> props = this.getProps();
            List<Map<String, Object>> rules = (List<Map<String, Object>>) props.get(
                ComponentFieldConstant.RULES_IN_PROPS);
            if (CollUtil.isNotEmpty(rules)) {
                for (Map<String, Object> rule : rules) {
                    String key = MapUtils.getString(rule, ComponentFieldConstant.RULE_KEY);
                    if (Objects.equals(key, ComponentFieldConstant.REQUIRED_RULE_KEY)) {
                        return MapUtils.getBooleanValue(rule,
                            ComponentFieldConstant.ENABLE_RULE_KEY);
                    }
                }
            }
        }
        return false;
    }

    public String getFormatter() {
        if (CollUtil.isNotEmpty(this.props)) {
            return MapUtils.getString(props, ComponentFieldConstant.FORMATTER_IN_PROPS);
        }
        return null;
    }

    public boolean getMultipleFlag() {
        Map<String, Object> props = this.getProps();
        return MapUtils.getBooleanValue(props, MULTIPLE_IN_PROPS, false);
    }

    public Map<String, Object> getLabelAndValueOptionMap() {
        Map<String, Object> props = this.getProps();
        if (CollUtil.isNotEmpty(props)) {
            List<Map<String, Object>> list = (List<Map<String, Object>>) MapUtils.getObject(props,
                ComponentFieldConstant.OPTIONS_IN_PROPS);
            Map<String, Object> optionMap = new HashMap<>(list.size());
            list.forEach(item -> {
                String label = MapUtils.getString(item, ComponentFieldConstant.LABEL_IN_OPTIONS);
                Object value = MapUtils.getObject(item, ComponentFieldConstant.VALUE_IN_OPTIONS);
                optionMap.put(label, value);
            });
            return optionMap;
        }
        return Collections.emptyMap();
    }

    public Map<String, String> getValueAndLableOptionMap() {
        Map<String, Object> props = this.getProps();
        if (CollUtil.isNotEmpty(props)) {
            List<Map<String, Object>> list = (List<Map<String, Object>>) MapUtils.getObject(props,
                ComponentFieldConstant.OPTIONS_IN_PROPS);
            Map<String, String> optionMap = new HashMap<>(list.size());
            list.forEach(item -> {
                String value = MapUtils.getString(item, ComponentFieldConstant.VALUE_IN_OPTIONS);
                String label = MapUtils.getString(item, ComponentFieldConstant.LABEL_IN_OPTIONS);
                optionMap.put(value, label);
            });
            return optionMap;
        }
        return Collections.emptyMap();
    }

    public String getFormCodeInAssociationForm() {
        Map<String, Object> props = this.getProps();
        if (CollUtil.isNotEmpty(props)) {
            Map<String, Object> associationForm = (Map<String, Object>) MapUtils.getMap(props,
                ComponentFieldConstant.ASSOCIATION_FORM_KEY);
            if (CollUtil.isNotEmpty(associationForm)) {
                return MapUtils.getString(associationForm,
                    ComponentFieldConstant.ASSOCIATION_FORM_FORM_CODE_KEY);
            }
        }
        return null;
    }


}
