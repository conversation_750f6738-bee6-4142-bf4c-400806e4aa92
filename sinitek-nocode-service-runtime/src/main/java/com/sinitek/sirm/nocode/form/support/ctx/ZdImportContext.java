package com.sinitek.sirm.nocode.form.support.ctx;

import com.sinitek.sirm.enumerate.CommonBooleanEnum;
import com.sinitek.sirm.nocode.form.dto.ZdDownloadResourceResultDTO;
import com.sinitek.sirm.nocode.form.dto.ZdImportFormHeaderFieldDTO;
import com.sinitek.sirm.nocode.form.dto.ZdPageComponentDTO;
import com.sinitek.sirm.nocode.form.dto.ZdPageFormDataDTO;
import com.sinitek.sirm.org.dto.OrgObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.Data;
import reactor.util.function.Tuple2;
import reactor.util.function.Tuples;

/**
 * <AUTHOR>
 * @date 2025-07-24 09:24
 */
@Data
@ApiModel("智搭导入上下文")
public class ZdImportContext {

    private static final String DEFAULT_SPLIT_STR = "@";

    private static final int DEFAULT_INIT_SIZE = 10000;

    @ApiModelProperty("表单编码")
    private String formCode;

    @ApiModelProperty("导入任务ID")
    private Long taskId;

    @ApiModelProperty("导入总数据")
    private Integer totalSize;

    @ApiModelProperty("导入模式")
    private Integer importMode;

    @ApiModelProperty("字段校验标识")
    private Integer fieldValidFlag;

    @ApiModelProperty("导入数据提交人模式")
    private Boolean useExcelCreatorFieldFlag;

    @ApiModelProperty("ref与组件数据映射")
    private Map<String, ZdPageComponentDTO> refAndComponentMap;

    @ApiModelProperty("需要字段校验的ref与字段数据映射")
    private Map<String, ZdImportFormHeaderFieldDTO> needValidateFieldMap;

    @ApiModelProperty("表单编码关联数据ID与存在标识映射")
    private Map<String, Boolean> formCodeAssociationDataIdAndFlagMap;

    @ApiModelProperty("失败数据")
    private List<Map<String, Object>> failureDataList;

    @ApiModelProperty("员工姓名与orgId映射")
    private Map<String, List<OrgObjectDTO>> orgNameAndOrgIdMap;

    @ApiModelProperty("部门名称与orgId映射")
    private Map<String, List<OrgObjectDTO>> deptNameAndOrgIdMap;

    @ApiModelProperty("岗位名称与orgId映射")
    private Map<String, List<OrgObjectDTO>> positionNameAndOrgIdMap;

    @ApiModelProperty("小组名称与orgId映射")
    private Map<String, List<OrgObjectDTO>> teamNameAndOrgIdMap;

    @ApiModelProperty("角色名称与orgId映射")
    private Map<String, List<OrgObjectDTO>> roleNameAndOrgIdMap;

    @ApiModelProperty("上传类型与sourceIdUK与下载地址映射")
    private Map<String, List<String>> uploadTypeAndSourceIdUkAndDownloadUrlsMap;

    @ApiModelProperty("数据索引与上传类型与sourceIdUK映射")
    private Map<Integer, List<String>> dataIndexAndTypeSourceIdUKMap;

    @ApiModelProperty("下载结果映射")
    private Map<String, ZdDownloadResourceResultDTO> downloadUrlAndResultMap;

    @ApiModelProperty("待新增或更新数据与type sourceId uk映射")
    private Map<ZdPageFormDataDTO, List<String>> needSaveOrUpdateDataAndUKMap;

    @ApiModelProperty("数据索引与错误消息映射(仅限错误数据未生成时使用)")
    private Map<Integer, String> dataIndexAndErrMsg;

    @ApiModelProperty("组织ID与组织对象映射")
    private Map<String, OrgObjectDTO> orgIdAndOrgObjectMap;

    public ZdImportContext() {
        this.uploadTypeAndSourceIdUkAndDownloadUrlsMap = new HashMap<>(DEFAULT_INIT_SIZE);
        this.dataIndexAndTypeSourceIdUKMap = new HashMap<>(DEFAULT_INIT_SIZE);
        this.dataIndexAndErrMsg = new HashMap<>(DEFAULT_INIT_SIZE);
        this.orgIdAndOrgObjectMap = new HashMap<>(DEFAULT_INIT_SIZE);
    }

    public boolean isValidateField() {
        return Objects.equals(CommonBooleanEnum.TRUE.getValue(), this.fieldValidFlag);
    }

    public void putOrgIdAndOrgObjectMap(String orgId, OrgObjectDTO orgObject) {
        this.orgIdAndOrgObjectMap.put(orgId, orgObject);
    }

    public List<String> getUKsFromIndex(Integer index) {
        return this.dataIndexAndTypeSourceIdUKMap.get(index);
    }

    public void putDataIndexAndUK(Integer index, String type, String sourceId) {
        String uk = this.getUploadTypeAndSourceIdUK(type, sourceId);
        List<String> ukList = this.dataIndexAndTypeSourceIdUKMap.get(index);
        if (Objects.isNull(ukList)) {
            ukList = new LinkedList<>();
            this.dataIndexAndTypeSourceIdUKMap.put(index, ukList);
        }
        ukList.add(uk);
    }

    public ZdPageComponentDTO getPageComponent(String ref) {
        return this.refAndComponentMap.get(ref);
    }

    public ZdDownloadResourceResultDTO getDownloadResult(String url) {
        return this.downloadUrlAndResultMap.get(url);
    }

    public List<String> getAllNeedDownloadUrl() {
        // 获取所有需要下载的url,这里要排除已失败的数据
        Map<Integer, Boolean> dataIndexAndErrorFlagMap = this.getDataIndexAndErrMsg().keySet()
            .stream()
            .collect(Collectors.toMap(item -> item, item -> Boolean.TRUE));

        List<String> allNeedDownloadUrl = new LinkedList<>();
        List<String> allNeedDownloadUks = new LinkedList<>();
        this.dataIndexAndTypeSourceIdUKMap.forEach((dataIndex, uks) -> {
            if (!Objects.equals(Boolean.TRUE, dataIndexAndErrorFlagMap.get(dataIndex))) {
                allNeedDownloadUks.addAll(uks);
            }
        });
        allNeedDownloadUks.forEach(uk -> {
            List<String> downloadUrls = this.getDownloadUrlByUK(uk);
            allNeedDownloadUrl.addAll(downloadUrls);
        });
        return allNeedDownloadUrl.stream().distinct().collect(Collectors.toList());
    }

    public void putDownloadCache(String type, String sourceId, String downloadUrl) {
        String uk = this.getUploadTypeAndSourceIdUK(type, sourceId);
        List<String> urls = this.uploadTypeAndSourceIdUkAndDownloadUrlsMap.get(uk);
        if (Objects.isNull(urls)) {
            urls = new LinkedList<>();
            uploadTypeAndSourceIdUkAndDownloadUrlsMap.put(uk, urls);
        }
        urls.add(downloadUrl);
    }

    public List<String> getDownloadUrlByUK(String uk) {
        return this.uploadTypeAndSourceIdUkAndDownloadUrlsMap.get(uk);
    }

    private String getUploadTypeAndSourceIdUK(String type, String sourceId) {
        return String.format("%s%s%s", type, DEFAULT_SPLIT_STR, sourceId);
    }

    public Tuple2<Long, Integer> parseAttachmentTypeAndSource(String uk) {
        String[] split = uk.split(DEFAULT_SPLIT_STR);
        return Tuples.of(Long.valueOf(split[1]), Integer.parseInt(split[0]));
    }

}
