package com.sinitek.sirm.nocode.form.support.handler.parse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025-07-29 09:36
 */
@Data
@ApiModel("智搭组件值解析器")
public class ZdComponentValueParserContainer {

    @ApiModelProperty("解析器")
    private Map<String, IZdComponentValueParser> componentNameAndParserMap;

    @ApiModelProperty("默认解析器")
    private IZdComponentValueParser defaultParser;

    public ZdComponentValueParserContainer(List<IZdComponentValueParser> parsers) {
        this.componentNameAndParserMap = new HashMap<>(parsers.size());
        for (IZdComponentValueParser parser : parsers) {
            boolean defaultParser = parser.isDefaultParser();
            if (defaultParser) {
                this.defaultParser = parser;
            } else {
                String matchedComponentName = parser.getMatchedComponentName();
                this.componentNameAndParserMap.put(matchedComponentName, parser);
            }
        }
    }

    public IZdComponentValueParser getParser(String componentName) {
        IZdComponentValueParser parser = this.componentNameAndParserMap.get(componentName);
        if (Objects.isNull(parser)) {
            return this.defaultParser;
        }
        return parser;
    }

}
