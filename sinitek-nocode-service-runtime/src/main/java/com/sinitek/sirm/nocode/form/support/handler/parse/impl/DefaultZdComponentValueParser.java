package com.sinitek.sirm.nocode.form.support.handler.parse.impl;

import com.sinitek.sirm.nocode.form.constant.FormSubmitFieldConstant;
import com.sinitek.sirm.nocode.form.dto.ZdPageComponentDTO;
import com.sinitek.sirm.nocode.form.support.ctx.ZdImportContext;
import com.sinitek.sirm.nocode.form.support.ctx.ZdParseComponentValueContext;
import com.sinitek.sirm.nocode.form.support.handler.parse.IZdComponentValueParser;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;

/**
 * <AUTHOR>
 * @date 2025-07-29 09:17
 */
@Slf4j
public class DefaultZdComponentValueParser implements IZdComponentValueParser {

    @Override
    public String getMatchedComponentName() {
        return "";
    }

    @Override
    public boolean isDefaultParser() {
        return true;
    }

    @Override
    public void parseComponentValueOnImport(ZdPageComponentDTO component,
        Map<String, Object> convertedData,
        Map<String, Object> sourceMap,
        ZdParseComponentValueContext ctx) {
        String ref = component.getRef();
        Object value = sourceMap.get(ref);
        convertedData.put(ref, value);
    }

    @Override
    public Map<String, Object> toSubmitFileBlockOnImport(ZdPageComponentDTO pageComponent,
        Map<String, Object> valueMap, ZdImportContext ctx) {
        String ref = pageComponent.getRef();
        String label = pageComponent.getLabel();
        String componentName = pageComponent.getComponentName();

        Object value = MapUtils.getObject(valueMap, ref);

        // 前端定义的规则
        // 那就是说如果formData中对应的数据是null,submitField中就不会存在这个组件包括label,value,componentName等数据
        if (Objects.isNull(value)) {
            log.debug("当前组件 {} 对应数据为null,submitField中不应该出现这个组件的数据", ref);
            return null;
        }

        Map<String, Object> submitField = new HashMap<>();
        submitField.put(FormSubmitFieldConstant.REF, ref);
        submitField.put(FormSubmitFieldConstant.LABEL, label);
        submitField.put(FormSubmitFieldConstant.COMPONENT_NAME, componentName);
        submitField.put(FormSubmitFieldConstant.VALUE, value);
        return submitField;
    }
}
