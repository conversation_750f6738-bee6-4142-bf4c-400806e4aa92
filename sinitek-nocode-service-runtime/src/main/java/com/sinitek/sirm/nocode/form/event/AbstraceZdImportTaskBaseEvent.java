package com.sinitek.sirm.nocode.form.event;

import com.sinitek.sirm.common.event.support.SiniCubeEvent;
import com.sinitek.sirm.nocode.form.dto.ZdImportTaskEventSourceDTO;
import io.swagger.annotations.ApiModel;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2025-07-14 09:07
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "导入任务基础事件")
public abstract class AbstraceZdImportTaskBaseEvent extends
    SiniCubeEvent<ZdImportTaskEventSourceDTO> {

    public AbstraceZdImportTaskBaseEvent(ZdImportTaskEventSourceDTO source) {
        super(source);
    }
}
