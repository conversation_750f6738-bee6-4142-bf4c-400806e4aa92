package com.sinitek.sirm.nocode.form.constant;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;

/**
 * 表单字段常量
 *
 * <AUTHOR>
 * @date 2025-07-15 10:08
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class ZdFormFieldConstant {

    // 实例ID: 为long值
    // 子表单id: 为字符串
    public static final String KEY_FIELD_NAME = "id";

    // 以下为附件中字段
    public static final String UPLOAD_SOURCE_ID = "sourceId";
    public static final String UPLOAD_TYPE = "type";

}
