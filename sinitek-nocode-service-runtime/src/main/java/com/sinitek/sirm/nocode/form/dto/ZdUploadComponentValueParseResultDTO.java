package com.sinitek.sirm.nocode.form.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import java.util.Map;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @date 2025-07-24 10:40
 */
@Data
@SuperBuilder
@NoArgsConstructor
@ApiModel("上传组件值解析结果")
public class ZdUploadComponentValueParseResultDTO {

    @ApiModelProperty("解析后的值")
    private Map<String, Object> value;

    @ApiModelProperty("下载地址列表")
    private List<String> downlaodUrlList;

}
