package com.sinitek.sirm.nocode.form.support.ctx;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Map;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @date 2025-07-29 09:11
 */
@Data
@SuperBuilder
@Accessors(chain = true)
@ApiModel("数据导出上下文")
@EqualsAndHashCode
public class ZdParseExportValueContext {

    @ApiModelProperty("数据主键")
    private Long id;

    @ApiModelProperty("数据子表单主键")
    private String childId;

    @ApiModelProperty("数据导出上下文")
    private ZdFormDataExportContext ctx;

    @ApiModelProperty("每个子表单中数据最大行数 key:实例id/子表单id")
    private Map<String, Integer> keyAndMaxRowMap;
}
