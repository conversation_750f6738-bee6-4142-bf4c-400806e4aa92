package com.sinitek.sirm.nocode.form.listener;

import com.sinitek.sirm.nocode.form.dto.ZdExportResourceTaskExecParamDTO;
import com.sinitek.sirm.nocode.form.dto.ZdExportResourcesFileInfoDTO;
import com.sinitek.sirm.nocode.form.event.ExportResourceTaskCreateEvent;
import com.sinitek.sirm.nocode.form.service.IZdExportTaskService;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

/**
 * 监听导出资源任务创建事件,执行导出资源任务
 *
 * <AUTHOR>
 * @date 2025-07-08 13:40
 */
@Slf4j
@Component
public class ExportResourceTaskCreateEventListener {

    @Autowired
    private IZdExportTaskService exportTaskService;

    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT, fallbackExecution = true)
    public void handleExportResourceTaskCreateEvent(ExportResourceTaskCreateEvent event) {
        ZdExportResourceTaskExecParamDTO source = event.getSource();

        Long taskId = source.getTaskId();
        List<ZdExportResourcesFileInfoDTO> list = source.getList();

        log.info("监听到导出资源任务创建事件，开始执行导出任务，taskId: {},压缩包文件个数: {}",
            taskId,
            list.size());
        this.exportTaskService.runExportResourceAsync(source);

    }

}
