package com.sinitek.sirm.nocode.form.support.handler.pre;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.sinitek.sirm.common.attachment.dto.AttachmentDTO;
import com.sinitek.sirm.common.attachment.service.IAttachmentExtService;
import com.sinitek.sirm.common.utils.JsonUtil;
import com.sinitek.sirm.framework.frontend.dto.UploadDTO;
import com.sinitek.sirm.framework.frontend.dto.UploadFileDTO;
import com.sinitek.sirm.nocode.common.enumerate.SaveOrUpdateEnum;
import com.sinitek.sirm.nocode.form.entity.ZdPageFormData;
import com.sinitek.sirm.nocode.form.enumerate.PageDataComponentTypeEnum;
import com.sinitek.sirm.nocode.form.support.handler.base.PreDataSaveOrUpdateHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import javax.validation.Validator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * 附件上传处理
 *
 * <AUTHOR>
 * @version 2025.0702
 * @since 1.0.0-SNAPSHOT
 */
@Slf4j
@Component
public class UploadFileHandler implements PreDataSaveOrUpdateHandler {

    @Resource
    private IAttachmentExtService attachmentExtService;

    @Resource
    private Validator validator;


    /**
     * 处理表单数据中的文件上传操作
     *
     * @param formData 表单数据对象
     * @param type     操作类型（保存或更新）
     * @implNote 实际调用uploadFile方法进行文件处理
     */
    @Override
    public void handler(ZdPageFormData formData, SaveOrUpdateEnum type) {
        Map<String, Object> formDataMap = formData.getFormDataMap();
        // 存在上传文件时，才处理上传文件
        AtomicBoolean hasUploadFile = new AtomicBoolean(false);
        uploadFile(formDataMap, formData, hasUploadFile);
        if (hasUploadFile.get()) {
            formData.setFormData(JsonUtil.toJsonString(formDataMap));
        }

    }

    /**
     * 递归处理表单中的文件上传字段
     *
     * @param formDataMap 表单数据映射
     * @param formData    主表单数据对象
     * @implNote 支持处理嵌套子表单中的文件上传字段
     */
    @SuppressWarnings("unchecked")
    private void uploadFile(Map<String, Object> formDataMap, ZdPageFormData formData, AtomicBoolean hasUploadFile) {
        formDataMap.forEach((key, value) -> {
            PageDataComponentTypeEnum pageDataComponentTypeEnum = PageDataComponentTypeEnum.formKey(key);
            if (PageDataComponentTypeEnum.ZD_UPLOAD.equals(pageDataComponentTypeEnum)) {
                if (!(value instanceof Map)) {
                    return;
                }
                String jsonString = JsonUtil.toJsonString(value);
                UploadDTO uploadDTO = JsonUtil.toJavaObject(jsonString, UploadDTO.class);
                String sourceEntity = uploadDTO.getSourceEntity();
                if (StringUtils.isBlank(sourceEntity)) {
                    uploadDTO.setSourceEntity(formData.getFormCode());
                }
                // 校验上传文件参数
                Set<ConstraintViolation<UploadDTO>> validate = validator.validate(uploadDTO);
                if (CollectionUtils.isNotEmpty(validate)) {
                    throw new ConstraintViolationException(validate);
                }
                List<UploadFileDTO> uploadFileList = uploadDTO.getUploadFileList();
                List<UploadFileDTO> removeFileList = uploadDTO.getRemoveFileList();
                if (CollectionUtils.isNotEmpty(uploadFileList) || CollectionUtils.isNotEmpty(removeFileList)) {
                    List<AttachmentDTO> attachmentDTOS = attachmentExtService.saveAttachmentList(uploadDTO, uploadDTO.getSourceId(), uploadDTO.getSourceEntity());
                    if (CollectionUtils.isNotEmpty(attachmentDTOS)) {
                        String attachmentIds = attachmentDTOS.stream().map(AttachmentDTO::getId).collect(Collectors.joining(","));
                        log.info("上传文件成功，文件id为：{}", attachmentIds);
                    }
                }
                Map<String, Object> valueMap = (Map<String, Object>) value;
                Map<String, Object> map = new HashMap<>();
                map.put("sourceId", valueMap.get("sourceId"));
                map.put("type", valueMap.get("type"));
                // 删除其他属性，只保留sourceId和type
                formDataMap.put(key, map);
                hasUploadFile.set(true);
            } else if (PageDataComponentTypeEnum.ZD_CHILD_FORM.equals(pageDataComponentTypeEnum) && (value instanceof List)) {
                List<Map<String, Object>> childFormDataList = (List<Map<String, Object>>) value;
                if (CollectionUtils.isNotEmpty(childFormDataList)) {
                    childFormDataList.forEach(childFormData -> uploadFile(childFormData, formData, hasUploadFile));
                }
            }
        });
    }
}
