package com.sinitek.sirm.nocode.form.util;

import static com.sinitek.sirm.nocode.form.constant.FormDataExtraFieldConstant.ID_FIELD_KEY;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.sinitek.sirm.common.utils.GlobalConstant;
import com.sinitek.sirm.nocode.form.constant.FormDataExtraFieldConstant;
import com.sinitek.sirm.nocode.form.constant.FormSubmitFieldConstant;
import com.sinitek.sirm.nocode.form.dto.ZdPageFormDataDTO;
import com.sinitek.sirm.nocode.form.support.ctx.ZdFormDataExportContext;
import com.sinitek.sirm.nocode.form.support.ctx.ZdParseExportValueContext;
import com.sinitek.sirm.nocode.form.support.handler.ZdExportDataParseResult;
import com.sinitek.sirm.nocode.form.support.handler.parse.IZdComponentValueParser;
import com.sinitek.sirm.nocode.form.support.handler.parse.ZdComponentValueParserContainer;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

/**
 * 表单数据格式化工具类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class ZdFormSubmitFieldUtil {

    public static Integer getPageData(List<Object> submitFields,
        Long id,
        String childId,
        ZdFormDataExportContext ctx, Map<String, Object> result, ZdPageFormDataDTO data,
        Map<String, Integer> keyAndMaxRowMap, ZdComponentValueParserContainer container) {

        boolean isChild = StringUtils.isNotBlank(childId);
        boolean hasData = Objects.nonNull(data);
        // 遍历每个字段
        if (CollUtil.isNotEmpty(submitFields)) {
            int maxRow = 0;
            for (Object submitField : submitFields) {
                Map<String, Object> submitFieldMap = (Map) submitField;

                String ref = MapUtils.getString(submitFieldMap, FormSubmitFieldConstant.REF);
                String componentName = MapUtils.getString(submitFieldMap,
                    FormSubmitFieldConstant.COMPONENT_NAME);

                IZdComponentValueParser parser = container.getParser(componentName);
                ZdExportDataParseResult parseResult = parser.getExportData(submitFieldMap,
                    ZdParseExportValueContext.builder().id(id).childId(childId).ctx(ctx)
                        .keyAndMaxRowMap(keyAndMaxRowMap).build());
                Integer maxRowInResult = parseResult.getMaxRow();
                Object exportValue = parseResult.getValue();

                result.put(ref, exportValue);
                maxRow = Math.max(maxRow, maxRowInResult);
            }

            if (!isChild && hasData) {
                // 主表单填充框架字段
                result.put(ID_FIELD_KEY, String.valueOf(data.getId()));
                result.put(FormDataExtraFieldConstant.CREATOR_FIELD_KEY, data.getCreator());
                result.put(FormDataExtraFieldConstant.CREATE_TIMESTAMP_FIELD_KEY,
                    DateUtil.format(data.getCreateTimeStamp(),
                        GlobalConstant.TIME_FORMAT_THIRTEEN));
                result.put(FormDataExtraFieldConstant.UPDATE_TIMESTAMP_FIELD_KEY,
                    DateUtil.format(data.getUpdateTimeStamp(),
                        GlobalConstant.TIME_FORMAT_THIRTEEN));
            }

            if (Objects.nonNull(keyAndMaxRowMap)) {
                if (isChild) {
                    keyAndMaxRowMap.put(childId, maxRow);
                } else {
                    keyAndMaxRowMap.put(String.valueOf(id), maxRow);
                }
            }

            return maxRow;
        }
        return 0;
    }
}
