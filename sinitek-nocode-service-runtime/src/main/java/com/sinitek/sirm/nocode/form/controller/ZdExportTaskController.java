package com.sinitek.sirm.nocode.form.controller;

import java.util.Date;
import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sinitek.sirm.common.user.factory.CurrentUserFactory;
import com.sinitek.sirm.framework.frontend.support.RequestResult;
import com.sinitek.sirm.framework.frontend.support.TableResult;
import com.sinitek.sirm.nocode.app.dto.ZdExportTaskAttachmentDTO;
import com.sinitek.sirm.nocode.app.dto.ZdExportTaskSearchParamDTO;
import com.sinitek.sirm.nocode.app.dto.ZdExportTaskSearchResultDTO;
import com.sinitek.sirm.nocode.common.dto.base.ZdIdDTO;
import com.sinitek.sirm.nocode.form.aspect.ZdAuth;
import com.sinitek.sirm.nocode.form.dto.ZdExportDataErrorMsgDTO;
import com.sinitek.sirm.nocode.form.dto.ZdExportOrImportLoadDetailParamDTO;
import com.sinitek.sirm.nocode.form.dto.ZdFormDataExportParamDTO;
import com.sinitek.sirm.nocode.form.dto.ZdFormDataExportResultDTO;
import com.sinitek.sirm.nocode.form.dto.ZdFormDataExportRetryDTO;
import com.sinitek.sirm.nocode.form.formatter.ZdExportTaskSearchResultFormatter;
import com.sinitek.sirm.nocode.form.service.IZdExportDataService;
import com.sinitek.sirm.nocode.form.service.IZdExportTaskService;
import com.sinitek.sirm.nocode.page.enumerate.OperationAuthEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version 2025.07.03
 * @description 数据导出
 * @since 1.0.0
 */
@Slf4j
@RestController
@Api(value = "/frontend/api/nocode/export-task", tags = "智搭-数据导出")
@RequestMapping("/frontend/api/nocode/export-task")
public class ZdExportTaskController {

    @Autowired
    private IZdExportDataService service;

    @Autowired
    private IZdExportTaskService exportTaskService;

    @Autowired
    private ZdExportTaskSearchResultFormatter formatter;

    /**
     * 查询导出任务列表
     *
     * @param param 查询参数
     * @return 查询结果
     */
    @ZdAuth(operationAuthType = OperationAuthEnum.VIEW)
    @ApiOperation(value = "查询导出任务列表")
    @PostMapping(path = "/search")
    public TableResult<ZdExportTaskSearchResultDTO> search(
            @RequestBody @Valid ZdExportTaskSearchParamDTO param) {
        IPage<ZdExportTaskSearchResultDTO> search = this.exportTaskService.search(param);
        return param.build(search, this.formatter);
    }

    /**
     * 导出数据,既然有查看数据的权限，那么也应该有导出数据的权限
     *
     * @param param 参数
     */
    @ZdAuth(operationAuthType = OperationAuthEnum.VIEW)
    @ApiOperation(value = "表单数据导出")
    @PostMapping("/export")
    public RequestResult<ZdFormDataExportResultDTO> export(
            @RequestBody @ApiParam(value = "表单数据导出", name = "表单数据导出") @Validated(
                    value = ZdIdDTO.View.class) ZdFormDataExportParamDTO param) {
        param.setOperatorId(CurrentUserFactory.getCurrentUserInfo().getOrgId());
        param.setOperateTime(new Date());
        ZdFormDataExportResultDTO exportResult = this.service.export(param);
        return new RequestResult<>(exportResult);
    }

    /**
     * 重新执行导出任务
     */
    @ZdAuth(operationAuthType = OperationAuthEnum.VIEW)
    @ApiOperation(value = "重新执行导出任务")
    @PostMapping("/retry")
    public RequestResult<Void> retry(HttpServletRequest request,
            @RequestBody @ApiParam(value = "表单数据导出重试", name = "表单数据导出重试") @Validated(
                    value = ZdIdDTO.View.class) ZdFormDataExportRetryDTO param) {
        param.setOperatorId(CurrentUserFactory.getCurrentUserInfo().getOrgId());
        param.setOperateTime(new Date());
        this.service.retry(param);
        return new RequestResult<>(null);
    }

    @ZdAuth(operationAuthType = OperationAuthEnum.VIEW)
    @ApiOperation(value = "单文件加载文件id")
    @PostMapping("/attachment/load-excel-attachment-id")
    public RequestResult<String> loadExcelDetail(
            @RequestBody @ApiParam(value = "加载Excel文件id参数", name = "加载Excel文件id参数") @Validated(
                    value = ZdIdDTO.View.class) ZdExportOrImportLoadDetailParamDTO param) {
        Long taskId = param.getTaskId();
        return new RequestResult<>(this.exportTaskService.getExportExcelAttachmentId(taskId));
    }

    @ZdAuth(operationAuthType = OperationAuthEnum.VIEW)
    @ApiOperation(value = "加载导出附件列表")
    @PostMapping("/attachment/search")
    public TableResult<ZdExportTaskAttachmentDTO> searchAttachment(
            @RequestBody @ApiParam(value = "加载导出附件列表", name = "加载导出附件列表") @Validated(
                    value = ZdIdDTO.View.class) ZdExportOrImportLoadDetailParamDTO param) {
        Long taskId = param.getTaskId();
        List<ZdExportTaskAttachmentDTO> attachmentList =
                this.exportTaskService.findExportExcelAttachment(taskId);
        IPage<ZdExportTaskAttachmentDTO> page = new Page<>();
        page.setTotal(attachmentList.size());
        page.setRecords(attachmentList);
        return new TableResult<>(page);
    }

    @ZdAuth(operationAuthType = OperationAuthEnum.VIEW)
    @ApiOperation(value = "查看失败原因")
    @PostMapping("/load-error-detail")
    public RequestResult<ZdExportDataErrorMsgDTO> loadErrorDetail(
            @RequestBody @ApiParam(value = "查看失败原因", name = "查看失败原因") @Validated(
                    value = ZdIdDTO.View.class) ZdExportOrImportLoadDetailParamDTO param) {
        Long taskId = param.getTaskId();
        return new RequestResult<>(this.exportTaskService.getExportTaskErrorDetail(taskId));
    }

    @ApiOperation(value = "删除历史已成功导出任务")
    @GetMapping("/delete-history-success-task")
    public RequestResult<Void> deleteHistorySuccessTask(@RequestParam(value = "days",
            defaultValue = "30") @ApiParam(value = "天数", name = "days") Integer days) {
        this.exportTaskService.cleanHistoryExportTask(days);
        return new RequestResult<>(null);
    }

    @ApiOperation(value = "检查导出任务状态")
    @GetMapping("/check-export-task-status")
    public RequestResult<Void> checkExportTaskStatus(@RequestParam(value = "minutes",
            defaultValue = "60") @ApiParam(value = "分钟数", name = "minutes") Integer minutes) {
        this.exportTaskService.checkExportTaskStatus(minutes);
        return new RequestResult<>(null);
    }
}
