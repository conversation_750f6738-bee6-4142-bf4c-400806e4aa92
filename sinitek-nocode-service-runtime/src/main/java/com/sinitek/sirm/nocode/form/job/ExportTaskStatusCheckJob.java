package com.sinitek.sirm.nocode.form.job;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import com.sinitek.sirm.nocode.form.service.IZdExportTaskService;

/**
 * 检查长时间处于"处理中"状态的任务，超过1小时自动标记为失败
 *
 * <AUTHOR>
 * @date 2025-07-16 10:08
 */
@Component
public class ExportTaskStatusCheckJob {

  @Autowired
  private IZdExportTaskService exportTaskService;

  @Scheduled(cron = "0 0/5 * * * ?")
  public void checkExportTaskStatus() {
    this.exportTaskService.checkExportTaskStatus(60);
  }

}
