package com.sinitek.sirm.nocode.form.controller;

import com.sinitek.sirm.framework.frontend.support.RequestResult;
import com.sinitek.sirm.framework.frontend.support.TableResult;
import com.sinitek.sirm.nocode.form.constant.FormConstant;
import com.sinitek.sirm.nocode.form.dto.ZdFormDataSearchParamDTO;
import com.sinitek.sirm.nocode.form.dto.ZdPageFormDataDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 表单数据管理对外接口
 *
 * <AUTHOR>
 * @version 2025.0508
 * @since 1.0.0-SNAPSHOT
 */
@Slf4j
@RestController
@Api(value = "/frontend/api/openness-api/form/data", tags = "表单数据管理对外接口")
@RequestMapping("/frontend/api/openness-api/nocode/form/data")
public class ZdFormDataOpenApiController {
    @Resource
    private ZdFormDataController zdFormDataController;

    @ApiOperation(value = "查询数据列表")
    @PostMapping("/search")
    public TableResult<ZdPageFormDataDTO> search(@RequestBody @ApiParam(value = "表单数据查询条件", name = "表单数据查询条件") @Valid ZdFormDataSearchParamDTO param) {
        return zdFormDataController.search(param);
    }


    @ApiOperation(value = "查看某条数据详情")
    @GetMapping("/view")
    public RequestResult<ZdPageFormDataDTO> view(@RequestParam(FormConstant.FORM_CODE)
                                                 @ApiParam(value = "表单编码", name = FormConstant.FORM_CODE)
                                                 String formCode, @RequestParam("id")
                                                 @ApiParam(value = "数据主键", name = "id")
                                                 Long id
    ) {
        return zdFormDataController.view(formCode, id);
    }

    @ApiOperation(value = "获取多条数据")
    @GetMapping("/list")
    public RequestResult<List<ZdPageFormDataDTO>> list(@RequestParam(FormConstant.FORM_CODE)
                                                       @ApiParam(value = "表单编码", name = FormConstant.FORM_CODE)
                                                       String formCode,
                                                       @RequestBody
                                                       @ApiParam(value = "数据主键集合", name = "idList")
                                                       List<Long> idList
    ) {
        return zdFormDataController.list(formCode, idList);
    }
}
