package com.sinitek.sirm.nocode.form.constant;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025-07-08 13:13
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class ExportAndImportTaskExecutorConstant {

    public static final String DEFAULT_IMPORT_TASK_EXECUTOR_NAME = "importTaskExecutor";

    public static final String DEFAULT_EXPORT_TASK_EXECUTOR_NAME = "exportTaskExecutor";

    public static final String DEFAULT_RESOURCE_EXPORT_TASK_EXECUTOR_NAME = "exportResourceTaskExecutor";

    public static final String DEFAULT_DOWNLOAD_RESOURCE_EXECUTOR_NAME = "downloadResourceExecutor";
}
