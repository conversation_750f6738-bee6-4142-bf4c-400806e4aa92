package com.sinitek.sirm.nocode.form.support.ctx;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Map;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @date 2025-07-29 09:11
 */
@Data
@SuperBuilder
@Accessors(chain = true)
@ApiModel("数据解析上下文")
@EqualsAndHashCode
public class ZdParseComponentValueContext {

    @ApiModelProperty("数据索引")
    private Integer dataIndex;

    @ApiModelProperty("导入上下文")
    private ZdImportContext ctx;

    @ApiModelProperty("数据库已存在数据")
    private Map<String, Object> existedFormDataFromDB;
}
