package com.sinitek.sirm.nocode.form.listener;

import com.sinitek.sirm.nocode.form.dto.ZdImportDataTaskExecParamDTO;
import com.sinitek.sirm.nocode.form.dto.ZdImportTaskEventSourceDTO;
import com.sinitek.sirm.nocode.form.event.ImportTaskRetryEvent;
import com.sinitek.sirm.nocode.form.service.IZdImportTaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

/**
 * 监听导入任务重试事件,重试执行导入任务
 *
 * <AUTHOR>
 * @date 2025-07-08 13:40
 */
@Slf4j
@Component
public class ImportTaskRetryEventListener {

    @Autowired
    private IZdImportTaskService importTaskService;

    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT, fallbackExecution = true)
    public void handleExportTaskCreateEvent(ImportTaskRetryEvent event) {
        ZdImportTaskEventSourceDTO source = event.getSource();
        Long taskId = source.getTaskId();

        log.info("监听到导入任务重试事件，开始重试执行导入任务，taskId: {}", taskId);
        this.importTaskService.runImportTaskAsync(ZdImportDataTaskExecParamDTO
            .builder()
            .taskId(taskId)
            .build());
    }

}
