package com.sinitek.sirm.nocode.form.support.handler.parse.impl;

import static com.sinitek.sirm.nocode.form.constant.ZdFormFieldConstant.UPLOAD_SOURCE_ID;
import static com.sinitek.sirm.nocode.form.constant.ZdFormFieldConstant.UPLOAD_TYPE;

import cn.hutool.core.collection.CollUtil;
import com.sinitek.sirm.common.attachment.dto.AttachmentDTO;
import com.sinitek.sirm.common.attachment.service.IAttachmentExtService;
import com.sinitek.sirm.common.spring.SpringFactory;
import com.sinitek.sirm.common.utils.JsonUtil;
import com.sinitek.sirm.nocode.form.constant.FormDataConstant;
import com.sinitek.sirm.nocode.form.constant.FormSubmitFieldConstant;
import com.sinitek.sirm.nocode.form.dto.ZdPageComponentDTO;
import com.sinitek.sirm.nocode.form.dto.ZdUploadComponentValueParseResultDTO;
import com.sinitek.sirm.nocode.form.dto.ZdUploadObjectDTO;
import com.sinitek.sirm.nocode.form.enumerate.PageDataComponentTypeEnum;
import com.sinitek.sirm.nocode.form.support.ctx.ZdFormDataExportContext;
import com.sinitek.sirm.nocode.form.support.ctx.ZdImportContext;
import com.sinitek.sirm.nocode.form.support.ctx.ZdParseComponentValueContext;
import com.sinitek.sirm.nocode.form.support.ctx.ZdParseExportValueContext;
import com.sinitek.sirm.nocode.form.support.handler.ZdExportDataParseResult;
import com.sinitek.sirm.nocode.form.support.handler.parse.IZdComponentValueParser;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2025-07-29 09:25
 */
@Slf4j
public class ZdUploadComponentValueParser implements IZdComponentValueParser {

    @Override
    public String getMatchedComponentName() {
        return PageDataComponentTypeEnum.ZD_UPLOAD.getValue();
    }

    @Override
    public void parseComponentValueOnImport(ZdPageComponentDTO component,
        Map<String, Object> convertedData,
        Map<String, Object> sourceMap, ZdParseComponentValueContext ctx) {
        String ref = component.getRef();
        Object value = sourceMap.get(ref);
        Integer dataIndex = ctx.getDataIndex();
        ZdImportContext importContext = ctx.getCtx();

        Map<String, Object> existedFormDataFromDB = ctx.getExistedFormDataFromDB();
        Map<String, Object> exitedData = (Map<String, Object>) MapUtils.getMap(
            existedFormDataFromDB, ref);
        boolean isNeedGenerateValue = CollUtil.isEmpty(exitedData);
        // 附件
        ZdUploadComponentValueParseResultDTO parseResult = this.parseZdUploadComponentValue(
            component, value, isNeedGenerateValue);
        this.parseUploadComponentValue(dataIndex, isNeedGenerateValue, parseResult, exitedData,
            convertedData, ref, importContext);
    }

    @Override
    public Map<String, Object> toSubmitFileBlockOnImport(ZdPageComponentDTO pageComponent,
        Map<String, Object> valueMap, ZdImportContext ctx) {
        String ref = pageComponent.getRef();
        String label = pageComponent.getLabel();
        String componentName = pageComponent.getComponentName();

        Object value = MapUtils.getObject(valueMap, ref);

        // 前端定义的规则
        // 那就是说如果formData中对应的数据是null,submitField中就不会存在这个组件包括label,value,componentName等数据
        if (Objects.isNull(value)) {
            log.debug("当前组件 {} 对应数据为null,submitField中不应该出现这个组件的数据", ref);
            return null;
        }

        Map<String, Object> submitField = new HashMap<>();
        submitField.put(FormSubmitFieldConstant.REF, ref);
        submitField.put(FormSubmitFieldConstant.LABEL, label);
        submitField.put(FormSubmitFieldConstant.COMPONENT_NAME, componentName);
        submitField.put(FormSubmitFieldConstant.VALUE, value);
        return submitField;
    }

    @Override
    public ZdExportDataParseResult getExportData(Map<String, Object> submitFieldMap,
        ZdParseExportValueContext ctx) {
        Long id = ctx.getId();
        String childId = ctx.getChildId();
        ZdFormDataExportContext exportContext = ctx.getCtx();

        String ref = MapUtils.getString(submitFieldMap, FormSubmitFieldConstant.REF);
        Object value = MapUtils.getObject(submitFieldMap,
            FormSubmitFieldConstant.VALUE);

        int maxRow = 0;
        Object storeValue = null;
        if (value instanceof Map) {
            Map<String, Object> uploadValue = (Map<String, Object>) value;
            Long sourceId = MapUtils.getLong(uploadValue, UPLOAD_SOURCE_ID);
            String sourceEntity = exportContext.getFormCode();
            Integer type = MapUtils.getInteger(uploadValue, UPLOAD_TYPE);
            List<AttachmentDTO> attachments = exportContext.getAttachments(sourceId, sourceEntity,
                type);
            if (Objects.isNull(attachments)) {
                IAttachmentExtService attachmentExtService = exportContext.getAttachmentExtService();
                if (Objects.isNull(attachmentExtService)) {
                    attachmentExtService = SpringFactory.getBean(IAttachmentExtService.class);
                }
                attachments = attachmentExtService.findAttachments(sourceEntity,
                    type, Collections.singletonList(sourceId));
                if (Objects.isNull(attachments)) {
                    attachments = Collections.emptyList();
                }
                exportContext.putAttachments(sourceId, sourceEntity, type, attachments);
                exportContext.putAttachmentsId(sourceId, sourceEntity, type, id, childId);
            }

            if (CollUtil.isNotEmpty(attachments)) {
                boolean exportAttachment = exportContext.isExportAttachment();
                if (exportAttachment) {
                    // 导出附件
                    List<ZdUploadObjectDTO> list = new LinkedList<>();
                    for (int i = 0; i < attachments.size(); i++) {
                        ZdUploadObjectDTO data = ZdUploadObjectDTO
                            .builder()
                            .sourceId(sourceId)
                            .sourceEntity(sourceEntity)
                            .type(type)
                            .id(id)
                            .childId(childId)
                            .fileIndex(i)
                            .build();
                        list.add(data);
                    }
                    storeValue = list;
                    maxRow = list.size();
                } else {
                    // 导出下载链接
                    storeValue = ZdUploadObjectDTO
                        .builder()
                        .sourceId(sourceId)
                        .sourceEntity(sourceEntity)
                        .type(type)
                        .id(id)
                        .build();
                    maxRow = 1;
                }
            }
        } else {
            log.warn("附件[{}]对应值应该是个Map,当前数据 {} 格式不符合约定", ref,
                JsonUtil.toJsonString(value));
        }
        return new ZdExportDataParseResult(storeValue, maxRow);
    }

    private void parseUploadComponentValue(int dataIndex, boolean isNeedGenerateValue,
        ZdUploadComponentValueParseResultDTO parseResult, Map<String, Object> exitedData,
        Map<String, Object> conveertedData, String ref, ZdImportContext ctx) {
        Map<String, Object> uploadValue;
        if (isNeedGenerateValue) {
            uploadValue = parseResult.getValue();
        } else {
            uploadValue = exitedData;
        }
        conveertedData.put(ref, uploadValue);

        String type = MapUtils.getString(uploadValue, FormDataConstant.UPLOAD_TYPE);
        String sourceId = MapUtils.getString(uploadValue, FormDataConstant.UPLOAD_SOURCE_ID);

        List<String> downlaodUrlList = parseResult.getDownlaodUrlList();
        if (CollUtil.isNotEmpty(downlaodUrlList)) {
            downlaodUrlList.forEach(downloadUrl -> {
                ctx.putDownloadCache(type, sourceId, downloadUrl);
            });
            ctx.putDataIndexAndUK(dataIndex, type, sourceId);
        }
    }

    private ZdUploadComponentValueParseResultDTO parseZdUploadComponentValue(
        ZdPageComponentDTO component,
        Object value, boolean isNeedGenerateValue) {

        String downloadUrls = null;
        if (value instanceof String) {
            downloadUrls = (String) value;
        }

        Map<String, Object> valueResult = new HashMap<>();
        if (isNeedGenerateValue) {
            String type = getRandomType();
            String sourceId = getRandomSourceId();
            valueResult.put(FormDataConstant.UPLOAD_TYPE, type);
            valueResult.put(FormDataConstant.UPLOAD_SOURCE_ID, sourceId);
        }

        List<String> downloadUrlList = new LinkedList<>();
        if (StringUtils.isNotBlank(downloadUrls)) {
            downloadUrlList = Arrays.stream(downloadUrls.split(","))
                .filter(StringUtils::isNotBlank).collect(
                    Collectors.toList());
        }
        return ZdUploadComponentValueParseResultDTO.builder()
            .value(valueResult)
            .downlaodUrlList(downloadUrlList)
            .build();
    }

    private String getRandomType() {
        Integer randomNumber = ThreadLocalRandom.current().nextInt(1, 10000);
        return String.valueOf(randomNumber);
    }

    private String getRandomSourceId() {
        long randomNumber = ThreadLocalRandom.current().nextLong(1, 1000000000000000L);
        return String.valueOf(randomNumber);
    }
}
