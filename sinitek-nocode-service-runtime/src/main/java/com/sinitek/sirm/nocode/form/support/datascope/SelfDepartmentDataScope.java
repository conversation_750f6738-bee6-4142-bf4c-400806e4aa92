package com.sinitek.sirm.nocode.form.support.datascope;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.sinitek.sirm.nocode.common.constant.ZdCommonConstant;
import com.sinitek.sirm.nocode.form.entity.ZdPageFormData;
import com.sinitek.sirm.nocode.form.support.datascope.base.DataScopeConditionSetter;
import com.sinitek.sirm.nocode.page.dto.ZdPageAuthDTO;
import com.sinitek.sirm.nocode.page.enumerate.DataScopeEnum;
import com.sinitek.sirm.org.entity.Employee;
import com.sinitek.sirm.org.enumerate.DepartmentEmployeeQueryScopeEnum;
import com.sinitek.sirm.org.service.IOrgService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 本部门提交的数据权限
 *
 * <AUTHOR>
 * @version 2025.0325
 * @see DataScopeEnum#SELF_DEPARTMENT
 * @since 1.0.0-SNAPSHOT
 */
@Component
public class SelfDepartmentDataScope implements DataScopeConditionSetter {
    @Resource
    private IOrgService orgService;

    @Override
    public void setCondition(QueryWrapper<ZdPageFormData> queryWrapper, ZdPageAuthDTO auth, String currentOrgId, Set<String> orgIds) {
        // 获取当前用户的部门
        // 本部门
        List<Employee> employeeList = orgService.findDepartmentEmployeesByEmpId(currentOrgId, DepartmentEmployeeQueryScopeEnum.PEERS);
        if (CollectionUtils.isNotEmpty(employeeList)) {
            orgIds.addAll(employeeList.stream().map(Employee::getId).collect(Collectors.toList()));
        } else {
            orgIds.add(ZdCommonConstant.NO_PERSON_ORG_ID);
        }

    }
}
