package com.sinitek.sirm.nocode.form.support.convert;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.HyperlinkData;
import com.alibaba.excel.metadata.data.HyperlinkData.HyperlinkType;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.sinitek.sirm.common.attachment.dto.AttachmentDTO;
import com.sinitek.sirm.common.attachment.service.IAttachmentExtService;
import com.sinitek.sirm.common.utils.JsonUtil;
import com.sinitek.sirm.nocode.form.dto.ZdUploadObjectDTO;
import com.sinitek.sirm.nocode.form.support.ctx.ZdFormDataExportContext;
import com.sinitek.sirm.nocode.form.util.ZdExportUtil;
import com.sinitek.sirm.setting.service.ISettingExtService;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;

/**
 * 组织结构数据转换器
 *
 * <AUTHOR>
 * @date 2025-07-08 14:41
 */
@Slf4j
public class UploadObjConvert implements Converter<ZdUploadObjectDTO> {

    private ISettingExtService settingExtService;

    private IAttachmentExtService attachmentExtService;

    private ZdFormDataExportContext ctx;

    public UploadObjConvert(ISettingExtService settingExtService,
        IAttachmentExtService attachmentExtService,
        ZdFormDataExportContext ctx) {
        this.settingExtService = settingExtService;
        this.attachmentExtService = attachmentExtService;
        this.ctx = ctx;
    }

    @Override
    public Class<ZdUploadObjectDTO> supportJavaTypeKey() {
        return ZdUploadObjectDTO.class;
    }

    @Override
    public WriteCellData<?> convertToExcelData(ZdUploadObjectDTO value,
        ExcelContentProperty contentProperty,
        GlobalConfiguration globalConfiguration) {

      Long id = value.getId();
      String childId = value.getChildId();
        Long sourceId = value.getSourceId();
        String sourceEntity = value.getSourceEntity();
        Integer type = value.getType();
        Integer fileIndex = value.getFileIndex();

        List<AttachmentDTO> attachments = ctx.getAttachments(sourceId, sourceEntity, type);
        if (Objects.isNull(attachments)) {
            attachments = this.attachmentExtService.findAttachments(sourceEntity,
                type, Collections.singletonList(sourceId));
            if (Objects.isNull(attachments)) {
                attachments = Collections.emptyList();
            }
            ctx.putAttachments(sourceId, sourceEntity, type, attachments);
            ctx.putAttachmentsId(sourceId, sourceEntity, type, id, childId);
        }

        boolean isExportAttachment = ctx.isExportAttachment();

        WriteCellData<Object> cellData = new WriteCellData<>("");
        if (CollUtil.isNotEmpty(attachments)) {
            if (isExportAttachment) {
                // 导出附件
                int size = attachments.size();
                if (size >= fileIndex) {
                    AttachmentDTO attachment = attachments.get(fileIndex);
                    String name = attachment.getName();
                    String fileType = attachment.getFileType();
                    String fileHyperlink = this.getFileHyperlink(value, name, fileType);
                    HyperlinkData hyperlinkData = new HyperlinkData();
                    hyperlinkData.setHyperlinkType(HyperlinkType.URL);
                    hyperlinkData.setAddress(fileHyperlink);
                    cellData = new WriteCellData<>(fileHyperlink);
                    cellData.setHyperlinkData(hyperlinkData);
                } else {
                    log.warn(
                        "sourceId: {},sourceEntity: {},type: {},附件索引超出范围，附件数量: {}, 附件索引: {}",
                        sourceId, sourceEntity, type, size, fileIndex);
                }
            } else {
                // 导出附件下载链接
                String attachmentDownloadUrl = attachments.stream().map(item -> {
                    String attachmentId = item.getId();
                    return this.getDownloadUrl(attachmentId);
                }).collect(Collectors.joining(","));
                cellData = new WriteCellData<>(attachmentDownloadUrl);
            }
        }

        if (log.isDebugEnabled()) {
            log.debug("附件 {} 对应单元格数据 {}", JsonUtil.toJsonString(value),
                JsonUtil.toJsonString(cellData));
        }

        return cellData;
    }


    private String getFileHyperlink(ZdUploadObjectDTO value, String fileName, String fileTypeName) {
        Long id = value.getId();
        String childId = value.getChildId();
        Integer fileType = value.getType();
        return ZdExportUtil.getFileHyperlink(id, childId, fileType, fileName, fileTypeName);
    }

    private String getDownloadUrl(String attachmentId) {
        // return this.ctx.getDirectConcatRequestHost() + "frontend/api/common/downloadattchment?accesstoken="
        //     + this.ctx.getAccessToken()
        //     + "&id=" + attachmentId + "&type=download";

        return this.ctx.getDirectConcatRequestHost()
            + "/frontend/api/common/downloadattchment?"
            + "id=" + attachmentId + "&type=download";
    }
}
