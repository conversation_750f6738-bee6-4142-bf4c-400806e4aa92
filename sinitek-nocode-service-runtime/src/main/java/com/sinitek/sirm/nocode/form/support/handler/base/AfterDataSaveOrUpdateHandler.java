package com.sinitek.sirm.nocode.form.support.handler.base;

import com.sinitek.sirm.nocode.common.enumerate.SaveOrUpdateEnum;
import com.sinitek.sirm.nocode.form.entity.ZdPageFormData;

/**
 * 后置处理器
 *
 * <AUTHOR>
 * @version 2025.0702
 * @since 1.0.0-SNAPSHOT
 */

public interface AfterDataSaveOrUpdateHandler {
    /**
     * 处理方法
     *
     * @param formData 表单数据
     * @param type     类型
     */
    void handler(ZdPageFormData formData, SaveOrUpdateEnum type);
}
