package com.sinitek.sirm.nocode.form.listener;

import com.sinitek.sirm.nocode.form.dto.ZdExportDataTaskExecParamDTO;
import com.sinitek.sirm.nocode.form.dto.ZdExportTaskEventSourceDTO;
import com.sinitek.sirm.nocode.form.event.ExportTaskCreateEvent;
import com.sinitek.sirm.nocode.form.service.IZdExportTaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

/**
 * 监听导出任务创建事件,执行导出任务
 *
 * <AUTHOR>
 * @date 2025-07-08 13:40
 */
@Slf4j
@Component
public class ExportTaskCreateEventListener {

    @Autowired
    private IZdExportTaskService exportTaskService;

    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT, fallbackExecution = true)
    public void handleExportTaskCreateEvent(ExportTaskCreateEvent event) {
        ZdExportTaskEventSourceDTO source = event.getSource();
        Long taskId = source.getTaskId();

        log.info("监听到导出任务创建事件，开始执行导出任务，taskId: {}", taskId);
        this.exportTaskService.runExportTaskAsync(ZdExportDataTaskExecParamDTO
            .builder()
            .taskId(taskId)
            .accessToken(source.getAccessToken())
            .requestHost(source.getRequestHost())
            .build());
    }

}
