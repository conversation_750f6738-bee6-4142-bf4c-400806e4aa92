package com.sinitek.sirm.nocode.form.service.impl;

import com.sinitek.sirm.nocode.common.support.json.Base64Encode;
import com.sinitek.sirm.nocode.common.utils.ZdJsonUtil;
import com.sinitek.sirm.nocode.form.dto.ZdFormInstanceSchemaCacheDTO;
import com.sinitek.sirm.nocode.form.dto.ZdFormInstanceSchemaCreateParamDTO;
import com.sinitek.sirm.nocode.form.dto.ZdFormInstanceSchemaDiffComponentItemDTO;
import com.sinitek.sirm.nocode.form.dto.ZdFormInstanceSchemaUpdateParamDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.context.ActiveProfiles;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 页面实例Schema缓存服务单元测试
 * 
 * <AUTHOR>
 * @date 2025-08-15 15:30
 */
@ExtendWith(MockitoExtension.class)
@ActiveProfiles("unit-test")
class ZdPageFormInstanceSchemaServiceImplTest {

    @InjectMocks
    private ZdPageFormInstanceSchemaServiceImpl schemaService;

    private ZdFormInstanceSchemaCreateParamDTO createParam;
    private ZdFormInstanceSchemaUpdateParamDTO updateParam;
    private Map<String, Object> testSchema;

    @BeforeEach
    void setUp() {
        // 准备测试数据
        testSchema = createTestSchema();
        
        // 创建参数
        createParam = new ZdFormInstanceSchemaCreateParamDTO();
        String schemaJson = ZdJsonUtil.toJson(testSchema);
        createParam.setSchema(Base64Encode.encode(schemaJson));
        
        // 更新参数
        updateParam = new ZdFormInstanceSchemaUpdateParamDTO();
        updateParam.setSchemaId("test_schema_id");
        
        List<ZdFormInstanceSchemaDiffComponentItemDTO> diffList = createTestDiffList();
        String diffJson = ZdJsonUtil.toJson(diffList);
        updateParam.setDiff(Base64Encode.encode(diffJson));
    }

    @Test
    void testCreateSchemaCache_Success() {
        // 执行测试
        String schemaId = schemaService.createSchemaCache(createParam);
        
        // 验证结果
        assertNotNull(schemaId);
        assertTrue(schemaId.startsWith("schema_"));
        assertTrue(schemaId.length() > 7); // "schema_" + UUID
    }

    @Test
    void testCreateSchemaCache_NullParam() {
        // 测试空参数
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class, 
            () -> schemaService.createSchemaCache(null)
        );
        assertEquals("Schema参数不能为空", exception.getMessage());
    }

    @Test
    void testCreateSchemaCache_EmptySchema() {
        // 测试空Schema
        ZdFormInstanceSchemaCreateParamDTO emptyParam = new ZdFormInstanceSchemaCreateParamDTO();
        emptyParam.setSchema("");
        
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class, 
            () -> schemaService.createSchemaCache(emptyParam)
        );
        assertEquals("Schema参数不能为空", exception.getMessage());
    }

    @Test
    void testDeleteSchemaCache_Success() {
        // 测试删除缓存（由于使用了@CacheEvict注解，这里主要测试方法不抛异常）
        assertDoesNotThrow(() -> schemaService.deleteSchemaCache("test_schema_id"));
    }

    @Test
    void testDeleteSchemaCache_EmptySchemaId() {
        // 测试空schemaId（应该不抛异常，只是记录警告日志）
        assertDoesNotThrow(() -> schemaService.deleteSchemaCache(""));
        assertDoesNotThrow(() -> schemaService.deleteSchemaCache(null));
    }

    @Test
    void testGetSchemaCache_EmptySchemaId() {
        // 测试空schemaId
        ZdFormInstanceSchemaCacheDTO result = schemaService.getSchemaCache("");
        assertNull(result);
        
        result = schemaService.getSchemaCache(null);
        assertNull(result);
    }

    @Test
    void testUpdateSchemaCache_NullParam() {
        // 测试空参数
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class, 
            () -> schemaService.updateSchemaCache(null)
        );
        assertEquals("schemaId和diff参数不能为空", exception.getMessage());
    }

    @Test
    void testUpdateSchemaCache_EmptySchemaId() {
        // 测试空schemaId
        ZdFormInstanceSchemaUpdateParamDTO emptyParam = new ZdFormInstanceSchemaUpdateParamDTO();
        emptyParam.setSchemaId("");
        emptyParam.setDiff("test_diff");
        
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class, 
            () -> schemaService.updateSchemaCache(emptyParam)
        );
        assertEquals("schemaId和diff参数不能为空", exception.getMessage());
    }

    @Test
    void testUpdateSchemaCache_EmptyDiff() {
        // 测试空diff
        ZdFormInstanceSchemaUpdateParamDTO emptyParam = new ZdFormInstanceSchemaUpdateParamDTO();
        emptyParam.setSchemaId("test_schema_id");
        emptyParam.setDiff("");
        
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class, 
            () -> schemaService.updateSchemaCache(emptyParam)
        );
        assertEquals("schemaId和diff参数不能为空", exception.getMessage());
    }

    /**
     * 创建测试Schema数据
     */
    private Map<String, Object> createTestSchema() {
        Map<String, Object> schema = new HashMap<>();
        schema.put("version", "1.0");
        schema.put("title", "测试表单");
        
        List<Map<String, Object>> components = new ArrayList<>();
        
        // 添加测试组件
        Map<String, Object> component1 = new HashMap<>();
        component1.put("id", "644762e5");
        component1.put("ref", "ZDNumber_4333");
        component1.put("componentName", "ZDNumber");
        
        Map<String, Object> props1 = new HashMap<>();
        props1.put("label", "数值");
        props1.put("rules", new ArrayList<>());
        props1.put("state", "normal");
        component1.put("props", props1);
        component1.put("hidden", false);
        component1.put("children", new ArrayList<>());
        
        components.add(component1);
        schema.put("components", components);
        
        return schema;
    }

    /**
     * 创建测试diff数据
     */
    private List<ZdFormInstanceSchemaDiffComponentItemDTO> createTestDiffList() {
        List<ZdFormInstanceSchemaDiffComponentItemDTO> diffList = new ArrayList<>();
        
        ZdFormInstanceSchemaDiffComponentItemDTO diffItem = new ZdFormInstanceSchemaDiffComponentItemDTO();
        diffItem.setType("component");
        
        Map<String, Object> data = new HashMap<>();
        data.put("id", "644762e5");
        data.put("ref", "ZDNumber_4333");
        
        Map<String, Object> props = new HashMap<>();
        props.put("label", "更新后的数值");
        props.put("rules", new ArrayList<>());
        props.put("state", "disabled");
        data.put("props", props);
        
        data.put("hidden", true);
        data.put("children", new ArrayList<>());
        data.put("componentName", "ZDNumber");
        
        diffItem.setData(data);
        diffList.add(diffItem);
        
        return diffList;
    }
}

// 注意：由于当前的实现使用了Spring Cache注解，
// 在实际的集成测试中需要配置缓存管理器才能完整测试缓存功能
