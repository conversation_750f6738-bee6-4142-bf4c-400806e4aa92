package com.sinitek.sirm.nocode.form.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sinitek.sirm.framework.frontend.support.RequestResult;
import com.sinitek.sirm.nocode.common.support.json.Base64Encode;
import com.sinitek.sirm.nocode.common.utils.ZdJsonUtil;
import com.sinitek.sirm.nocode.form.dto.ZdFormInstanceSchemaCacheDTO;
import com.sinitek.sirm.nocode.form.dto.ZdFormInstanceSchemaCreateParamDTO;
import com.sinitek.sirm.nocode.form.dto.ZdFormInstanceSchemaUpdateParamDTO;
import com.sinitek.sirm.nocode.form.service.IZdPageFormInstanceSchemaService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;

import java.util.*;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 页面实例Schema缓存控制器单元测试
 * 
 * <AUTHOR>
 * @date 2025-08-15 15:45
 */
@ExtendWith(MockitoExtension.class)
@WebMvcTest(ZdPageFormInstanceSchemaController.class)
@ActiveProfiles("unit-test")
class ZdPageFormInstanceSchemaControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @MockBean
    private IZdPageFormInstanceSchemaService schemaService;

    private ZdFormInstanceSchemaCreateParamDTO createParam;
    private ZdFormInstanceSchemaUpdateParamDTO updateParam;
    private ZdFormInstanceSchemaCacheDTO cacheDTO;

    @BeforeEach
    void setUp() {
        // 准备测试数据
        Map<String, Object> testSchema = createTestSchema();
        
        // 创建参数
        createParam = new ZdFormInstanceSchemaCreateParamDTO();
        String schemaJson = ZdJsonUtil.toJson(testSchema);
        createParam.setSchema(Base64Encode.encode(schemaJson));
        
        // 更新参数
        updateParam = new ZdFormInstanceSchemaUpdateParamDTO();
        updateParam.setSchemaId("test_schema_id");
        updateParam.setDiff(Base64Encode.encode("[]"));
        
        // 缓存DTO
        cacheDTO = new ZdFormInstanceSchemaCacheDTO();
        cacheDTO.setSchemaId("test_schema_id");
        cacheDTO.setSchema(testSchema);
        cacheDTO.setCreateTime(new Date());
        cacheDTO.setUpdateTime(new Date());
    }

    @Test
    void testSaveInstanceSchema_Success() throws Exception {
        // Mock服务方法
        when(schemaService.createSchemaCache(any(ZdFormInstanceSchemaCreateParamDTO.class)))
            .thenReturn("test_schema_id");

        // 执行请求
        mockMvc.perform(post("/frontend/api/nocode/runtime/save-instance-schema")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(createParam)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").value("test_schema_id"));

        // 验证服务方法被调用
        verify(schemaService, times(1)).createSchemaCache(any(ZdFormInstanceSchemaCreateParamDTO.class));
    }

    @Test
    void testSaveInstanceSchema_ServiceException() throws Exception {
        // Mock服务方法抛异常
        when(schemaService.createSchemaCache(any(ZdFormInstanceSchemaCreateParamDTO.class)))
            .thenThrow(new RuntimeException("创建失败"));

        // 执行请求
        mockMvc.perform(post("/frontend/api/nocode/runtime/save-instance-schema")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(createParam)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("保存失败：创建失败"));
    }

    @Test
    void testUpdateInstanceSchema_Success() throws Exception {
        // Mock服务方法
        doNothing().when(schemaService).updateSchemaCache(any(ZdFormInstanceSchemaUpdateParamDTO.class));

        // 执行请求
        mockMvc.perform(post("/frontend/api/nocode/runtime/update-instance-schema")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(updateParam)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));

        // 验证服务方法被调用
        verify(schemaService, times(1)).updateSchemaCache(any(ZdFormInstanceSchemaUpdateParamDTO.class));
    }

    @Test
    void testUpdateInstanceSchema_ServiceException() throws Exception {
        // Mock服务方法抛异常
        doThrow(new IllegalArgumentException("参数错误"))
            .when(schemaService).updateSchemaCache(any(ZdFormInstanceSchemaUpdateParamDTO.class));

        // 执行请求
        mockMvc.perform(post("/frontend/api/nocode/runtime/update-instance-schema")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(updateParam)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("参数错误：参数错误"));
    }

    @Test
    void testRemoveInstanceSchema_Success() throws Exception {
        // Mock服务方法
        doNothing().when(schemaService).deleteSchemaCache(eq("test_schema_id"));

        // 准备请求参数
        ZdPageFormInstanceSchemaController.RemoveSchemaRequest request = 
            new ZdPageFormInstanceSchemaController.RemoveSchemaRequest();
        request.setSchemaId("test_schema_id");

        // 执行请求
        mockMvc.perform(post("/frontend/api/nocode/runtime/remove-instance-schema")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));

        // 验证服务方法被调用
        verify(schemaService, times(1)).deleteSchemaCache(eq("test_schema_id"));
    }

    @Test
    void testGetInstanceSchema_Success() throws Exception {
        // Mock服务方法
        when(schemaService.getSchemaCache(eq("test_schema_id"))).thenReturn(cacheDTO);

        // 执行请求
        mockMvc.perform(get("/frontend/api/nocode/runtime/get-instance-schema")
                .param("schemaId", "test_schema_id"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.schemaId").value("test_schema_id"));

        // 验证服务方法被调用
        verify(schemaService, times(1)).getSchemaCache(eq("test_schema_id"));
    }

    @Test
    void testGetInstanceSchema_NotFound() throws Exception {
        // Mock服务方法返回null
        when(schemaService.getSchemaCache(eq("not_exist_id"))).thenReturn(null);

        // 执行请求
        mockMvc.perform(get("/frontend/api/nocode/runtime/get-instance-schema")
                .param("schemaId", "not_exist_id"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("未找到对应的Schema"));
    }

    /**
     * 创建测试Schema数据
     */
    private Map<String, Object> createTestSchema() {
        Map<String, Object> schema = new HashMap<>();
        schema.put("version", "1.0");
        schema.put("title", "测试表单");
        
        List<Map<String, Object>> components = new ArrayList<>();
        
        Map<String, Object> component = new HashMap<>();
        component.put("id", "644762e5");
        component.put("ref", "ZDNumber_4333");
        component.put("componentName", "ZDNumber");
        
        Map<String, Object> props = new HashMap<>();
        props.put("label", "数值");
        props.put("state", "normal");
        component.put("props", props);
        
        components.add(component);
        schema.put("components", components);
        
        return schema;
    }
}
