package com.sinitek.sirm.nocode.app.controller;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.sinitek.sirm.framework.frontend.support.RequestResult;
import com.sinitek.sirm.nocode.app.constant.AppConstant;
import com.sinitek.sirm.nocode.app.dto.ZdAppAiSettingBatchDTO;
import com.sinitek.sirm.nocode.app.dto.ZdAppAiSettingDTO;
import com.sinitek.sirm.nocode.app.service.IZdAppAiSettingService;
import com.sinitek.sirm.nocode.common.annotation.ApiEnumProperty;
import com.sinitek.sirm.nocode.common.dto.ZdOptionDTO;
import com.sinitek.sirm.nocode.common.dto.base.ZdIdDTO;
import com.sinitek.sirm.nocode.llm.config.AiModelConfig;
import com.sinitek.sirm.nocode.llm.enumerate.AiSourceEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 应用AI设置Controller
 *
 * <AUTHOR>
 * @version 2025.0729
 * @description 针对表【zd_app_ai_setting(应用AI设置)】的控制器
 * @since 1.0.0-SNAPSHOT
 */
@Slf4j
@RestController
@Api(value = "/frontend/api/nocode/app/ai-setting", tags = "应用ai设置接口")
@RequestMapping("/frontend/api/nocode/app/ai-setting")
@Validated
public class ZdAppAiSettingController {

    @Resource
    private IZdAppAiSettingService zdAppAiSettingService;


    @ApiOperation(value = "新增或者修改dify应用")
    @PostMapping("/save-or-update-dify")
    public RequestResult<Long> saveOrUpdateDify(
            @ApiParam(value = "创建ai应用时的参数", required = true)
            @Valid
            @RequestBody ZdAppAiSettingDTO settingSaveDTO) {
        settingSaveDTO.setAiSource(AiSourceEnum.DIFY);
        return new RequestResult<>(zdAppAiSettingService.saveOrUpdate(settingSaveDTO));
    }

    @ApiOperation(value = "批量新增或者修改dify应用")
    @PostMapping("/save-or-update-dify-batch")
    public RequestResult<Boolean> saveOrUpdateDifyBatch(
            @ApiParam(value = "批量新增或者修改dify应用的参数", required = true)
            @Valid
            @RequestBody ZdAppAiSettingBatchDTO settingSaveDTO) {
        List<ZdAppAiSettingDTO> list = settingSaveDTO.getList();
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(a -> a.setAiSource(AiSourceEnum.DIFY));
        }
        return new RequestResult<>(zdAppAiSettingService.saveOrUpdate(settingSaveDTO));
    }

    @ApiOperation(value = "获取dify应用列表")
    @GetMapping("/list-dify")
    public RequestResult<List<ZdAppAiSettingDTO>> list(
            @ApiParam(name = AppConstant.APP_CODE, value = "应用编码", example = "app_48539ab4e34f478289d85a691e37661b", required = true)
            @RequestParam(AppConstant.APP_CODE) String appCode
    ) {
        return new RequestResult<>(zdAppAiSettingService.findByAppCode(appCode, AiSourceEnum.DIFY));
    }


    @ApiOperation(value = "获取类型列表")
    @GetMapping("/type-list")
    public RequestResult<List<ZdOptionDTO<String>>> typeList(
            @ApiEnumProperty(required = true)
            @RequestParam("source") AiSourceEnum source
    ) {
        return new RequestResult<>(zdAppAiSettingService.typeList(source));
    }

    @ApiOperation(value = "删除ai应用")
    @PostMapping("/delete")
    public RequestResult<Boolean> delete(
            @ApiParam(value = "主键", required = true)
            @Validated(value = ZdIdDTO.Update.class)
            @RequestBody ZdIdDTO idDTO) {
        return new RequestResult<>(zdAppAiSettingService.deleteById(idDTO.getId()));
    }


    /**
     * 获取模型配置
     *
     * <p>返回当前模型的配置信息，包括模型名称、模型类型、模型应用等。</p>
     *
     * @return 模型配置信息
     */
    @ApiOperation(value = "模型配置")
    @GetMapping("/config")
    public RequestResult<AiModelConfig> getConfig(
            @ApiParam(name = AppConstant.APP_CODE, value = "应用编码", example = "app_48539ab4e34f478289d85a691e37661b", required = true)
            @RequestParam(AppConstant.APP_CODE) String appCode
    ) {
        return new RequestResult<>(zdAppAiSettingService.getConfig(appCode));
    }


}
