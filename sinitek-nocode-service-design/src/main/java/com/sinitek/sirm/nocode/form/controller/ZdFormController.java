package com.sinitek.sirm.nocode.form.controller;

import com.sinitek.sirm.common.user.factory.CurrentUserFactory;
import com.sinitek.sirm.common.utils.JsonUtil;
import com.sinitek.sirm.framework.frontend.support.RequestResult;
import com.sinitek.sirm.nocode.appmanager.aspect.ZdAppRight;
import com.sinitek.sirm.nocode.common.dto.ZdCodeDTO;
import com.sinitek.sirm.nocode.common.dto.base.ZdIdDTO;
import com.sinitek.sirm.nocode.form.constant.FormConstant;
import com.sinitek.sirm.nocode.form.dto.ZdFormButtonsSupportDTO;
import com.sinitek.sirm.nocode.form.dto.ZdFormDataComponentConfigDTO;
import com.sinitek.sirm.nocode.form.dto.ZdPageFormDTO;
import com.sinitek.sirm.nocode.form.dto.ZdPageFormHistoryDTO;
import com.sinitek.sirm.nocode.form.dto.ZdPageFormShowConfigDTO;
import com.sinitek.sirm.nocode.form.dto.ZdPageFormTransDTO;
import com.sinitek.sirm.nocode.form.dto.ZdPageFormUpdateDTO;
import com.sinitek.sirm.nocode.form.service.IZdPageFormConfigService;
import com.sinitek.sirm.nocode.form.service.IZdPageFormFieldMappingService;
import com.sinitek.sirm.nocode.form.service.IZdPageFormHistoryService;
import com.sinitek.sirm.nocode.form.service.IZdPageFormService;
import com.sinitek.sirm.nocode.form.service.IZdPageFormShowConfigService;
import com.sinitek.sirm.nocode.page.service.IZdPageAuthService;
import com.sinitek.sirm.nocode.page.service.IZdPageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 2025.0312
 * @since 1.0.0-SNAPSHOT
 */
@Slf4j
@RestController
@Api(value = "/frontend/api/nocode/form", tags = "表单管理")
@RequestMapping("/frontend/api/nocode/form")
@RequiredArgsConstructor
public class ZdFormController {


    private final IZdPageFormConfigService pageFormConfigService;
    private final IZdPageFormShowConfigService pageFormShowConfigService;
    private final IZdPageFormHistoryService pageFormHistoryService;
    private final IZdPageFormService pageFormService;
    private final IZdPageAuthService pageAuthService;
    private final IZdPageService pageService;
    private final IZdPageFormFieldMappingService formFieldMappingService;


    /**
     * 保存或者修改表单
     *
     * @param pageForm 表单数据
     * @return 保存结果
     */
    @ZdAppRight(value = "@appRightEl.getAppCodeByPageCode(#p0.code)")
    @ApiOperation(value = "保存或者修改表单")
    @PostMapping("/save-or-update")
    public RequestResult<Boolean> saveOrUpdate(
            @ApiParam(name = "表单参数", value = "表单参数")
            @Valid
            @RequestBody ZdPageFormUpdateDTO pageForm
    ) {
        return new RequestResult<>(pageFormService.saveOrUpdate(pageForm));
    }


    @ZdAppRight(value = "@appRightEl.getAppCodeByPageCode(#p0.code)")
    @ApiOperation(value = "发布表单")
    @PostMapping("/publish")
    public RequestResult<Void> publish(
            @ApiParam(name = "表单编码", value = "表单编码")
            @Valid
            @RequestBody ZdCodeDTO codeDTO
    ) {
        pageFormService.publish(codeDTO.getCode());
        return new RequestResult<>();
    }


    @ZdAppRight(value = "@appRightEl.getAppCodeByPageCode(#p0.code)")
    @ApiOperation(value = "立即生效")
    @PostMapping("/effect-version")
    public RequestResult<Void> effectVersion(
            @ApiParam(name = "表单实体DTO", value = "表单实体DTO")
            @RequestBody ZdPageFormDTO zdPageFormDTO
    ) {
        pageFormService.effectVersion(zdPageFormDTO);
        return new RequestResult<>();
    }


    @ZdAppRight(value = "@appRightEl.getAppCodeByFormId(#p0.id)")
    @ApiOperation(value = "应用历史版本")
    @PostMapping("/apply-history-version")
    public RequestResult<ZdPageFormDTO> applyHistoryVersion(
            @ApiParam(name = "表单主键", value = "表单主键DTO")
            @RequestBody ZdIdDTO idDTO
    ) {
        return new RequestResult<>(pageFormService.applyHistoryVersion(idDTO.getId()));
    }

    @ApiOperation(value = "版本历史")
    @GetMapping("/version-history")
    public RequestResult<List<ZdPageFormDTO>> versionHistory(
            @ApiParam(name = "id", value = "表单主键")
            @RequestParam("id") Long id
    ) {
        return new RequestResult<>(pageFormService.versionHistory(id));
    }

    @ApiOperation(value = "通过id获取当前表单更改历史列表（留痕）")
    @GetMapping("/update-history")
    public RequestResult<List<ZdPageFormHistoryDTO>> updateHistory(
            @ApiParam(name = "id", value = "表单主键")
            @RequestParam("id") Long id
    ) {
        return new RequestResult<>(pageFormHistoryService.findByFormId(id));
    }

    @ApiOperation(value = "通过formCode获取表单更改历史列表（留痕）")
    @GetMapping("/update-history-by-form-code")
    public RequestResult<List<ZdPageFormHistoryDTO>> updateHistory(
            @ApiParam(name = FormConstant.FORM_CODE, value = "表单编码")
            @RequestParam(FormConstant.FORM_CODE) String formCode
    ) {
        return new RequestResult<>(pageFormService.updateHistory(formCode));
    }

    @ApiOperation(value = "通过表单留痕id获取历史）")
    @GetMapping("/get-update-history-by-id")
    public RequestResult<ZdPageFormHistoryDTO> getUpdateHistoryById(
            @ApiParam(value = "表单留痕id")
            @RequestParam("id") Long id
    ) {
        return new RequestResult<>(pageFormHistoryService.getById(id));
    }


    @ZdAppRight(value = "@appRightEl.getAppCodeByFormId(#p0.id)")
    @ApiOperation(value = "更新表单版本")
    @PostMapping("/update-version")
    public RequestResult<ZdPageFormDTO> updateVersion(
            @ApiParam(name = "表单主键", value = "表单主键DTO")
            @RequestBody ZdIdDTO idDTO
    ) {
        return new RequestResult<>(pageFormService.updateVersion(idDTO.getId()));
    }

    @ApiOperation(value = "预览表单", notes = "获取最新的数据（未发布的，已经发布的）")
    @GetMapping("/view")
    public RequestResult<ZdPageFormDTO> view(
            @ApiParam(name = "表单编码", example = "page_123456789", required = true)
            @RequestParam(name = FormConstant.FORM_CODE) String formCode
    ) {
        ZdPageFormDTO view = pageFormService.view(formCode);
        if (Objects.nonNull(view)) {
            view.setName(pageService.getNameByCode(formCode));
            view.setPageType(pageService.getPageTypeByCode(formCode));
        }
        return new RequestResult<>(view);
    }

    @ApiOperation(value = "根据表单code获取发布的表单")
    @GetMapping("/get-published-form")
    public RequestResult<ZdPageFormDTO> getPublishedForm(
            @ApiParam(name = "表单编码", example = "page_123456789", required = true)
            @RequestParam(name = FormConstant.FORM_CODE) String formCode
    ) {
        ZdPageFormDTO publishedForm = pageFormService.getPublishedForm(formCode);
        if (Objects.nonNull(publishedForm)) {
            publishedForm.setPageType(pageService.getPageTypeByCode(formCode));
        }
        return new RequestResult<>(publishedForm);
    }

    @ApiOperation(value = "根据表单code获取表单字段列表")
    @GetMapping("/get-form-page-data")
    public RequestResult<ZdPageFormTransDTO> getFormPageData(
            @ApiParam(name = "表单编码", example = "page_123456789", required = true)
            @RequestParam(name = FormConstant.FORM_CODE) String formCode
    ) {
        return new RequestResult<>(pageFormService.getFormPageData(formCode));
    }

    @ApiOperation(value = "获取表单按钮权限")
    @GetMapping("/get-manage-form-buttons")
    public RequestResult<ZdFormButtonsSupportDTO> getManageFormButtons(
            @ApiParam(value = "表单编码", name = FormConstant.FORM_CODE)
            @RequestParam(FormConstant.FORM_CODE) String formCode
    ) {
        return new RequestResult<>(pageAuthService.getFormButtonsSupport(formCode, CurrentUserFactory.getOrgId()));
    }

    @ApiOperation(value = "获取当前登陆人字段配置")
    @GetMapping("/get-form-data-manage-setting")
    public RequestResult<List<ZdFormDataComponentConfigDTO>> getFormDataManageSetting(
            @ApiParam(value = "表单编码", name = FormConstant.FORM_CODE)
            @RequestParam(FormConstant.FORM_CODE) String formCode
    ) {
        return new RequestResult<>(pageFormShowConfigService.findShowConfigFormCode(formCode, CurrentUserFactory.getOrgId()));
    }


    @ApiOperation(value = "保存个性化字段配置")
    @PostMapping("/save/filed/config")
    public RequestResult<Void> saveFiledConfig(
            @RequestBody
            @ApiParam(value = "表单数据显示字段个性化配置参数", name = "表单数据显示字段个性化配置参数")
            List<ZdFormDataComponentConfigDTO> configDTOList,
            @ApiParam(value = "表单编码", name = FormConstant.FORM_CODE)
            @RequestParam(FormConstant.FORM_CODE) String formCode
    ) {

        ZdPageFormShowConfigDTO zdPageFormShowConfigDTO = new ZdPageFormShowConfigDTO();
        zdPageFormShowConfigDTO.setFormCode(formCode);
        zdPageFormShowConfigDTO.setOrgId(CurrentUserFactory.getOrgId());
        zdPageFormShowConfigDTO.setShowConfig(JsonUtil.toJsonString(configDTOList));
        pageFormShowConfigService.saveOrUpdate(zdPageFormShowConfigDTO);
        return new RequestResult<>();
    }


    @ApiOperation(value = "获取全局剩余可建表单数")
    @GetMapping("/get/idle-table-length")
    public RequestResult<Integer> getIdleTableLength() {
        return new RequestResult<>(pageFormConfigService.getIdleTableLength());
    }

    @ApiOperation(value = "获取字段映射code")
    @GetMapping("/get-field-mapping-code")
    public RequestResult<Map<String, String>> getFieldMappingCode(@ApiParam(value = "表单编码", name = FormConstant.FORM_CODE)
                                                                  @RequestParam(FormConstant.FORM_CODE) String formCode) {
        return new RequestResult<>(formFieldMappingService.getFieldMappingCode(formCode));
    }
}
