package com.sinitek.sirm.nocode.page.job;

import com.sinitek.sirm.nocode.page.service.IZdPageSceneTaskService;
import lombok.RequiredArgsConstructor;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.Scheduled;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 2025.0725
 * @since 1.0.0-SNAPSHOT
 */
@ConditionalOnProperty(prefix = "nocode", name = "page-scene-task", havingValue = "true", matchIfMissing = true)
@RequiredArgsConstructor
@Configuration
public class ZdPageSceneTaskJob {
    @Resource
    private IZdPageSceneTaskService taskService;

    /**
     * 定时发送每日报告任务（每天18:00执行）
     *
     * <p>该方法执行以下主要步骤：</p>
     * <ol>
     *     <li>查询当日需发送的报告场景</li>
     *     <li>构造表单报告链接</li>
     *     <li>构建并发送消息通知</li>
     * </ol>
     *
     * @implNote 未配置接收人时记录日志并跳过发送
     */
    @Scheduled(cron = "${nocode.report.send-time:0 0 18 * * *}")
    public void report() {
        taskService.report();
    }


    /**
     * 定时任务
     */
    @Scheduled(fixedDelay = 60_000) // 每分钟检查一次
    public void notice() {
        taskService.notice();
    }

}
