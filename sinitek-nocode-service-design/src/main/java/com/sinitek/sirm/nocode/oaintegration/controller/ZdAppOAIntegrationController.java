package com.sinitek.sirm.nocode.oaintegration.controller;

import com.sinitek.sirm.framework.frontend.support.RequestResult;
import com.sinitek.sirm.nocode.app.constant.AppConstant;
import com.sinitek.sirm.nocode.app.dto.ZdAppOaIntegrationDTO;
import com.sinitek.sirm.nocode.app.enumerate.PlatformTypeEnum;
import com.sinitek.sirm.nocode.app.service.IZdAppOaIntegrationService;
import com.sinitek.sirm.nocode.appmanager.aspect.ZdAppRight;
import com.sinitek.sirm.nocode.common.annotation.ApiEnumProperty;
import com.sinitek.sirm.nocode.common.dto.ModelPropertyDTO;
import com.sinitek.sirm.nocode.common.utils.SwaggerUtil;
import com.sinitek.sirm.nocode.message.dingtalk.dto.DingDingGetAccessTokenRequestDTO;
import com.sinitek.sirm.wxwork.config.WxCpProperties;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2025.0312
 * @since 1.0.0-SNAPSHOT
 * 应用集成接口
 */
@Slf4j
@RestController
@Api(value = "/frontend/api/nocode/app-oa-integration", tags = "应用OA集成接口")
@RequestMapping("/frontend/api/nocode/app-oa-integration")
public class ZdAppOAIntegrationController {
    @Resource
    private IZdAppOaIntegrationService zdAppOaIntegrationService;


    /**
     * 保存或者修改应用OA集成接口
     *
     * @param zdAppOaIntegrationDTO 应用集成参数
     * @return 是否保存成功
     */
    @ZdAppRight(AppConstant.APP_CODE_PARAM)
    @ApiOperation(value = "保存或者修改应用OA集成接口")
    @PostMapping("/save-or-update")
    public RequestResult<Boolean> saveOrUpdate(
            @ApiParam(name = "保存应用OA集成DTO", value = "保存应用OA集成")
            @RequestBody ZdAppOaIntegrationDTO zdAppOaIntegrationDTO
    ) {
        return new RequestResult<>(zdAppOaIntegrationService.saveOrUpdate(zdAppOaIntegrationDTO));
    }


    @ApiOperation(value = "不同平台参数不一样，因此需要此接口来显示不同的参数")
    @GetMapping(path = "/platform/param")
    public RequestResult<List<ModelPropertyDTO>> platformParam(
            @ApiEnumProperty(required = true, example = "0")
            @RequestParam("platform") PlatformTypeEnum platform
    ) {
        if (platform == PlatformTypeEnum.WE_COM) {
            return new RequestResult<>(SwaggerUtil.modelProperty(WxCpProperties.AppConfig.class));
        } else if (platform == PlatformTypeEnum.DING) {
            return new RequestResult<>(SwaggerUtil.modelProperty(DingDingGetAccessTokenRequestDTO.class));
        }
        return null;
    }
}
