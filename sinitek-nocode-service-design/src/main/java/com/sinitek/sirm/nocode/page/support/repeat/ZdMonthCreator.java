package com.sinitek.sirm.nocode.page.support.repeat;

import com.sinitek.sirm.nocode.common.enumerate.YesOrNoEnum;
import com.sinitek.sirm.nocode.common.utils.ConvertUtil;
import com.sinitek.sirm.nocode.page.entity.ZdPageScene;
import com.sinitek.sirm.nocode.page.support.repeat.base.ZdRepeatCreator;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 2025.0813
 * @since 1.0.0-SNAPSHOT
 */
@Component
public class ZdMonthCreator extends ZdRepeatCreator {
    @Override
    public LocalDate next(LocalDate localDate, ZdPageScene zdPageScene) {
        if (Objects.isNull(localDate)) {
            return next(LocalDate.now(), zdPageScene);
        }
        Integer monthDay = ConvertUtil.nullDefault(zdPageScene.getMonthDay(), 1);
        while (monthDay != localDate.getDayOfMonth()) {
            localDate = localDate.plusDays(1);
        }
        YesOrNoEnum holidayStrategy = zdPageScene.getHolidayStrategy();
        if (Objects.equals(holidayStrategy, YesOrNoEnum.YES)) {
            return adjustForHolidays(localDate);
        }
        return localDate;
    }


}
