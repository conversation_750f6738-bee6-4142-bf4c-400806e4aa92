package com.sinitek.sirm.nocode.page.support.repeat.base;

import com.sinitek.sirm.nocode.page.entity.ZdPageScene;

import javax.annotation.Resource;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @version 2025.0813
 * @since 1.0.0-SNAPSHOT
 */

public abstract class ZdRepeatCreator {
    @Resource
    protected HolidaysCacheService holidaysCacheService;

    public abstract LocalDate next(LocalDate localDate, ZdPageScene zdPageScene);

    /**
     * 调整日期以避开节假日（最多检查10天）
     *
     * @param date 需要检查的日期
     * @return 调整后的日期
     */
    protected LocalDate adjustForHolidays(LocalDate date) {
        LocalDate adjustedDate = date;
        boolean isHolidays = true;
        while (isHolidays) {
            isHolidays = holidaysCacheService.checkHolidays(adjustedDate);
            if (isHolidays) {
                // 如果是节假日，延后一天
                adjustedDate = adjustedDate.plusDays(1);
            }
        }
        return adjustedDate;
    }
}
