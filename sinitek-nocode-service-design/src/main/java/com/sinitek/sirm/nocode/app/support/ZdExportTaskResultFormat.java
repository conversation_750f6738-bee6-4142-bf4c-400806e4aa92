package com.sinitek.sirm.nocode.app.support;

import java.util.List;
import java.util.Objects;

import org.springframework.stereotype.Component;

import com.sinitek.sirm.framework.frontend.support.ITableResultFormat;
import com.sinitek.sirm.nocode.app.dto.ZdExportTaskSearchResultDTO;
import com.sinitek.sirm.nocode.form.enumerate.ExportOrImportTaskStatusEnum;

import cn.hutool.core.collection.CollUtil;

/**
 * 导出任务查询结果格式化类
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Component
public class ZdExportTaskResultFormat implements ITableResultFormat<ZdExportTaskSearchResultDTO> {

  @Override
  public List<ZdExportTaskSearchResultDTO> format(List<ZdExportTaskSearchResultDTO> data) {
    if (CollUtil.isEmpty(data)) {
      return data;
    }

    data.forEach(item -> {
      // 格式化状态名称
      if (Objects.nonNull(item.getStatus())) {
        ExportOrImportTaskStatusEnum statusEnum =
            ExportOrImportTaskStatusEnum.fromValue(item.getStatus());
        if (Objects.nonNull(statusEnum)) {
          item.setStatusName(statusEnum.getLabel());
        }
      }

      // 设置操作人姓名 (暂时使用操作人ID，后续可以通过用户服务获取真实姓名)
      if (Objects.nonNull(item.getOperatorId())) {
        item.setOperatorName(item.getOperatorId());
      }
    });

    return data;
  }
}
