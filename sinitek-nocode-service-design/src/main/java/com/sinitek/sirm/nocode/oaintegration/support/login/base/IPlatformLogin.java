package com.sinitek.sirm.nocode.oaintegration.support.login.base;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @version 2025.0425
 * @description 平台登陆
 * @since 1.0.0-SNAPSHOT
 */
public interface IPlatformLogin {
    /**
     * 登陆
     *
     * @param request  请求
     * @param response 响应
     * @param param    参数
     * @param code     授权码
     * @param appCode  应用编码
     * @return 登陆结果
     */
    String login(HttpServletRequest request, HttpServletResponse response, String param, String code, String appCode);

    /**
     * 登陆
     *
     * @param request  请求
     * @param response 响应
     * @return 登陆结果
     */
    String login(HttpServletRequest request, HttpServletResponse response);
}
