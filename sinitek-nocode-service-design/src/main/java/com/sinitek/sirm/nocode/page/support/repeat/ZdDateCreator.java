package com.sinitek.sirm.nocode.page.support.repeat;

import com.sinitek.sirm.nocode.common.enumerate.YesOrNoEnum;
import com.sinitek.sirm.nocode.page.entity.ZdPageScene;
import com.sinitek.sirm.nocode.page.support.repeat.base.ZdRepeatCreator;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 2025.0813
 * @since 1.0.0-SNAPSHOT
 */
@Component
public class ZdDateCreator extends ZdRepeatCreator {
    @Override
    public LocalDate next(LocalDate localDate, ZdPageScene zdPageScene) {
        if (Objects.isNull(localDate)) {
            // 设置为当天的前一天
            localDate = LocalDate.now().minusDays(1);
        }
        YesOrNoEnum holidayStrategy = zdPageScene.getHolidayStrategy();
        if (Objects.equals(holidayStrategy, YesOrNoEnum.YES)) {
            return adjustForHolidays(localDate.plusDays(1));
        } else {
            return localDate.plusDays(1);
        }
    }
}
