package com.sinitek.sirm.nocode.common.config;

import com.fasterxml.jackson.core.util.JacksonFeatureSet;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.sinitek.sirm.nocode.common.support.json.CustomLocalDateDeserializer;
import com.sinitek.sirm.nocode.support.mybatis.handler.JsonbTypeHandler;
import org.springframework.boot.autoconfigure.jackson.Jackson2ObjectMapperBuilderCustomizer;
import org.springframework.context.annotation.Configuration;

import java.time.LocalDate;

/**
 * 自定义Jackson配置
 *
 * <AUTHOR>
 * @version 2025.0521
 * @see Jackson2ObjectMapperBuilderCustomizer
 * @since 1.0.0-SNAPSHOT
 */
@Configuration
public class CustomJackJsonConfig {
    public CustomJackJsonConfig(ObjectMapper objectMapper) {
        // java 8 时间处理器
        JavaTimeModule javaTimeModule = new JavaTimeModule();
        JacksonFeatureSet<JavaTimeFeature> javaTimeFeatureJacksonFeatureSet = JacksonFeatureSet.fromDefaults(JavaTimeFeature.values());
        javaTimeModule.addDeserializer(LocalDate.class, new CustomLocalDateDeserializer(LocalDateDeserializer.INSTANCE.withFeatures(javaTimeFeatureJacksonFeatureSet)));
        objectMapper.registerModule(javaTimeModule);
        JsonbTypeHandler.setObjectMapper(objectMapper);
    }
}
