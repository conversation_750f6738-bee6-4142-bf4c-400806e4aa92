package com.sinitek.sirm.nocode.common.config;

import org.springframework.boot.task.TaskSchedulerBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.SchedulingConfigurer;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.scheduling.config.ScheduledTaskRegistrar;

import javax.annotation.Resource;

/**
 * 调度配置
 *
 * <AUTHOR>
 * @version 2025.0514
 * @since 1.0.0-SNAPSHOT
 */

@Configuration
@EnableScheduling
public class SchedulerConfig implements SchedulingConfigurer {

    @Resource
    private TaskSchedulerBuilder builder;

    @Override
    public void configureTasks(ScheduledTaskRegistrar taskRegistrar) {
        taskRegistrar.setTaskScheduler(taskScheduler());
    }


    @Bean
    public ThreadPoolTaskScheduler taskScheduler() {
        return builder.build();
    }
}
