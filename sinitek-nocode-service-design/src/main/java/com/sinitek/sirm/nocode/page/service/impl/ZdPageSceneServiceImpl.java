package com.sinitek.sirm.nocode.page.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sinitek.sirm.common.user.factory.CurrentUserFactory;
import com.sinitek.sirm.nocode.app.constant.AppConstant;
import com.sinitek.sirm.nocode.app.event.AppDeleteEvent;
import com.sinitek.sirm.nocode.common.dto.ZdOptionDTO;
import com.sinitek.sirm.nocode.common.utils.BaseEnumUtils;
import com.sinitek.sirm.nocode.page.dao.ZdPageSceneDAO;
import com.sinitek.sirm.nocode.page.dto.ZdPageAuthDTO;
import com.sinitek.sirm.nocode.page.dto.ZdPageDTO;
import com.sinitek.sirm.nocode.page.dto.ZdPageSceneDTO;
import com.sinitek.sirm.nocode.page.entity.ZdPageScene;
import com.sinitek.sirm.nocode.page.enumerate.NoteTypeEnum;
import com.sinitek.sirm.nocode.page.enumerate.PageAuthTypeEnum;
import com.sinitek.sirm.nocode.page.enumerate.SceneSubmitEnum;
import com.sinitek.sirm.nocode.page.enumerate.SceneTypeEnum;
import com.sinitek.sirm.nocode.page.event.PageCreateEvent;
import com.sinitek.sirm.nocode.page.mapper.ZdPageSceneMapper;
import com.sinitek.sirm.nocode.page.mapstruct.ZdPageSceneMapstruct;
import com.sinitek.sirm.nocode.page.service.IZdPageAuthService;
import com.sinitek.sirm.nocode.page.service.IZdPageSceneService;
import com.sinitek.sirm.nocode.support.BaseDAO;
import lombok.RequiredArgsConstructor;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2025-03-12 14:21:01
 * @description 针对表【zd_page_scene(页面场景设置)】的数据库操作Service实现
 */
@Service
@RequiredArgsConstructor
public class ZdPageSceneServiceImpl extends BaseDAO<ZdPageSceneMapper, ZdPageScene, ZdPageSceneDAO>
        implements IZdPageSceneService {
    private final IZdPageAuthService authService;
    private final ZdPageSceneMapstruct mapstruct;


    @EventListener(condition = "#appDeleteEvent.next.equals('" + AppConstant.DE_FORM + "')")
    @Transactional(rollbackFor = Exception.class)
    public void delete(AppDeleteEvent appDeleteEvent) {
        List<String> pageCodeList = appDeleteEvent.getCodeList();
        LambdaQueryWrapper<ZdPageScene> queryWrapper = pageCodeQuery(pageCodeList);
        // 删除页面场景
        dao.remove(queryWrapper);
    }


    /**
     * 创建页面后创建场景
     * 需要是创建收集表单
     *
     * @param pageCreateEvent 创建页面事件
     */
    @Transactional(rollbackFor = Exception.class)
    @EventListener(condition = "T(cn.hutool.core.util.BooleanUtil).isTrue(#pageCreateEvent.source.collectFormFlag)")
    public void createForm(PageCreateEvent pageCreateEvent) {
        ZdPageDTO source = pageCreateEvent.getSource();
        ZdPageSceneDTO pageSceneSaveDTO = new ZdPageSceneDTO();
        // 表单code
        pageSceneSaveDTO.setFormCode(source.getCode());
        // 收集表单
        pageSceneSaveDTO.setSceneType(SceneTypeEnum.COLLECTION_FORM);
        // 设置当前登陆人，为报告接收人
        List<String> reportSendOrgIds = new ArrayList<>();
        reportSendOrgIds.add(CurrentUserFactory.getOrgId());
        pageSceneSaveDTO.setReportSendOrgIds(reportSendOrgIds);
        // 默认平台站内提醒
        pageSceneSaveDTO.setNoteType(Collections.singletonList(NoteTypeEnum.SYS_REMINDER.getValue()));
        //
        pageSceneSaveDTO.setSubmitType(SceneSubmitEnum.NONE);
        // 保存数据
        saveOrUpdate(pageSceneSaveDTO);

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean saveOrUpdate(ZdPageSceneDTO pageSceneSaveDTO) {
        Long id = pageSceneSaveDTO.getId();
        ZdPageScene zdPageScene = idCheck(id);
        if (Objects.isNull(zdPageScene)) {
            String pageCode = pageSceneSaveDTO.getFormCode();
            zdPageScene = dao.getOne(pageCodeQuery(Collections.singletonList(pageCode)));
        }
        if (Objects.isNull(zdPageScene)) {
            zdPageScene = new ZdPageScene();
        }
        mapstruct.updateEntityFromDto(pageSceneSaveDTO, zdPageScene);
        SceneTypeEnum sceneType = pageSceneSaveDTO.getSceneType();
        if (Objects.equals(sceneType, SceneTypeEnum.COMMON)) {
            // 假如是通用的话，那么其他属性设置为空
            zdPageScene.setRepeatType(null);
            zdPageScene.setStartTime(null);
            zdPageScene.setEndDate(null);
            zdPageScene.setHolidayStrategy(null);
            zdPageScene.setNoteType(null);
            zdPageScene.setReportSendOrgIds(null);
        }
        return dao.saveOrUpdate(zdPageScene);

    }

    @Override
    public ZdPageSceneDTO getPageScene(String pageCode) {
        return mapstruct.toDTO(dao.getOne(pageCodeQuery(Collections.singletonList(pageCode))));
    }

    @Override
    public List<ZdOptionDTO<Integer>> sceneTypeList(String pageCode) {
        List<ZdPageAuthDTO> list = authService.list(pageCode, PageAuthTypeEnum.DATA_AUTH);
        // 只有那些有范围的才显示
        List<ZdPageAuthDTO> authDTOList = list.stream().filter(auth -> com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(auth.getMemberOrgIdList())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(authDTOList)) {
            return BaseEnumUtils.option(SceneTypeEnum.class);
        }
        List<ZdOptionDTO<Integer>> option = new ArrayList<>();
        ZdOptionDTO<Integer> optionDTO = new ZdOptionDTO<>();
        optionDTO.setName(SceneTypeEnum.COMMON.getLabel());
        optionDTO.setValue(SceneTypeEnum.COMMON.getValue());
        option.add(optionDTO);
        return option;
    }


    private LambdaQueryWrapper<ZdPageScene> pageCodeQuery(List<String> pageCodeList) {

        return eqOrIn(ZdPageScene::getPageCode, pageCodeList);
    }
}




