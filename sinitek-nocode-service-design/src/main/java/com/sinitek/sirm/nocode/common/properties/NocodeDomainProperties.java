package com.sinitek.sirm.nocode.common.properties;

import com.sinitek.sirm.common.spring.SpringFactory;
import com.sinitek.sirm.common.utils.Base64Utils;
import com.sinitek.sirm.nocode.common.config.ZdCommonConfig;
import com.sinitek.sirm.nocode.common.constant.ZdCommonConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 2025.0515
 * @since 1.0.0-SNAPSHOT
 */
@ConfigurationProperties(
        prefix = "nocode.main"
)
@Component
@Data
@ApiModel(description = "地址配置")
public class NocodeDomainProperties {

    @ApiModelProperty(value = "域名或者ip", example = "************** 或者 sirm.sinitek.com", required = true)
    private String domain;

    @ApiModelProperty(value = "协议类型", example = "http 或者 https", required = true)
    private String domainSchema;

    @ApiModelProperty(value = "端口", example = "8080")
    private String domainPort;

    @ApiModelProperty(value = "表单提交地址", example = "http://**************:8080/#/ZD/app_da24008fabc044ccb80277193a42eb03/submit/page_5d8a38d7537e4f40bfab157f23b271e1")
    private String submitUrl = "/zhida/#/ZD/${appCode}/submit/${formCode}";


    @ApiModelProperty(value = "报告地址", example = "http://**************:8080/#/ZD/app_da24008fabc044ccb80277193a42eb03/report/page_5d8a38d7537e4f40bfab157f23b271e1")
    private String reportUrl = "/zhida/#/ZD/${appCode}/receive/${formCode}";

    /**
     * 获取域名地址
     *
     * @return 地址
     */
    public String getDomainUrl() {
        return domainSchema + "://" + domain + (StringUtils.isNotBlank(domainPort) ? ":" + domainPort : "");
    }

    /**
     * 获取提交表单地址
     *
     * @param appCode  应用编码
     * @param formCode 表单编码
     * @return 提交表单地址
     */
    public String getSubmitUrl(String appCode, String formCode) {
        return submitUrl.replace("${appCode}", appCode).replace("${formCode}", formCode);
    }

    /**
     * 获取报表地址
     *
     * @param appCode  应用编码
     * @param formCode 表单编码
     * @return 报表地址
     */
    public String getReportUrl(String appCode, String formCode) {
        return reportUrl.replace("${appCode}", appCode).replace("${formCode}", formCode);
    }


    /**
     * 构建包含重定向参数的完整域名URL
     *
     * @param redirectUrl 需要编码的原始重定向URL
     * @return 包含编码后重定向参数的完整域名URL
     * @implNote 1. 获取系统配置的上下文路径
     * 2. 拼接基础重定向路径并确保以斜杠开头
     * 3. 返回包含Base64编码参数的完整URL
     * @since 1.0.0
     */
    public String redirectUrl(String redirectUrl) {
        String contextPath = SpringFactory.getBean(ZdCommonConfig.class).getContextPath();
        String url = contextPath + ZdCommonConstant.REDIRECT_URL;
        if (!url.startsWith("/")) {
            url = "/" + url;
        }
        return getDomainUrl() + url + "?redirectUrl=" + Base64Utils.encode(redirectUrl);
    }
}
