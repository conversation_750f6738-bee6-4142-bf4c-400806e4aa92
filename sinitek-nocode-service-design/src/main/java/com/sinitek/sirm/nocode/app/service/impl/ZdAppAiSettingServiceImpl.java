package com.sinitek.sirm.nocode.app.service.impl;

import cn.hutool.core.util.NumberUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.sinitek.sirm.framework.exception.BussinessException;
import com.sinitek.sirm.nocode.app.dao.ZdAppAiSettingDAO;
import com.sinitek.sirm.nocode.app.dto.ZdAppAiSettingBatchDTO;
import com.sinitek.sirm.nocode.app.dto.ZdAppAiSettingDTO;
import com.sinitek.sirm.nocode.app.entity.ZdAppAiSetting;
import com.sinitek.sirm.nocode.app.mapper.ZdAppAiSettingMapper;
import com.sinitek.sirm.nocode.app.mapstruct.ZdAppAiSettingMapstruct;
import com.sinitek.sirm.nocode.app.service.IZdAppAiSettingService;
import com.sinitek.sirm.nocode.common.dto.ZdOptionDTO;
import com.sinitek.sirm.nocode.common.utils.CopyUtil;
import com.sinitek.sirm.nocode.llm.config.AiModelConfig;
import com.sinitek.sirm.nocode.llm.enumerate.AiSourceEnum;
import com.sinitek.sirm.nocode.support.BaseDAO;
import com.sinitek.sirm.nocode.support.mybatis.conditions.query.LamWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 应用AI设置Service实现类
 *
 * <AUTHOR>
 * @version 2025.0729
 * @description 针对表【zd_app_ai_setting(应用AI设置)】的数据库操作Service实现
 * @since 1.0.0-SNAPSHOT
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ZdAppAiSettingServiceImpl extends BaseDAO<ZdAppAiSettingMapper, ZdAppAiSetting, ZdAppAiSettingDAO>
        implements IZdAppAiSettingService {

    private final AiModelConfig config;
    private final ZdAppAiSettingMapstruct mapstruct;


    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long saveOrUpdate(ZdAppAiSettingDTO settingDTO) {
        ZdAppAiSetting zdAppAiSetting = saveOrUpdate(settingDTO, true);
        if (Objects.nonNull(zdAppAiSetting)) {
            return zdAppAiSetting.getId();
        }
        return 0L;
    }


    @Override
    public boolean saveOrUpdate(ZdAppAiSettingBatchDTO batchDTO) {
        List<ZdAppAiSettingDTO> list = batchDTO.getList();
        List<Long> deleteIds = batchDTO.getDeleteIds();
        boolean flag = false;
        if (CollectionUtils.isNotEmpty(list)) {
            List<ZdAppAiSetting> collect = list.stream().map(settingDTO -> saveOrUpdate(settingDTO, false))
                    .filter(Objects::nonNull).collect(Collectors.toList());
            if (list.size() != collect.size()) {
                throw new BussinessException("参数错误！请联系管理员");
            }
            flag = dao.saveOrUpdateBatch(collect);
        }
        if (CollectionUtils.isNotEmpty(deleteIds)) {
            flag = dao.removeByIds(deleteIds);
        }
        return flag;
    }

    @Override
    public List<ZdAppAiSettingDTO> findByAppCode(String appCode) {
        LambdaQueryWrapper<ZdAppAiSetting> queryWrapper = eqOrIn(ZdAppAiSetting::getAppCode, appCode);
        return mapList(queryWrapper);
    }

    @Override
    public List<ZdAppAiSettingDTO> findByAppCode(String appCode, AiSourceEnum aiSource) {
        LambdaQueryWrapper<ZdAppAiSetting> queryWrapper = eqOrIn(ZdAppAiSetting::getAppCode, appCode)
                .eq(ZdAppAiSetting::getAiSource, aiSource);
        return mapList(queryWrapper);
    }

    @Override
    public boolean deleteById(Long id) {
        return dao.removeById(id);
    }

    @Override
    public AiModelConfig getConfig(String appCode) {
        List<ZdAppAiSettingDTO> list = findByAppCode(appCode);
        if (CollectionUtils.isNotEmpty(list)) {
            AiModelConfig aiModelConfig = CopyUtil.deepCopy(config);
            list.forEach(aiModelConfig::add);
            return aiModelConfig;
        }
        return config;
    }

    @Override
    public AiModelConfig getByAppKeyConfig(String appKey) {
        AiModelConfig.ModelAppInfo appInfo = config.findByAppKey(appKey);
        if (Objects.isNull(appInfo) && NumberUtil.isNumber(appKey)) {
            Long id = NumberUtil.parseLong(appKey, 0L);
            String appCode = stringValue(LamWrapper.eqOrIn(ZdAppAiSetting::getId, id).select(ZdAppAiSetting::getAppCode));
            return getConfig(appCode);

        } else {
            return config;
        }


    }

    @Override
    public List<ZdOptionDTO<String>> typeList(AiSourceEnum sourceEnum) {
        AiModelConfig.Model model = config.findModelByKey(sourceEnum.getValue());
        if (Objects.nonNull(model)) {
            List<AiModelConfig.ModelType> allModelTypes = model.getAllModelTypes();
            if (CollectionUtils.isEmpty(allModelTypes)) {
                allModelTypes = model.getModelTypes();
            }
            if (CollectionUtils.isNotEmpty(allModelTypes)) {
                return allModelTypes.stream().map(modelType -> new ZdOptionDTO<>(modelType.getName(), modelType.getKey())).collect(Collectors.toList());
            }
        }

        return Collections.emptyList();
    }

    private List<ZdAppAiSettingDTO> mapList(LambdaQueryWrapper<ZdAppAiSetting> queryWrapper) {
        return mapstruct.toDTOList(dao.list(queryWrapper.orderByDesc(ZdAppAiSetting::getId)));
    }

    private ZdAppAiSetting saveOrUpdate(ZdAppAiSettingDTO settingDTO, boolean update) {
        AiSourceEnum aiSource = settingDTO.getAiSource();
        String value = aiSource.getValue();
        AiModelConfig.Model model = config.findModelByKey(value);
        if (model == null) {
            log.error("未找到模型:{}", value);
            return null;
        }
        List<AiModelConfig.ModelType> allModelTypes = model.getAllModelTypes();
        if (CollectionUtils.isEmpty(allModelTypes)) {
            allModelTypes = model.getModelTypes();
        }
        if (CollectionUtils.isEmpty(allModelTypes)) {
            log.error("未找到模型类型:{}", value);
            return null;
        }
        String typeKey = settingDTO.getTypeKey();
        boolean b = allModelTypes.stream().anyMatch(modelType -> typeKey.equals(modelType.getKey()));
        if (!b) {
            log.error("未找到模型类型:{}", typeKey);
            return null;
        }
        ZdAppAiSetting entity = mapstruct.toEntity(settingDTO);
        if (update) {
            dao.saveOrUpdate(entity);
        }
        return entity;
    }


}
