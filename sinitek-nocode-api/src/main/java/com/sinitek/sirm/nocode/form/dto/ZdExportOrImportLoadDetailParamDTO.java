package com.sinitek.sirm.nocode.form.dto;

import com.sinitek.sirm.nocode.common.dto.base.ZdIdDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 2025-07-04
 * @description
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "表单数据加载参数结果")
public class ZdExportOrImportLoadDetailParamDTO extends ZdFormDataBaseModelDTO {

    @NotNull(message = "任务ID不能为空", groups = {ZdIdDTO.View.class})
    @ApiModelProperty(value = "导出任务id")
    private Long taskId;

}
