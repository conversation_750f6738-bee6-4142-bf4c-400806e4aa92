package com.sinitek.sirm.nocode.page.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.sinitek.sirm.nocode.page.enumerate.MemberTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2025.0724
 * @since 1.0.0-SNAPSHOT
 */
@Data
@ApiModel(description = "页面分享DTO")
public class ZdPageShareDTO {

    /**
     * 页面编码
     */
    @NotBlank(message = "页面编码不能为空")
    @ApiModelProperty(value = "页面编码", example = "page_114b522908e04cd09deeea7f4c25ca25", required = true)
    private String pageCode;

    @NotEmpty(message = "分享人员不能为空")
    @ApiModelProperty(value = "分享人员id数组", example = "[\"999000001\"]")
    private List<String> orgIdList;

    /**
     * 分享人员类型
     */
    @JsonIgnore
    private MemberTypeEnum memberType = MemberTypeEnum.PERSON;

    /**
     * 通知类型
     */
    @JsonIgnore
    private List<Integer> noteType;
}
