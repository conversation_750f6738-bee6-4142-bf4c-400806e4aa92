package com.sinitek.sirm.nocode.form.enumerate;

import java.util.Arrays;
import java.util.Objects;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.sinitek.sirm.nocode.common.support.enumeratebase.BaseIntegerEnum;

import lombok.Getter;

/**
 * 任务状态枚举
 * <AUTHOR>
 * @version 1.0.0
 * @since 1.0.0
 */
@Getter
public enum ExportOrImportTaskStatusEnum implements BaseIntegerEnum {
    PENDING(0, "待处理"),
    PROCESSING(1, "处理中"),
    SUCCESS(2, "处理成功"),
    FAILED(3, "处理失败");

    /**
     * 值
     */
    @JsonValue
    private final Integer value;
    /**
     * 名称
     */
    private final String label;

    ExportOrImportTaskStatusEnum(Integer value, String label) {
        this.value = value;
        this.label = label;
    }

    @JsonCreator
    public static ExportOrImportTaskStatusEnum fromValue(Integer value) {
        return Arrays.stream(ExportOrImportTaskStatusEnum.values())
                .filter(a -> Objects.equals(value, a.getValue())).findAny().orElse(null);
    }
} 