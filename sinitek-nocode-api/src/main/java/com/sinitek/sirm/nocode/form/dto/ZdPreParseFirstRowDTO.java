package com.sinitek.sirm.nocode.form.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @version 2025.07.03
 * @description 导出字段
 * @since 1.0.0
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
@ApiModel(description = "预解析第一行数据")
public class ZdPreParseFirstRowDTO {

    @ApiModelProperty(value = "字段编码")
    private String ref;

    @ApiModelProperty(value = "字段名称")
    private String label;

    @ApiModelProperty(value = "所属子表单ref")
    private String parentRef;

    @ApiModelProperty("所属子表单名称")
    private String parentLabel;
}
