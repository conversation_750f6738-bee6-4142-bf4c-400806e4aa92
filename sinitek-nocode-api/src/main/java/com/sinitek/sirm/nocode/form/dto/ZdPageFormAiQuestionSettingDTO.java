package com.sinitek.sirm.nocode.form.dto;

import com.sinitek.sirm.nocode.common.annotation.ApiEnumProperty;
import com.sinitek.sirm.nocode.common.dto.ZdKeyAndValueDTO;
import com.sinitek.sirm.nocode.form.enumerate.AiMarkdownConversionTypeEnum;
import com.sinitek.sirm.nocode.form.enumerate.AiOutFormatEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 智搭AI问答设置DTO
 *
 * <AUTHOR>
 * @version 2025.0619
 * @since 1.0.0-SNAPSHOT
 */
@Data
@ApiModel(description = "智搭AI问答设置DTO")
public class ZdPageFormAiQuestionSettingDTO {
    /**
     * 模型编码
     */
    @NotBlank(message = "模型编码")
    @Length(max = 50, message = "模型编码不能超过50个字符")
    @ApiModelProperty(value = "模型编码", example = "10828dbb-37f3-4ed8-a6ea-e2e31f6d7308", required = true)
    private String modelCode;

    /**
     * 输入内容
     */
    @NotBlank(message = "输入内容不能为空")
    @ApiModelProperty(value = "输入内容", example = "请根据以下信息生成报告", required = true)
    private String inputContent;


    /**
     * 输出内容
     */
   /* @NotBlank(message = "输入内容不能为空")
    @ApiModelProperty(value = "输出内容", example = "AI生成的回答内容")
    private String outContent;*/

    /**
     * 输入参数
     */
    @ApiModelProperty(value = "输入参数")
    private List<ZdKeyAndValueDTO> inputVar;


    /**
     * 输出格式
     */
    @NotNull(message = "输出格式不能为空")
    @ApiEnumProperty(example = "0", required = true)
    private AiOutFormatEnum outFormat;

    /**
     * 当进行markdown转换时，允许的格式
     */
    @ApiEnumProperty(enumClazz = AiMarkdownConversionTypeEnum.class, keepDataType = true, example = "[0,1]")
    private List<Integer> markdownConversionType;
}
