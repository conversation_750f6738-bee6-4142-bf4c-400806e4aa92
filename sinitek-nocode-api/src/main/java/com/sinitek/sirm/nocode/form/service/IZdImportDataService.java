package com.sinitek.sirm.nocode.form.service;

import com.sinitek.sirm.nocode.app.dto.ZdImportTaskCreateParamDTO;
import com.sinitek.sirm.nocode.app.dto.ZdImportTaskDeleteAddedParamDTO;
import com.sinitek.sirm.nocode.app.dto.ZdImportTaskRetryParamDTO;

public interface IZdImportDataService {

    void importData(ZdImportTaskCreateParamDTO param);

    void retry(ZdImportTaskRetryParamDTO param);

    void deleteAddedImportData(ZdImportTaskDeleteAddedParamDTO param);
}
