package com.sinitek.sirm.nocode.common.feign;

import com.sinitek.sirm.framework.frontend.support.RequestResult;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @version 2025.0811
 * @since 1.0.0-SNAPSHOT
 */
@FeignClient(
        name = "${sinicube.sirmapp.remote.service-name:CLOUD-SIRMAPP}",
        contextId = "userFlagTokenLoginApiService",
        url = "${sinicube.sirmapp.remote.url:}"
)
public interface IUserFlagTokenLoginApiService {
    @PostMapping({"/frontend/api/user-flag-token-login"})
    @ApiOperation("根据userFlagToken登录")
    RequestResult<String> login(@RequestBody String userFlagTokenStr);
}
