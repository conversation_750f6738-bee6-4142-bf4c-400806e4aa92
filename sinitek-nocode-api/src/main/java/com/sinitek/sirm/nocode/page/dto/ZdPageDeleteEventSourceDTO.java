package com.sinitek.sirm.nocode.page.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

/**
 * page删除事件源DTO
 *
 * @TableName zd_page
 */
@Data
@SuperBuilder
@EqualsAndHashCode
@ApiModel(description = "page删除事件源DTO")
public class ZdPageDeleteEventSourceDTO {

    @ApiModelProperty(value = "页面编码")
    private List<String> codes;

}
