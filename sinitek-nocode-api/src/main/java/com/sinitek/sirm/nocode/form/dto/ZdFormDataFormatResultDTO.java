package com.sinitek.sirm.nocode.form.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * 表单数据格式化结果数据传输对象
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@ApiModel("表单数据格式化结果数据")
public class ZdFormDataFormatResultDTO {

  /** 格式化后的值 */
  @ApiModelProperty("格式化后的值")
  private Object value;

}
