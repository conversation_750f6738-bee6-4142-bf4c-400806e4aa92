package com.sinitek.sirm.nocode.form.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @version 2025.07.03
 * @description 导出字段
 * @since 1.0.0
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "表单字段")
public class ZdImportFormHeaderFieldDTO extends ZdFormFieldBaseDTO {

    @ApiModelProperty("组件名称")
    private String componentName;

    @ApiModelProperty("所属子表单名称")
    private String parentLabel;

    @ApiModelProperty("所属子表单Ref")
    private String parentRef;

    @ApiModelProperty("是否子表单")
    private Boolean childFormItemFlag;

    @ApiModelProperty("日期格式化")
    private String formatter;

    @ApiModelProperty("选项")
    private List<Map<String, Object>> options;

}
