package com.sinitek.sirm.nocode.form.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @version 2025.07.03
 * @description 导出字段
 * @since 1.0.0
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "表单字段")
public class ZdFormFieldDTO extends ZdF<PERSON><PERSON><PERSON>BaseDTO {
    
    @ApiModelProperty(value = "子字段", example = "[]")
    private List<ZdFormFieldDTO> children;

}
