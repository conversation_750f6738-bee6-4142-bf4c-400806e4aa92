package com.sinitek.sirm.nocode.form.enumerate;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.sinitek.sirm.nocode.common.support.enumeratebase.BaseIntegerEnum;
import java.util.Arrays;
import java.util.Objects;
import lombok.Getter;

/**
 * 导入数据提交人模式
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 1.0.0
 */
@Getter
public enum ImportDataCreatorModeEnum implements BaseIntegerEnum {

    CURRENT_USER(1, "当前导入人"),
    PLANTFORM_ADMIN(2, "平台管理员"),
    CREATOR_IN_EXCEL(3, "Excel中指定提交人");

    /**
     * 值
     */
    @JsonValue
    private final Integer value;
    /**
     * 名称
     */
    private final String label;

    ImportDataCreatorModeEnum(Integer value, String label) {
        this.value = value;
        this.label = label;
    }

    @JsonCreator
    public static ImportDataCreatorModeEnum fromValue(Integer value) {
        return Arrays.stream(ImportDataCreatorModeEnum.values())
            .filter(a -> Objects.equals(value, a.getValue())).findAny().orElse(
                ImportDataCreatorModeEnum.CURRENT_USER);
    }
} 