package com.sinitek.sirm.nocode.form.dto;

import java.io.File;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "ZdGenerateImportExcelTemplateResultDTO", description = "生成导入模板结果")
public class ZdGenerateImportExcelTemplateResultDTO {

  @ApiModelProperty(value = "表单名")
  private String formName;

  @ApiModelProperty(value = "文件")
  private File file;

}
