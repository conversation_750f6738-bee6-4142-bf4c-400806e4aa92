package com.sinitek.sirm.nocode.form.dto;

import com.sinitek.sirm.nocode.form.support.FormCodeSupplier;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @version 2025.0721
 * @since 1.0.0-SNAPSHOT
 */
@ApiModel(description = "表单编码dto")
@Data
public class ZdFormCodeDTO implements FormCodeSupplier {
    /**
     * 表单编码
     */
    @ApiModelProperty(value = "表单编码", example = "page_57049cf9c4e94acfbc94d6775bc9e73e", required = true)
    @NotBlank(message = "表单编码不能为空")
    private String formCode;
}
