package com.sinitek.sirm.nocode.form.dto;

import com.sinitek.sirm.framework.frontend.dto.UploadFileDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @date 2025-07-24 10:40
 */
@Data
@SuperBuilder
@NoArgsConstructor
@ApiModel("下载资源结果")
public class ZdDownloadResourceResultDTO {

    @ApiModelProperty(value = "下载标识", notes = "null: 未开始,false: 下载失败,true: 下载成功")
    private Boolean flag;

    @ApiModelProperty("下载地址")
    private String downloadUrl;

    @ApiModelProperty("下载资源转换为上传资源")
    private UploadFileDTO uploaderFile;

    @ApiModelProperty("失败信息")
    private String errMes;
}
