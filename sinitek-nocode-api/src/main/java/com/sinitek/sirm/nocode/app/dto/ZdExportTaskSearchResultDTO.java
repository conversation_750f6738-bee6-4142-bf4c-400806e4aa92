package com.sinitek.sirm.nocode.app.dto;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.sinitek.sirm.common.utils.GlobalConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 导出任务查询结果DTO
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "导出任务查询结果DTO")
public class ZdExportTaskSearchResultDTO {

  @ApiModelProperty("任务ID")
  private Long id;

  @ApiModelProperty("文件名")
  private String fileName;

  @ApiModelProperty("文件数量")
  private Integer fileCount;

  @ApiModelProperty("状态：0-待处理，1-处理中，2-处理成功，3-处理失败")
  private Integer status;

  @ApiModelProperty("状态名称")
  private String statusName;

  @ApiModelProperty("操作人id")
  private String operatorId;

  @ApiModelProperty("操作人姓名")
  private String operatorName;

  @JsonFormat(pattern = GlobalConstant.TIME_FORMAT_THIRTEEN)
  @ApiModelProperty("操作时间")
  private Date operateTime;

  @ApiModelProperty("表单编码")
  private String formCode;
}
