package com.sinitek.sirm.nocode.common.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 2025.0714
 * @since 1.0.0-SNAPSHOT
 */
@ApiModel(
        value = "选项数据",
        description = "用于前端下拉框显示"
)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ZdOptionDTO<T> {
    @ApiModelProperty("显示名称")
    private String name;
    @ApiModelProperty("值")
    private T value;
}
