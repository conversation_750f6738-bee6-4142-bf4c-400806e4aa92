package com.sinitek.sirm.nocode.common.dto.base;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.groups.Default;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 2025.0311
 * @since 1.0.0-SNAPSHOT
 */
@Data
@ApiModel(description = "主键DTO")
public class ZdIdDTO implements Serializable {
    private static final long serialVersionUID = 8409209529938200905L;
    @ApiModelProperty("主键,更新时必须传递")
    @NotNull(
            message = "id不能为空",
            groups = {Update.class}
    )
    @Min(value = 1, message = "id不能小于1", groups = {Update.class})
    private Long id;


    public interface Save extends Default {
    }

    public interface Update extends Default {
    }

    public interface Delete extends Default {
    }

    public interface View extends Default {
    }
}
