package com.sinitek.sirm.nocode.common.utils;

import cn.hutool.core.text.CharSequenceUtil;
import com.sinitek.sirm.nocode.common.constant.ZdCommonConstant;
import com.sinitek.sirm.nocode.common.dto.ZdOptionDTO;
import com.sinitek.sirm.nocode.common.support.enumeratebase.BaseEnum;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2025.0711
 * @since 1.0.0-SNAPSHOT
 */
@Slf4j
public class BaseEnumUtils {
    
    private BaseEnumUtils() {
        throw new UnsupportedOperationException("Utility class");
    }

    @SuppressWarnings("unchecked")
    public static <T extends Serializable> List<ZdOptionDTO<T>> option(Class<?> aClass, int type) {
        if (BaseEnum.class.isAssignableFrom(aClass) && aClass.isEnum()) {
            BaseEnum<T>[] enumConstants = (BaseEnum<T>[]) aClass.getEnumConstants();
            return Arrays.stream(enumConstants)
                .filter(e -> e.findTypeWithTarget(type) == type && e.isEnable())
                .map(e -> new ZdOptionDTO<>(e.getLabel(), e.getValue()))
                .collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    public static <T extends Serializable> List<ZdOptionDTO<T>> option(Class<?> aClass) {
        return option(aClass, 0);
    }

    /**
     * 获取枚举值
     *
     * @return 枚举值
     */

    public static <T extends Serializable> List<ZdOptionDTO<T>> option(String modelName, String enumName, int type) {
        // 类首字母大写
        enumName = CharSequenceUtil.upperFirst(enumName);
        String classPath = String.format(ZdCommonConstant.ENUMERATE_CLASS, modelName, enumName);
        List<ZdOptionDTO<T>> list = new ArrayList<>();
        try {
            Class<?> aClass = Class.forName(classPath);
            return option(aClass, type);
        } catch (Exception e) {
            log.error("获取枚举值异常:{}", e.getMessage(), e);
            return list;
        }
    }
}