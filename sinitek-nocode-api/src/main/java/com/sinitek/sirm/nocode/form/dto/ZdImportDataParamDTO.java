package com.sinitek.sirm.nocode.form.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @date 2025-07-21 16:07
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
@ApiModel(description = "导入参数")
public class ZdImportDataParamDTO {

    @ApiModelProperty("导入模式")
    private Integer importMode;

    @ApiModelProperty("字段校验标识")
    private Integer fieldValidFlag;

    @ApiModelProperty("导入数据提交人模式")
    private Integer importDataCreatorMode;

    @ApiModelProperty("用户自定义导入字段")
    private Map<Integer, ZdImportFormHeaderFieldDTO> userColumnSettingField;

}
