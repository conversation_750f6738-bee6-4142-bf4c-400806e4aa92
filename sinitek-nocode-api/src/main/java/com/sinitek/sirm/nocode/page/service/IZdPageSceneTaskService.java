package com.sinitek.sirm.nocode.page.service;

import com.sinitek.sirm.nocode.page.dto.ZdPageShareDTO;
import com.sinitek.sirm.org.entity.Employee;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2025.0724
 * @since 1.0.0-SNAPSHOT
 */

public interface IZdPageSceneTaskService {
    /**
     * 分享到企微
     *
     * @param zdPageShareDTO 分享参数
     */
    void share(ZdPageShareDTO zdPageShareDTO);


    void report();

    void notice();

    List<Employee> findEmployeeByPageCode(String pageCode, LocalDate localDate);

    /**
     * 获取收集表单是否停止
     *
     * @param pageCode    页面code
     * @param reportParam 报告参数
     * @return 是否停止
     */
    boolean collectIsStop(String pageCode, String reportParam);
}
