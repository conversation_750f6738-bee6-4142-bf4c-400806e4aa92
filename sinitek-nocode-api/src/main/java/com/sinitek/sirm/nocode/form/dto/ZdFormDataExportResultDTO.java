package com.sinitek.sirm.nocode.form.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @version 2025-07-04
 * @description
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode
@SuperBuilder
@ApiModel(description = "表单数据导出结果")
public class ZdFormDataExportResultDTO {

    @ApiModelProperty(value = "导出任务id")
    private Long taskId;

    @ApiModelProperty(value = "文件名称")
    private String fileName;
}
