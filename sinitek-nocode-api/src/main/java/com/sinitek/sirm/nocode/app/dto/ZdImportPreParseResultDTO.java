package com.sinitek.sirm.nocode.app.dto;

import com.sinitek.sirm.nocode.form.dto.ZdFormFieldDTO;
import com.sinitek.sirm.nocode.form.dto.ZdImportFormHeaderFieldDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("预导入解析结果")
public class ZdImportPreParseResultDTO {
    @ApiModelProperty("表头")
    private List<ZdFormFieldDTO> excelHeader;
    @ApiModelProperty("表单字段")
    private List<ZdImportFormHeaderFieldDTO> allFields;
    @ApiModelProperty("预解析字段")
    private List<Map<String, Object>> preParsefields;
    @ApiModelProperty("解析结果")
    private List<Map<String, Object>> parseResultList;
    @ApiModelProperty("表格合并信息")
    private Map<Integer, ?> cellExtraData;
}
