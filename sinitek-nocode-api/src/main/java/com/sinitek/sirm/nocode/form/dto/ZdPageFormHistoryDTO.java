package com.sinitek.sirm.nocode.form.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.sinitek.sirm.nocode.common.dto.base.BaseAuditEntityDTO;
import com.sinitek.sirm.nocode.common.support.json.Base64Encode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Objects;

/**
 * 页面表单历史表
 *
 * @TableName zd_page_form_history
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(description = "历史表单DTO")
public class ZdPageFormHistoryDTO extends BaseAuditEntityDTO {
    /**
     * 表单配置
     */
    @JsonSerialize(using = Base64Encode.class)
    @ApiModelProperty(value = "表单配置")
    private String pageData;

    /**
     * 页面表单id
     */
    @ApiModelProperty(value = "页面表单id")
    private Long pageFormId;


    @ApiModelProperty("修改版本")
    private Integer updateVersion;


    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人名称")
    private String creator;
    /**
     * 是否发布标志
     */
    @ApiModelProperty(value = "是否发布标志")
    private Boolean publishFlag;

    /**
     * 发布状态
     */
    @ApiModelProperty(value = "发布状态")
    private Integer publishStatus;

    public Boolean getPublishFlag() {
        return Objects.equals(publishStatus, 2);
    }
}
