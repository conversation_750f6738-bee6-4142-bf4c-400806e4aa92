package com.sinitek.sirm.nocode.form.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @date 2025-07-14 08:56
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
@ApiModel(description = "导出资源任务执行参数")
public class ZdExportResourceTaskExecParamDTO {

    @ApiModelProperty(value = "任务Id")
    private Long taskId;

    @ApiModelProperty(value = "待导出数据")
    private List<ZdExportResourcesFileInfoDTO> list;

}
