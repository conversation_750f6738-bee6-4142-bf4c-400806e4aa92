package com.sinitek.sirm.nocode.common.support.coverts;

import com.sinitek.sirm.common.utils.NumberTool;
import com.sinitek.sirm.nocode.common.support.enumeratebase.BaseEnum;
import com.sinitek.sirm.nocode.common.support.enumeratebase.BaseIntegerEnum;
import com.sinitek.sirm.nocode.common.utils.ZdJsonUtil;
import org.springframework.core.convert.TypeDescriptor;
import org.springframework.core.convert.converter.GenericConverter;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.HashSet;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 2025.0428
 * @description 枚举转换器
 * @since 1.0.0-SNAPSHOT
 */
@Component
public class BaseEnumConvert<T extends Serializable, C extends BaseEnum<T>, E extends Enum<E>> implements GenericConverter {

    @Override
    public Set<ConvertiblePair> getConvertibleTypes() {
        Set<ConvertiblePair> convertiblePairs = new HashSet<>();
        convertiblePairs.add(new ConvertiblePair(String.class, BaseEnum.class));
        convertiblePairs.add(new ConvertiblePair(Integer.class, BaseEnum.class));
        return convertiblePairs;
    }

    @SuppressWarnings("unchecked")
    @Override
    public Object convert(@Nullable Object source, TypeDescriptor sourceType, TypeDescriptor targetType) {
        if (Objects.isNull(source) || "".equals(source)) {
            return null;
        }

        Class<?> type = targetType.getType();
        if (!type.isEnum()) {
            return null;
        }

        return convertEnum((Class<C>) type, source);
    }

    /**
     * 将源值转换为目标枚举类型实例
     *
     * @param enumType 目标枚举类型
     * @param source   需要转换的源值
     * @return 匹配的枚举实例或null
     */
    @SuppressWarnings("unchecked")
    private Object convertEnum(Class<C> enumType, Object source) {
        C[] enumConstants = enumType.getEnumConstants();
        if (enumConstants == null || enumConstants.length == 0) {
            return null;
        }

        /*
         * 处理整数类型转换逻辑
         * 1. 检查枚举类型是否为BaseIntegerEnum的子类
         * 2. 将源值安全转换为Integer类型
         * 3. 遍历枚举常量匹配数值
         */
        if (BaseIntegerEnum.class.isAssignableFrom(enumType)) {
            Integer intValue = NumberTool.safeToInteger(source, -1);
            for (C c : enumConstants) {
                if (Objects.equals(c.getValue(), intValue)) {
                    return c;
                }
            }
            return ZdJsonUtil.parse(ZdJsonUtil.toJson(intValue), enumType);
        }

        /*
         * 处理字符串类型转换逻辑
         * 1. 检查源值是否为字符串类型
         * 2. 遍历枚举常量匹配名称
         */
        if (source instanceof String) {
            String name = (String) source;
            for (C c : enumConstants) {
                if (Objects.equals(name, ((E) c).name())) {
                    return c;
                }
            }
            return ZdJsonUtil.parse(ZdJsonUtil.toJson(name), enumType);
        }

        return null;
    }
}