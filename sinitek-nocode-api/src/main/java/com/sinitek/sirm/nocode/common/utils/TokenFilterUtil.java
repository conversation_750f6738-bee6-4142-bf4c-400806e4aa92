package com.sinitek.sirm.nocode.common.utils;

import com.sinitek.cloud.common.dto.UserDTO;
import com.sinitek.sirm.framework.frontend.support.RequestResult;
import com.sinitek.sirm.nocode.common.feign.IRemoteOrgUserService;
import com.sinitek.sirm.nocode.common.web.RequestContext;
import com.sinitek.spirit.um.server.shiro.properties.SiniCubeShiroProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.env.Environment;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Enumeration;
import java.util.List;

/**
 * Token过滤工具类
 * <p>
 * 用于处理用户认证Token的获取和验证，支持从请求头、Cookie、请求参数等多种方式获取Token，
 * 并通过远程服务验证Token的有效性。
 * </p>
 *
 * <AUTHOR>
 * @version 2025.0731
 * @since 1.0.0-SNAPSHOT
 */

@Component
@Slf4j
public class TokenFilterUtil {

    /**
     * 需要排除的路径列表
     */
    List<String> excludePathList;

    /**
     * Shiro安全属性配置
     */
    SiniCubeShiroProperties siniCubeShiroProperties;

    /**
     * 环境变量
     */
    @Resource
    private Environment environment;

    /**
     * 远程组织用户服务接口
     */
    @Lazy
    @Resource
    private IRemoteOrgUserService remoteOrgService;

    /**
     * 需要排除的路径配置
     */
    @Value("${sirm.security.excludepath:}")
    private String excludepath;


    /**
     * Token名称
     */
    private String tokenName;

    /**
     * 构造函数，初始化TokenFilterUtil实例
     * <p>
     * 初始化排除路径列表，将配置的排除路径和默认的API文档路径添加到排除列表中
     * </p>
     *
     * @param siniCubeShiroProperties Shiro安全属性配置
     */
    public TokenFilterUtil(SiniCubeShiroProperties siniCubeShiroProperties) {
        this.siniCubeShiroProperties = siniCubeShiroProperties;
        List<String> tempExcludePathList = new ArrayList<>();
        if (StringUtils.isNotBlank(excludepath)) {
            tempExcludePathList = Arrays.asList(StringUtils.split(excludepath, ","));
        }
        if (tempExcludePathList.isEmpty()) {
            tempExcludePathList = new ArrayList<>();
        }

        tempExcludePathList.add("/v2/api-docs");
        this.excludePathList = tempExcludePathList;
    }

    /**
     * 验证Token有效性并获取用户信息
     * <p>
     * 通过远程服务调用验证Token的有效性，如果验证成功则返回用户信息，否则返回null
     * </p>
     *
     * @param accessToken                    访问令牌
     * @param clientType                     客户端类型
     * @param ignoreUpdateLastTimeHeaderName 忽略更新最后时间的请求头名称
     * @param cookie                         Cookie信息
     * @param authorization                  授权信息
     * @return UserDTO 用户信息，如果验证失败则返回null
     */
    public UserDTO checkToken(String accessToken, String clientType, String ignoreUpdateLastTimeHeaderName, String cookie, String authorization) {
        try {
            ResponseEntity<RequestResult<UserDTO>> response = this.remoteOrgService.current(accessToken, clientType, ignoreUpdateLastTimeHeaderName, cookie, authorization);
            RequestResult<UserDTO> result = response.getBody();
            if (result != null && result.isSuccess()) {
                UserDTO user = result.getData();
                log.debug("current = {}", user);
                String respAccessToken = response.getHeaders().getFirst(RequestContext.ACCESSTOKEN);
                if (StringUtils.isNotBlank(respAccessToken)) {
                    user.setResponseAccessToken(respAccessToken);
                }

                return user;
            } else {
                log.error("远调获取当前用户失败,通过 accessToken: {}，获取到的用户为空！", accessToken);
                return null;
            }
        } catch (Exception e) {
            log.error("远调获取当前用户失败, accessToken: {}", accessToken, e);
            return null;
        }

    }

    /**
     * 从HTTP请求中获取Token
     * <p>
     * 按照优先级从请求中获取Token：
     * 1. 从请求头获取
     * 2. 从请求参数获取
     * 3. 从Cookie中获取
     * </p>
     *
     * @param request HTTP请求对象
     * @return String Token字符串，如果未找到则返回空字符串
     */
    public String getTokenByRequest(HttpServletRequest request) {
        String tokenName = getTokenName();
        Cookie cookie = getFirstCookie(request, tokenName);

        String accesstoken = getFirstHeader(request, tokenName);
        String accessTokenByParams = getFirstQueryParam(request, tokenName);
        if (StringUtils.isBlank(accesstoken) || "null".equals(accesstoken)) {
            accesstoken = accessTokenByParams;
            if (StringUtils.isBlank(accessTokenByParams)) {
                String cookieValue = cookie == null ? null : cookie.getValue();
                accesstoken = cookieValue;
                if (StringUtils.isBlank(cookieValue)) {
                    return "";
                }
            }
        }

        return accesstoken;
    }

    public String getTokenName() {
        if (this.tokenName != null) {
            return this.tokenName;
        }
        tokenName = this.environment.getProperty("sessionid.cookie.name", RequestContext.ACCESSTOKEN);
        return tokenName;
    }

    /**
     * 检查请求路径是否在忽略更新最后时间的URL列表中
     *
     * @param path 请求路径
     * @return String 如果路径在忽略列表中返回"true"，否则返回null
     */
    public String checkIgnoreUpdateLastTimeUrl(String path) {
        List<String> ignoreUpdateLastTimeUrlList = this.siniCubeShiroProperties.getIgnoreUpdateLastTimeUrlList();
        return ignoreUpdateLastTimeUrlList.contains(path) ? "true" : null;
    }


    /**
     * 从HTTP请求中获取客户端类型
     *
     * @param request HTTP请求对象
     * @return String 客户端类型，如果未设置则返回null
     */
    public String getClientType(HttpServletRequest request) {
        return getFirstHeader(request, "client-type");
    }

    /**
     * 从HTTP请求中获取第一个匹配名称的Cookie
     *
     * @param request HTTP请求对象
     * @param name    Cookie名称
     * @return Cookie 匹配的Cookie对象，如果未找到则返回null
     */
    public static Cookie getFirstCookie(HttpServletRequest request, String name) {
        Cookie[] cookies = request.getCookies();
        if (cookies == null) {
            return null;
        }
        for (Cookie cookie : cookies) {
            if (cookie.getName().equals(name)) {
                return cookie;
            }
        }
        return null;
    }

    /**
     * 从HTTP请求中获取第一个匹配名称的请求头
     *
     * @param request HTTP请求对象
     * @param name    请求头名称
     * @return String 请求头值，如果未找到则返回null
     */
    public static String getFirstHeader(HttpServletRequest request, String name) {
        Enumeration<String> headers = request.getHeaders(name);
        if (headers.hasMoreElements()) {
            return headers.nextElement();
        }
        return null;
    }

    /**
     * 从HTTP请求中获取第一个匹配名称的查询参数
     *
     * @param request HTTP请求对象
     * @param name    查询参数名称
     * @return String 查询参数值，如果未找到则返回null
     */
    public static String getFirstQueryParam(HttpServletRequest request, String name) {
        String[] values = request.getParameterValues(name);
        if (values != null && values.length > 0) {
            return values[0];
        }
        return null;
    }
}
