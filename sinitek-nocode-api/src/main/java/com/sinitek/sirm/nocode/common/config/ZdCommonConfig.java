package com.sinitek.sirm.nocode.common.config;

import com.sinitek.sirm.nocode.common.constant.ZdCommonConstant;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 2025.0717
 * @since 1.0.0-SNAPSHOT
 */
@Component
@Data
public class ZdCommonConfig {

    @Value("${nocode.app.key:}")
    private String appKey;

    @Value("${server.servlet.context-path:}")
    private String contextPath;

    public String getAppKey() {
        return "".equals(appKey) ? ZdCommonConstant.APP_KEY : appKey;
    }

}
