package com.sinitek.sirm.nocode.common.support.validator;

import com.sinitek.sirm.nocode.common.support.validator.base.IValidator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @version 2025.0511
 * @description
 * @since 1.0.0-SNAPSHOT
 */
@Target(ElementType.TYPE) // 仅作用于类
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = ValidatorAllValidator.class) // 关联验证器
public @interface ValidatorAll {
    Class<? extends IValidator> value();

    String message() default "";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}