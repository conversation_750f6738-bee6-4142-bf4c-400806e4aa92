package com.sinitek.sirm.nocode.form.support.condition;

import cn.hutool.core.util.ClassUtil;
import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sinitek.sirm.nocode.form.dto.ZdFormDataSearchFieldDTO;
import com.sinitek.sirm.nocode.form.enumerate.OperatorEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

/**
 * 条件
 *
 * <AUTHOR>
 * @version 2025.0613
 * @since 1.0.0-SNAPSHOT
 */


public interface ConditionQuery {

    /**
     * 自定义字段条件
     *
     * @param queryBuilder 查询构造器
     * @param condition    条件
     */
    void applyCustomFieldCondition(QueryWrapper<?> queryBuilder,
                                   ZdFormDataSearchFieldDTO condition);

    /**
     * 数据库类型
     *
     * @return 数据库类型
     */
    DbType dbType();

    @SuppressWarnings("java:S1640")
    default Map<OperatorEnum, OperationInterface> getOperationMap() {
        Logger logger = LoggerFactory.getLogger(ConditionQuery.class);
        Map<OperatorEnum, OperationInterface> map = new HashMap<>();
        ClassUtil.scanPackageBySuper("com.sinitek.sirm.nocode.form.support.condition.operation." + dbType().getDb(), OperationInterface.class)
                .forEach(clazz -> {
                    try {
                        OperationInterface operation = (OperationInterface) clazz.newInstance();
                        map.put(operation.type(), operation);
                    } catch (Exception e) {
                        logger.error("初始化操作失败:{}", e.getMessage(), e);
                    }
                });
        return map;
    }
}
