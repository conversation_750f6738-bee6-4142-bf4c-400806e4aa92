package com.sinitek.sirm.nocode.form.constant;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;

@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class FormDataExtraFieldConstant {

  public static final String ID_FIELD_KEY = "id";
  public static final String CREATOR_FIELD_KEY = "creator";
  public static final String CREATE_TIMESTAMP_FIELD_KEY = "createTimeStamp";
  public static final String UPDATE_TIMESTAMP_FIELD_KEY = "updateTimeStamp";

  public static final String ID_FIELD_NAME = "实例ID";
  public static final String CREATOR_FIELD_NAME = "提交人";
  public static final String CREATE_TIMESTAMP_FIELD_NAME = "创建时间";
  public static final String UPDATE_TIMESTAMP_FIELD_NAME = "修改时间";

}
