package com.sinitek.sirm.nocode.page.support.validator;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.sinitek.sirm.nocode.common.support.validator.base.IValidator;
import com.sinitek.sirm.nocode.form.dto.ZdFormDataScopeCustomConditionDTO;
import com.sinitek.sirm.nocode.page.dto.ZdPageAuthDTO;
import com.sinitek.sirm.nocode.page.enumerate.DataScopeEnum;
import com.sinitek.sirm.nocode.page.enumerate.MemberTypeEnum;
import com.sinitek.sirm.nocode.page.enumerate.PageAuthTypeEnum;
import com.sinitek.sirm.nocode.page.service.IZdPageService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import javax.validation.ConstraintValidatorContext;
import javax.validation.ConstraintViolation;
import javax.validation.Validator;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2025.0414
 * @description 权限验证
 * @since 1.0.0-SNAPSHOT
 */
@Component
@RequiredArgsConstructor
public class ZdPageAuthValidator implements IValidator<ZdPageAuthDTO> {
    private final IZdPageService pageService;
    private final Validator validator;

    /**
     * 验证页面权限配置的合法性
     *
     * @param zdPageAuthDTO 页面权限数据传输对象
     * @return 验证失败时返回错误信息，成功返回null
     */
    @Override
    public String valid(ZdPageAuthDTO zdPageAuthDTO, ConstraintValidatorContext context) {
        if (!isPageCodeValid(zdPageAuthDTO)) {
            return "页面编码不存在";
        }

        if (!validateMemberType(zdPageAuthDTO)) {
            return "成员类型不为所有成员时，成员id不能为空";
        }

        if (zdPageAuthDTO.getAuthType() == PageAuthTypeEnum.DATA_AUTH) {
            return validateDataScope(zdPageAuthDTO);
        }

        return null;
    }

    /**
     * 验证页面编码是否存在
     *
     * @param dto 页面权限数据传输对象
     * @return 如果页面编码有效返回true，否则false
     * @implNote 使用pageService检查页面编码存在性
     */
    private boolean isPageCodeValid(ZdPageAuthDTO dto) {
        return pageService.exists(dto.getFormCode());
    }

    /**
     * 验证成员类型与组织ID列表的合法性
     *
     * @param dto 页面权限数据传输对象
     * @return 如果成员类型与组织ID列表匹配返回true，否则false
     * @implNote 当成员类型不为ALL时，必须提供组织ID列表
     */
    private boolean validateMemberType(ZdPageAuthDTO dto) {
        return !(dto.getMemberType() != MemberTypeEnum.ALL &&
                CollectionUtils.isEmpty(dto.getMemberOrgIdList()));
    }

    /**
     * 验证数据范围配置的合法性
     *
     * @param dto 页面权限数据传输对象
     * @return 验证失败时返回错误信息，成功返回null
     * @implNote 包含自定义过滤条件和自定义部门两种验证场景
     */
    private String validateDataScope(ZdPageAuthDTO dto) {
        if (CollectionUtils.isEmpty(dto.getDataScopeList())) {
            return "数据范围不能为空";
        }
        if (dto.getDataScopeList().contains(DataScopeEnum.CUSTOM_FILTER.getValue())) {
            String s = validateCustomFilter(dto.getCustomDataScope());
            if (Objects.nonNull(s)) {
                return s;
            }
        } else {
            dto.setCustomDataScope(null);
        }
        if (dto.getDataScopeList().contains(DataScopeEnum.CUSTOM_DEPARTMENT.getValue())) {
            String s = validateCustomDepartment(dto.getDepartmentIdList());
            if (Objects.nonNull(s)) {
                return s;
            }
        }
        return null;
    }

    /**
     * 验证自定义过滤条件的合法性
     *
     * @param customDataScope 自定义数据范围条件对象
     * @return 验证失败时返回错误信息，成功返回null
     * @implNote 使用JSR-380规范的Bean Validation验证
     */
    private String validateCustomFilter(ZdFormDataScopeCustomConditionDTO customDataScope) {
        if (customDataScope == null) {
            return "自定义过滤条件不能为空";
        }

        Set<ConstraintViolation<ZdFormDataScopeCustomConditionDTO>> violations = validator.validate(customDataScope);
        if (!CollectionUtils.isEmpty(violations)) {
            return violations.stream()
                    .map(ConstraintViolation::getMessage)
                    .collect(Collectors.joining(";"));
        }
        return null;
    }

    /**
     * 验证自定义部门数据的合法性
     *
     * @param departmentIds 部门ID列表
     * @return 验证失败时返回错误信息，成功返回null
     * @implNote 当数据范围包含CUSTOM_DEPARTMENT时必须提供部门ID
     */
    private String validateCustomDepartment(List<String> departmentIds) {
        return CollectionUtils.isEmpty(departmentIds)
                ? "数据范围包含自定义部门时，自定义部门数据不能为空"
                : null;
    }
}
