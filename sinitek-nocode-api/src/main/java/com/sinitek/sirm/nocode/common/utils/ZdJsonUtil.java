package com.sinitek.sirm.nocode.common.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JavaType;
import com.sinitek.sirm.common.utils.JsonUtil;
import com.sinitek.sirm.framework.exception.BussinessException;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 2025.0711
 * @since 1.0.0-SNAPSHOT
 */
@Slf4j
public class ZdJsonUtil {

    private ZdJsonUtil() {
    }


    /**
     * 转换为对象
     *
     * @param object 对象
     * @return 转换后的对象
     */
    @SuppressWarnings("unchecked")
    public static <T> T parseObject(Object object, JavaType javaType) {
        if (Objects.isNull(object)) {
            return null;
        }
        if (object instanceof String) {
            return parse((String) object, javaType);
        }
        Class<?> rawClass = javaType.getRawClass();
        // 有多少个类型
        int i = javaType.containedTypeCount();
        if (i == 0 && rawClass.isAssignableFrom(object.getClass())) {
            // 直接返回
            return (T) object;
        } else if (i == 1 && rawClass.isAssignableFrom(List.class) && (object instanceof List)) {
            List<Object> list = (List<Object>) object;
            if (list.isEmpty()) {
                return (T) list;
            }
            Object o = list.stream().filter(Objects::nonNull).findFirst().orElse(null);
            if (Objects.isNull(o)) {
                return (T) list;
            }
            Class<?> subRawClass = javaType.containedType(0).getRawClass();
            if (subRawClass.isAssignableFrom(o.getClass())) {
                return (T) list;
            }
        }
        return parse(toJson(object), javaType);
    }


    public static String toJson(Object obj) {
        try {
            return JsonUtil.getObjectMapper().writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            log.error("JSON转换异常:{}", e.getMessage(), e);
            throw new BussinessException("010199");
        }
    }


    @SuppressWarnings("unchecked")
    public static <T> T parse(String json, JavaType javaType) {
        try {
            Class<?> firstType = javaType.getRawClass();
            if (Objects.equals(String.class, firstType)) {
                return (T) json;
            }
            return JsonUtil.getObjectMapper().readValue(addFix(json, javaType), javaType);
        } catch (IOException e) {
            log.error("JSON转换异常:{}", e.getMessage(), e);
            throw new BussinessException("010199");
        }
    }

    public static <T> T parse(String json, Class<T> javaType) {
        return parse(json, JsonUtil.getObjectMapper().constructType(javaType));
    }

    private static String addFix(String value, JavaType javaType) {
        if (Objects.nonNull(value)) {
            Class<?> firstType = javaType.getRawClass();
            if (Objects.equals(List.class, firstType) && !value.startsWith("[")) {
                value = "[" + value + "]";
            }
        }
        return value;
    }
}
