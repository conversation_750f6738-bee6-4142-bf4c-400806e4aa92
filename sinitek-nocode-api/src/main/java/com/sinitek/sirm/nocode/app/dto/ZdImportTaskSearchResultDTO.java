package com.sinitek.sirm.nocode.app.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.sinitek.sirm.common.utils.GlobalConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 导入任务查询结果DTO
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "导入任务查询结果DTO")
public class ZdImportTaskSearchResultDTO {

    @ApiModelProperty("任务ID")
    private Long id;

    @ApiModelProperty("文件名")
    private String fileName;

    @ApiModelProperty("状态：0-待处理，1-处理中，2-处理成功，3-处理失败")
    private Integer status;

    @ApiModelProperty("总记录数")
    private Integer totalCount;

    @ApiModelProperty("成功记录数")
    private Integer successCount;

    @ApiModelProperty("失败记录数")
    private Integer failedCount;

    @ApiModelProperty("状态名称")
    private String statusName;

    @ApiModelProperty("操作人id")
    private String operatorId;

    @ApiModelProperty("操作人姓名")
    private String operatorName;

    @JsonFormat(pattern = GlobalConstant.TIME_FORMAT_THIRTEEN)
    @ApiModelProperty("操作时间")
    private Date operateTime;

    @ApiModelProperty("表单编码")
    private String formCode;
}
