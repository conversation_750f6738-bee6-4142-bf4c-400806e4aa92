package com.sinitek.sirm.nocode.form.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @date 2025-07-21 16:07
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
@ApiModel(description = "导入错误报告信息")
public class ZdFailureReportInfoDTO {

    @ApiModelProperty("导入表头字段(错误报告)")
    private List<ZdImportFormHeaderFieldDTO> headerFields;

    @ApiModelProperty("导入数据表头(错误报告)")
    private List<ZdFormFieldDTO> excelHeader;

    @ApiModelProperty("excel合并信息(错误报告)")
    private List<ZdExcelMergeInfoDTO> excelMergeInfoList;
}
