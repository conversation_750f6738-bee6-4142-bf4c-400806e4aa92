package com.sinitek.sirm.nocode.form.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @date 2025-07-21 16:07
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
@ApiModel(description = "excel合并信息")
public class ZdExcelMergeInfoDTO {

    /**
     * First row
     */
    @ApiModelProperty("First row")
    private int firstRowIndex;
    /**
     * Last row
     */
    @ApiModelProperty("Last row")
    private int lastRowIndex;
    /**
     * First column
     */
    @ApiModelProperty("First column")
    private int firstColumnIndex;
    /**
     * Last row
     */
    @ApiModelProperty("Last row")
    private int lastColumnIndex;
}
