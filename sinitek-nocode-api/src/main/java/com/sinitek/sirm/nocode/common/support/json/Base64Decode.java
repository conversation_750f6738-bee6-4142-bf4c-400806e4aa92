package com.sinitek.sirm.nocode.common.support.json;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang.StringUtils;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
 * base64解密，用于反序列化
 *
 * <AUTHOR>
 * @version 2025.0312
 * @since 1.0.0-SNAPSHOT
 */
public class Base64Decode extends JsonDeserializer<String> {
    @Override
    public String deserialize(JsonParser jsonParser, DeserializationContext deserializationContext) throws IOException {
        String str;
        if (jsonParser == null || (str = jsonParser.getText()) == null) {
            return null;
        }
        return decode(str);
    }

    public static String decode(String encryptContent) {
        String content = null;
        if (StringUtils.isNotBlank(encryptContent)) {
            content = new String(Base64.decodeBase64(encryptContent), StandardCharsets.UTF_8);
        }

        return content;
    }
}
