package com.sinitek.sirm.nocode.app.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * 导出任务查询参数DTO
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
@ApiModel(description = "导出任务附件DTO")
public class ZdExportTaskAttachmentDTO {

    @ApiModelProperty(value = "附件id")
    private String id;

    @ApiModelProperty("文件名")
    private String fileName;
}
