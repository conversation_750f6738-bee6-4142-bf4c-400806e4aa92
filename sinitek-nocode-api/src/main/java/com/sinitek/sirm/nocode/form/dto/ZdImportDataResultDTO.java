package com.sinitek.sirm.nocode.form.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @date 2025-07-21 16:07
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
@ApiModel(description = "导入结果")
public class ZdImportDataResultDTO {

    @ApiModelProperty("新增数据id")
    private List<Long> addedIds;

    @ApiModelProperty("更新数据id")
    private List<Long> updatedIds;

    @ApiModelProperty("导入失败数据")
    private List<Map<String, Object>> failureDataList;

    @ApiModelProperty("更新前数据")
    private List<ZdPageFormDataDTO> beforeUpdateDataList;

    @ApiModelProperty("错误报告")
    private ZdFailureReportInfoDTO failureReportInfo;
}
