package com.sinitek.sirm.nocode.form.enumerate;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.sinitek.sirm.nocode.common.support.enumeratebase.BaseIntegerEnum;
import java.util.Arrays;
import java.util.Objects;
import lombok.Getter;

/**
 * 导入模式
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 1.0.0
 */
@Getter
public enum ImportModeEnum implements BaseIntegerEnum {

    ONLY_NEW(1, "仅新增数据"),
    UPDATE_EXISTING(2, "更新现有数据"),
    BOTH(3, "新增或更新");

    /**
     * 值
     */
    @JsonValue
    private final Integer value;
    /**
     * 名称
     */
    private final String label;

    ImportModeEnum(Integer value, String label) {
        this.value = value;
        this.label = label;
    }

    @JsonCreator
    public static ImportModeEnum fromValue(Integer value) {
        return Arrays.stream(ImportModeEnum.values())
            .filter(a -> Objects.equals(value, a.getValue())).findAny().orElse(ImportModeEnum.BOTH);
    }
} 