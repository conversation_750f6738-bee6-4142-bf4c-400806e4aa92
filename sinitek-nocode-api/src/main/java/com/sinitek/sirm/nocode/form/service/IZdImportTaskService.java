package com.sinitek.sirm.nocode.form.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sinitek.sirm.nocode.app.dto.ZdImportPreParseParamDTO;
import com.sinitek.sirm.nocode.app.dto.ZdImportPreParseResultDTO;
import com.sinitek.sirm.nocode.app.dto.ZdImportTaskSearchParamDTO;
import com.sinitek.sirm.nocode.app.dto.ZdImportTaskSearchResultDTO;
import com.sinitek.sirm.nocode.form.dto.ZdDoloadImportTemplateParam;
import com.sinitek.sirm.nocode.form.dto.ZdExportOrImportLoadDetailParamDTO;
import com.sinitek.sirm.nocode.form.dto.ZdGenerateImportExcelTemplateResultDTO;
import com.sinitek.sirm.nocode.form.dto.ZdImportDataErrorMsgDTO;
import com.sinitek.sirm.nocode.form.dto.ZdImportDataTaskExecParamDTO;

/**
 * 导入任务Service接口
 * <AUTHOR>
 * @version 1.0.0
 */
public interface IZdImportTaskService {

  IPage<ZdImportTaskSearchResultDTO> search(ZdImportTaskSearchParamDTO param);


  ZdGenerateImportExcelTemplateResultDTO generateImportExcelTemplate(
      ZdDoloadImportTemplateParam param)
      throws Exception;

  ZdImportPreParseResultDTO preParse(ZdImportPreParseParamDTO param) throws Exception;

  void runImportTaskAsync(ZdImportDataTaskExecParamDTO param);

  void updateFailureTaskStatusWithNewTransaction(Long taskId, ZdImportDataErrorMsgDTO param);

  String loadImportResourceAttachmentId(ZdExportOrImportLoadDetailParamDTO param);

  String loadImportFailureAttachmentId(ZdExportOrImportLoadDetailParamDTO param);

  ZdImportDataErrorMsgDTO getImportTaskErrorDetail(Long taskId);
} 