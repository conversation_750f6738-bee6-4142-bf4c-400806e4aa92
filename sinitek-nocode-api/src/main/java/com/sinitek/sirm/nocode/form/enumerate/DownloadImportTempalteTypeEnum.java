package com.sinitek.sirm.nocode.form.enumerate;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.sinitek.sirm.nocode.common.support.enumeratebase.BaseIntegerEnum;
import java.util.Arrays;
import java.util.Objects;
import lombok.Getter;

/**
 * 下载导入模板类型
 *
 * <AUTHOR>
 * @date 2025-07-30 14:57
 */
@Getter
public enum DownloadImportTempalteTypeEnum implements BaseIntegerEnum {
    ALL_FIELD(1, "全部字段"),
    ONLY_FORM_FIELD(2, "仅表单字段");

    /**
     * 值
     */
    @JsonValue
    private final Integer value;
    /**
     * 名称
     */
    private final String label;

    DownloadImportTempalteTypeEnum(Integer value, String label) {
        this.value = value;
        this.label = label;
    }

    @JsonCreator
    public static DownloadImportTempalteTypeEnum fromValue(Integer value) {
        return Arrays.stream(DownloadImportTempalteTypeEnum.values())
            .filter(a -> Objects.equals(value, a.getValue())).findAny().orElse(null);
    }
}
