package com.sinitek.sirm.nocode.form.enumerate;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.interfaces.Compare;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.enums.SqlKeyword;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sinitek.sirm.common.utils.JsonUtil;
import com.sinitek.sirm.nocode.common.support.enumeratebase.BaseStringEnum;
import com.sinitek.sirm.nocode.common.utils.ZdJsonUtil;
import com.sinitek.sirm.nocode.form.support.condition.OperationInterface;
import com.sinitek.sirm.nocode.form.support.condition.base.BetweenObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Getter;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 2025.0313
 * @since 1.0.0-SNAPSHOT
 */
@ApiModel(description = "操作枚举")
@Getter
public enum OperatorEnum implements BaseStringEnum {


    @ApiModelProperty("等于")
    EQ(SqlKeyword.EQ.name().toLowerCase(), "等于", "equal", "1,2", Compare::eq, null),
    @ApiModelProperty("不等于")
    NE(SqlKeyword.NE.name().toLowerCase(), "不等于", "not equal", "1,2", Compare::ne, null),
    @ApiModelProperty("大于")
    GT(SqlKeyword.GT.name().toLowerCase(), "大于", "greater than", null, Compare::gt, null),
    @ApiModelProperty("大于等于")
    GE(SqlKeyword.GE.name().toLowerCase(), "大于等于", "greater or equal", "3", Compare::ge, null),
    @ApiModelProperty("小于")
    LT(SqlKeyword.LT.name().toLowerCase(), "小于", "less than", null, Compare::lt, null),
    @ApiModelProperty("小于等于")
    LE(SqlKeyword.LE.name().toLowerCase(), "小于等于", "less or equal", "3", Compare::le, null),
    @ApiModelProperty("介于")
    BETWEEN(SqlKeyword.BETWEEN.name().toLowerCase(), "介于", "between", null, (a, b, c) -> {
        BetweenObject pairValue = OperationInterface.betweenObject(c);
        if (Objects.nonNull(pairValue)) {
            a.between(b, pairValue.getMin(), pairValue.getMax());
        }
    }, List.class),
    @ApiModelProperty("为空")
    IS_NULL(SqlKeyword.IS_NULL.name().toLowerCase(), "为空", "is null", null, (a, b, c) -> a.isNull(b), null),
    @ApiModelProperty("不为空")
    IS_NOT_NULL(SqlKeyword.IS_NOT_NULL.name().toLowerCase(), "不为空", "is not null", null, (a, b, c) -> a.isNotNull(b), null),
    @ApiModelProperty("等于任意一个")
    IN(SqlKeyword.IN.name().toLowerCase(), "包含", "in any", null, (wrapper, fieldName, value) -> {
        if (Objects.isNull(value)) {
            return;
        }
        if (value.getClass().isArray()) {
            Object[] objects = (Object[]) value;
            wrapper.in(objects.length > 0, fieldName, objects);
        } else if (List.class.isAssignableFrom(value.getClass())) {
            List<?> list = (List<?>) value;
            wrapper.in(!list.isEmpty(), fieldName, list);
        } else {
            wrapper.eq(fieldName, value);
        }
    }, List.class),
    @ApiModelProperty("不等于任意一个")
    NOT_IN(SqlKeyword.NOT_IN.name().toLowerCase(), "不包含", "not in any", "1", (wrapper, fieldName, value) -> {
        if (Objects.isNull(value)) {
            return;
        }
        if (value.getClass().isArray()) {
            Object[] objects = (Object[]) value;
            wrapper.notIn(objects.length > 0, fieldName, objects);
        } else if (List.class.isAssignableFrom(value.getClass())) {
            List<?> list = (List<?>) value;
            wrapper.notIn(!list.isEmpty(), fieldName, list);
        } else {
            wrapper.ne(fieldName, value);
        }
    }, List.class),
    LIKE(SqlKeyword.LIKE.name().toLowerCase(), "像", "like", null, Compare::like, String.class),
    NOT_LIKE(SqlKeyword.NOT_LIKE.name().toLowerCase(), "不像", "not like", null, Compare::notLike, String.class);


    @JsonValue
    @ApiModelProperty("值")
    private final String value;
    @ApiModelProperty("中文名称")
    private final String label;
    @ApiModelProperty("英文名称")
    private final String labelEn;


    @ApiModelProperty("所属")
    private final String ownedType;

    @ApiModelProperty("操作动作")
    @JsonIgnore
    @Getter(AccessLevel.NONE)
    private final OperatorInterface op;

    @ApiModelProperty("数值类型")
    @JsonIgnore
    @Getter(AccessLevel.NONE)
    private final Class<?> rawType;


    OperatorEnum(String value, String label, String labelEn, String ownedType, OperatorInterface op, Class<?> rawType) {
        this.value = value;
        this.label = label;
        this.labelEn = labelEn;
        this.ownedType = ownedType;
        this.op = op;
        this.rawType = rawType;
    }

    public static List<OperatorEnum> list() {
        return new ArrayList<>(Arrays.asList(OperatorEnum.values()));
    }

    @JsonCreator
    public static OperatorEnum fromValue(String value) {
        if (Objects.isNull(value)) {
            return null;
        }
        if (value.startsWith("{")) {
            HashMap<String, Object> map = JSONUtil.toBean(value, HashMap.class);
            value = (String) map.get("description");
        }
        String finalValue = value;
        return Arrays.stream(OperatorEnum.values()).filter(a -> Objects.equals(finalValue, a.getValue())).findAny().orElse(null);
    }


    @Override
    public int findTypeWithTarget(int type) {
        if (Objects.nonNull(ownedType) && ownedType.contains(String.valueOf(type))) {
            return type;

        }
        return getType();
    }

    /**
     * 操作
     *
     * @param type      值的类型
     * @param wrapper   wrap
     * @param fieldName 字段名
     * @param value     值
     */

    public void apply(Class<?> type, QueryWrapper<?> wrapper, String fieldName, Object value) {
        value = getObject(type, value);
        op.apply(wrapper, fieldName, value);
    }


    public Object getObject(Class<?> type, Object value) {
        if (Objects.nonNull(type)) {
            JavaType javaType = null;
            ObjectMapper objectMapper = JsonUtil.getObjectMapper();
            if (Objects.nonNull(rawType)) {
                if (Objects.equals(rawType, String.class)) {
                    javaType = objectMapper.getTypeFactory().constructType(String.class);
                } else {
                    javaType = objectMapper.getTypeFactory().constructParametricType(rawType, type);
                }
            } else {
                javaType = objectMapper.getTypeFactory().constructType(type);
            }
            value = ZdJsonUtil.parseObject(value, javaType);
        }
        return value;
    }

    @FunctionalInterface
    private interface OperatorInterface {
        /**
         * 操作
         *
         * @param wrapper   wrap
         * @param fieldName 字段名
         * @param value     值
         */
        void apply(QueryWrapper<?> wrapper, String fieldName, Object value);
    }


}
