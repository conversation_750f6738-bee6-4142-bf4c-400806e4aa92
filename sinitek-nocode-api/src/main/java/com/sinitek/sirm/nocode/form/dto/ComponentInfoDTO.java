package com.sinitek.sirm.nocode.form.dto;

import java.util.Map;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * 组件信息数据传输对象 用于存储从JSON中解析的组件字段信息
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "组件信息数据传输对象")
public class ComponentInfoDTO {

    /**
     * 组件唯一标识ID
     */
    @ApiModelProperty("组件唯一标识ID")
    private String id;

    /**
     * 组件引用名称
     */
    @ApiModelProperty("组件引用名称")
    private String ref;

    /**
     * 组件显示标签
     */
    @ApiModelProperty("组件显示标签")
    private String label;


    /**
     * 组件类型名称
     */
    @ApiModelProperty("组件类型名称")
    private String componentName;

    /**
     * 父组件引用
     */
    @ApiModelProperty("父组件引用")
    private String parentRef;

    /**
     * 父组件标签
     */
    @ApiModelProperty("父组件标签")
    private String parentLabel;

    /**
     * 是否为值组件
     */
    @ApiModelProperty("是否为值组件")
    private Boolean valueComponentFlag;

    /**
     * 是否为容器组件
     */
    @ApiModelProperty("是否为容器组件")
    private Boolean containerComponentFlag;

    /**
     * props
     */
    @ApiModelProperty("props")
    private Map<String, Object> props;

}
