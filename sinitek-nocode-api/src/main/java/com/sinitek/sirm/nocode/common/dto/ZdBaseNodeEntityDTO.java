package com.sinitek.sirm.nocode.common.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2025.0318
 * @since 1.0.0-SNAPSHOT
 */
@Data
@ApiModel(description = "树节点")
public class ZdBaseNodeEntityDTO {
    /**
     * 名称
     */
    @NotBlank(message = "名称不能为空")
    @Length(max = 100, message = "名称不能超过100个字符")
    @ApiModelProperty(value = "名称", example = "我的简历")
    private String name;


    @ApiModelProperty(value = "子节点")
    private List<? extends ZdBaseNodeEntityDTO> children;


    @ApiModelProperty(value = "父级id")
    private Long parentId;

    @ApiModelProperty("主键")
    private Long id;


    @ApiModelProperty(value = "排序")
    private Integer sort;


}
