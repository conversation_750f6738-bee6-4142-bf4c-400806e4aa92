package com.sinitek.sirm.nocode.form.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @date 2025-07-22 13:13
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("智搭提交字段")
public abstract class AbstractZdSubmitFieldBaseDTO {

    @ApiModelProperty("字段编码")
    private String ref;

    @ApiModelProperty("名称")
    private String label;

    @ApiModelProperty("对应值")
    private Object value;

    @ApiModelProperty("组件名称")
    private String componentName;

}
