package com.sinitek.sirm.nocode.page.support.validator;

import com.sinitek.sirm.nocode.common.enumerate.YesOrNoEnum;
import com.sinitek.sirm.nocode.common.support.validator.base.IValidator;
import com.sinitek.sirm.nocode.form.service.IZdPageFormService;
import com.sinitek.sirm.nocode.page.dto.ZdPageSceneDTO;
import com.sinitek.sirm.nocode.page.enumerate.SceneRepeatTypeEnum;
import com.sinitek.sirm.nocode.page.enumerate.SceneTypeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.validation.ConstraintValidatorContext;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2025.0514
 * @since 1.0.0-SNAPSHOT
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class ZdPageSceneValidator implements IValidator<ZdPageSceneDTO> {
    private static final int MIN_WEEK_DAY = 1;
    private static final int MAX_WEEK_DAY = 7;
    private static final int MIN_MONTH_DAY = 1;
    private static final int MAX_MONTH_DAY = 31;

    private final IZdPageFormService formService;

    @Override
    public String valid(ZdPageSceneDTO zdPageSceneSaveDTO, ConstraintValidatorContext context) {
        String pageCode = zdPageSceneSaveDTO.getFormCode();
        boolean published = formService.isPublished(pageCode);
        if (!published) {
            log.warn("页面未发布,请先发布页面");
        }
        SceneTypeEnum sceneType = zdPageSceneSaveDTO.getSceneType();
        if (Objects.equals(sceneType, SceneTypeEnum.COLLECTION_FORM)) {
            String repeatTypeError = validateRepeatType(zdPageSceneSaveDTO);
            if (repeatTypeError != null) {
                return repeatTypeError;
            }

            String timeFieldError = validateTimeFields(zdPageSceneSaveDTO);
            if (timeFieldError != null) {
                return timeFieldError;
            }
            List<String> reportSendOrgIds = zdPageSceneSaveDTO.getReportSendOrgIds();
            if (Objects.nonNull(reportSendOrgIds)) {
                reportSendOrgIds = reportSendOrgIds.stream().filter(Objects::nonNull).collect(Collectors.toList());
                if (reportSendOrgIds.isEmpty()) {
                    zdPageSceneSaveDTO.setReportSendOrgIds(null);
                }
            }


            return validateNotification(zdPageSceneSaveDTO);
        }
        return null;
    }

    /**
     * 验证重复类型是否已填写
     *
     * @param dto 页面场景数据传输对象
     * @return 错误信息，如果验证通过则返回null
     */
    private String validateRepeatType(ZdPageSceneDTO dto) {
        SceneRepeatTypeEnum repeatType = dto.getRepeatType();
        if (Objects.equals(repeatType, SceneRepeatTypeEnum.WEEK)) {
            Integer weekDay = dto.getWeekDay();
            if (Objects.isNull(weekDay)) {
                return "请选择每周几";
            } else if (weekDay < MIN_WEEK_DAY || weekDay > MAX_WEEK_DAY) {
                return "请选择正确的每周几";
            }
        }
        if (Objects.equals(repeatType, SceneRepeatTypeEnum.MONTH)) {
            Integer monthDay = dto.getMonthDay();
            if (Objects.isNull(monthDay)) {
                return "请选择每月几号";
            } else if (monthDay < MIN_MONTH_DAY || monthDay > MAX_MONTH_DAY) {
                return "请选择正确的每月几号";
            }
        }
        return null;
    }

    /**
     * 验证时间字段是否已填写
     *
     * @param dto 页面场景数据传输对象
     * @return 错误信息，如果验证通过则返回null
     */
    private String validateTimeFields(ZdPageSceneDTO dto) {
        LocalTime startTime = dto.getStartTime();
        if (Objects.isNull(startTime)) {
            return "开始时间不能为空";
        }
        YesOrNoEnum holidayStrategy = dto.getHolidayStrategy();
        if (Objects.isNull(holidayStrategy)) {
            return "节假日是否顺延不能为空";
        }
        LocalDate endDate = dto.getEndDate();
        if (Objects.isNull(endDate)) {
            return "截至日期不能为空";
        }
        return null;
    }

    /**
     * 验证通知方式是否已选择
     *
     * @param dto 页面场景数据传输对象
     * @return 错误信息，如果验证通过则返回null
     */
    private String validateNotification(ZdPageSceneDTO dto) {
        List<Integer> noteType = dto.getNoteType();
        if (CollectionUtils.isEmpty(noteType)) {
            return "请选择通知方式";
        }
        return null;
    }
}