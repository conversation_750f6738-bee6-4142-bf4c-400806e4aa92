package com.sinitek.sirm.nocode.common.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 2025.0630
 * @since 1.0.0-SNAPSHOT
 */
@Data
public class ZdKeyAndNameDTO implements Serializable {
    private static final long serialVersionUID = -8716518660110056114L;
    @ApiModelProperty(value = "唯一标志", required = true)
    private String key;
    @ApiModelProperty(value = "名称", required = true)
    private String name;
}
