package com.sinitek.sirm.nocode.page.event;

import com.sinitek.sirm.common.event.support.SiniCubeEvent;
import com.sinitek.sirm.nocode.page.dto.ZdPageDTO;

/**
 * <AUTHOR>
 * @version 2025.0402
 * @description 页面创建事件
 * @since 1.0.0-SNAPSHOT
 */

public class PageCreateEvent extends SiniCubeEvent<ZdPageDTO> {
    private static final long serialVersionUID = -7610894873617784042L;

    public PageCreateEvent() {
        super(new ZdPageDTO());
    }

    public PageCreateEvent(ZdPageDTO source) {
        super(source);
    }
}