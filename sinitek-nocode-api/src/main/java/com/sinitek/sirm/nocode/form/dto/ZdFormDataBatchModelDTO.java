package com.sinitek.sirm.nocode.form.dto;

import com.sinitek.sirm.nocode.common.dto.base.ZdIdDTO;
import com.sinitek.sirm.nocode.form.support.FormCodeSupplier;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2025.0408
 * @description
 * @since 1.0.0-SNAPSHOT
 */


@ApiModel(description = "表单数据批量模型dto")
@Data
public class ZdFormDataBatchModelDTO implements FormCodeSupplier {
    /**
     * 表单编码
     */
    @ApiModelProperty(value = "表单编码", example = "a10000")
    @NotBlank(message = "表单编码不能为空", groups = {ZdIdDTO.Delete.class, ZdIdDTO.View.class, ZdIdDTO.Save.class, ZdIdDTO.Update.class})
    private String formCode;

    @NotEmpty(message = "主键列表不能为空", groups = {ZdIdDTO.Delete.class})
    @ApiModelProperty(value = "主键列表", example = "1,2,3")
    private List<Long> idList;
}
