package com.sinitek.sirm.nocode.app.service;

import com.sinitek.sirm.nocode.app.dto.ZdAppOaIntegrationDTO;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @version 2025-03-12 09:24:24
 * @description 针对表【zd_app_oa_integration(应用OA集成表)】的数据库操作Service
 */
public interface IZdAppOaIntegrationService {
    /**
     * 保存或者修改应用OA集成接口
     *
     * @param zdAppOaIntegrationDTO 应用参数
     * @return 是否保存成功
     */

    boolean saveOrUpdate(ZdAppOaIntegrationDTO zdAppOaIntegrationDTO);

    /**
     * 应用集成登陆
     *
     * @param appCode 应用编码
     * @param code    授权码
     * @return 登录结果
     */
    String appLogin(String appCode, String code, HttpServletRequest request, HttpServletResponse response);

    String login(HttpServletRequest request, HttpServletResponse response);

}
