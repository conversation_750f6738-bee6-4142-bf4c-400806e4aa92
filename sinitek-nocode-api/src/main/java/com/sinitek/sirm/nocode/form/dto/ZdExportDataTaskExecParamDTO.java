package com.sinitek.sirm.nocode.form.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @date 2025-07-14 08:56
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
@ApiModel(description = "导出任务执行参数")
public class ZdExportDataTaskExecParamDTO {

    @ApiModelProperty(value = "任务Id")
    private Long taskId;

    @ApiModelProperty(value = "用户token")
    private String accessToken;

    @ApiModelProperty(value = "请求地址(用于拼接下载地址)")
    private String requestHost;

}
