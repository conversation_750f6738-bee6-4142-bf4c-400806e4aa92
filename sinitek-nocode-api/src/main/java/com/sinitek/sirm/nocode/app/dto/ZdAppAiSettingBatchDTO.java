package com.sinitek.sirm.nocode.app.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2025.0730
 * @since 1.0.0-SNAPSHOT
 */
@Data
@ApiModel(description = "批量新增或者修改的ai设置参数")
public class ZdAppAiSettingBatchDTO {
    @ApiModelProperty("批量新增或者修改的数据")
    private List<ZdAppAiSettingDTO> list;
    @ApiModelProperty("批量删除的数据")
    private List<Long> deleteIds;
}
