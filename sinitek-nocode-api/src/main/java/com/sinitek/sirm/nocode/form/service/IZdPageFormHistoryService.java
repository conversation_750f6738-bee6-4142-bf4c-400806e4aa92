package com.sinitek.sirm.nocode.form.service;

import com.sinitek.sirm.nocode.form.dto.ZdPageFormHistoryDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2025-03-13 16:13:23
 * @description 针对表【zd_page_form_history(页面表单历史表)】的数据库操作Service
 */
public interface IZdPageFormHistoryService {
    /**
     * 创建历史表单
     *
     * @param pageFormHistoryDTO 表单历史数据
     * @return 主键
     */
    Long create(ZdPageFormHistoryDTO pageFormHistoryDTO);

    /**
     * 根据表单id查询历史表单
     *
     * @param formId 表单id
     * @return 表单历史数据
     */
    List<ZdPageFormHistoryDTO> findByFormId(Long formId);

    /**
     * 根据表单id列表查询历史表单
     *
     * @param formIdList 表单id列表
     * @return 表单历史数据
     */
    List<ZdPageFormHistoryDTO> findByFormIdList(List<Long> formIdList);

    /**
     * 根据id查询历史表单
     *
     * @param id 主键
     * @return 表单历史数据
     */
    ZdPageFormHistoryDTO getById(Long id);


    /**
     * 根据表单id查询历史表单数量
     *
     * @param formIdList 表单id
     * @return 表单历史数据数量
     */
    Long countByFormId(List<Long> formIdList);


}
