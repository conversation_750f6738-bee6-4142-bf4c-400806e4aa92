package com.sinitek.sirm.nocode.form.dto;

import com.sinitek.sirm.nocode.common.dto.base.ZdIdDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.List;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 2025.0409
 * @description
 * @since 1.0.0-SNAPSHOT
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(description = "表单数据导出参数")
public class ZdFormDataExportParamDTO extends ZdFormDataBatchModelDTO {

    @ApiModelProperty(value = "查询参数", example = "{}")
    private ZdFormDataSearchParamDTO queryParam;

    @NotEmpty(message = "导出字段不能为空", groups = {ZdIdDTO.View.class})
    @ApiModelProperty(value = "导出字段", example = "[]")
    private List<ZdFormFieldDTO> exportFields;

    @NotNull(message = "导出附件标识不能为空", groups = {ZdIdDTO.View.class})
    @ApiModelProperty(value = "导出附件标识", example = "true/false")
    private Boolean exportAttachment;

    @NotNull(message = "导出实例ID标识不能为空", groups = {ZdIdDTO.View.class})
    @ApiModelProperty(value = "导出实例ID", example = "true/false")
    private Boolean exportId;

    @ApiModelProperty(value = "用户token")
    private String accessToken;

    @ApiModelProperty(value = "请求地址(用于拼接下载地址)")
    private String requestHost;

    @ApiModelProperty(value = "操作人ID(后端自动生成)")
    private String operatorId;

    @ApiModelProperty(value = "操作时间(后端自动生成)")
    private Date operateTime;
}
