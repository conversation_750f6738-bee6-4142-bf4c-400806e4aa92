package com.sinitek.sirm.nocode.app.dto;

import com.sinitek.sirm.nocode.form.support.FormCodeSupplier;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("导入任务删除新增数据参数")
public class ZdImportTaskDeleteAddedParamDTO implements FormCodeSupplier {

    @NotBlank(message = "表单编码不能为空")
    @ApiModelProperty("表单编码")
    private String formCode;

    @NotNull(message = "导入任务ID不能为空")
    @ApiModelProperty("导入任务ID")
    private Long taskId;

    @ApiModelProperty(value = "操作人ID(后端自动生成)")
    private String operatorId;

    @ApiModelProperty(value = "操作时间(后端自动生成)")
    private Date operateTime;
}
