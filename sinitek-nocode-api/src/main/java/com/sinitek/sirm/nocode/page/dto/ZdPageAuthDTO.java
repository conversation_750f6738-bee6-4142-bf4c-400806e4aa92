package com.sinitek.sirm.nocode.page.dto;

import com.sinitek.sirm.nocode.common.annotation.ApiEnumProperty;
import com.sinitek.sirm.nocode.common.dto.ZdOrgObjectDTO;
import com.sinitek.sirm.nocode.common.dto.base.ZdIdDTO;
import com.sinitek.sirm.nocode.common.support.validator.ValidatorAll;
import com.sinitek.sirm.nocode.form.dto.ZdFormDataScopeCustomConditionDTO;
import com.sinitek.sirm.nocode.page.enumerate.DataScopeEnum;
import com.sinitek.sirm.nocode.page.enumerate.FieldAuthFlagEnum;
import com.sinitek.sirm.nocode.page.enumerate.MemberTypeEnum;
import com.sinitek.sirm.nocode.page.enumerate.OperationAuthEnum;
import com.sinitek.sirm.nocode.page.enumerate.PageAuthTypeEnum;
import com.sinitek.sirm.nocode.page.support.validator.ZdPageAuthValidator;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 页面权限DTO
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ValidatorAll(value = ZdPageAuthValidator.class)
public class ZdPageAuthDTO extends ZdIdDTO {

    private static final long serialVersionUID = 4081547676090157785L;
    /**
     * 页面编码
     */
    @NotBlank(message = "表单编码不能为空")
    @ApiModelProperty(value = "表单编码", example = "page_114b522908e04cd09deeea7f4c25ca25", required = true)
    private String formCode;

    /**
     * 数据权限类型，0:提交，1:数据权限
     */
    @NotNull(message = "数据权限类型不能为空")
    @ApiEnumProperty(required = true, description = "权限类型")
    private PageAuthTypeEnum authType;

    /**
     * 权限排序
     */
    @NotNull(message = "权限排序不能为空")
    @ApiModelProperty(value = "权限排序", example = "0")
    private Integer sort;

    /**
     * 权限名称
     */
    @Length(max = 100, message = "权限名称不能超过100")
    @NotEmpty(message = "权限名称不能为空")
    @ApiModelProperty(value = "权限名称", example = "全部提交权限", required = true)
    private String name;

    /**
     * 权限描述
     */
    @Length(max = 1000, message = "权限描述不能超过1000")
    @ApiModelProperty(value = "权限描述", example = "这个是关于全部人员可以提交的权限描述")
    private String description;

    /**
     * 成员类型，0：所有成员,1:自定义
     */
    @NotNull(message = "成员类型不能为空")
    @ApiEnumProperty(example = "0")
    private MemberTypeEnum memberType;

    /**
     * 当成员类型选择为自定的时候，存储人员id,是个人员id数组
     */
    @ApiModelProperty(value = "当成员类型选择为自定的时候，存储人员id,是个人员id数组", example = "[\"999000001\"]")
    private List<String> memberOrgIdList;

    /**
     * 操作权限，多个用逗号隔开
     */
    @NotEmpty(message = "操作权限不能为空")
    @ApiEnumProperty(enumClazz = OperationAuthEnum.class, required = true, example = "0")
    private List<String> operationAuthList;

    /**
     * 数据范围，多个用逗号隔开
     */
    @ApiEnumProperty(enumClazz = DataScopeEnum.class, example = "0")
    private List<String> dataScopeList;

    /**
     * 当数据范围类型为自定义部门时，存储自定义部门数据
     */
    @ApiModelProperty(value = "当数据类型为自定义部门时，存储自定义部门数据", example = "\"[121212,11111]\"")
    private List<String> departmentIdList;

    /**
     * 字段权限，当为空时，为表单字段字段状态，有具体的权限控制的话，是个json数据
     */
    @ApiModelProperty(value = "自定义字段权限，是个json字符数据", example = "\"{name:\"read\"}\"")
    private String fieldAuthData;

    @NotNull(message = "字段权限枚举不能为空")
    @ApiEnumProperty(example = "0", required = true)
    private FieldAuthFlagEnum fieldAuth;

    /**
     * 自定义数据权限范围
     */
    @ApiModelProperty(value = "自定义数据权限范围")
    private ZdFormDataScopeCustomConditionDTO customDataScope;


    @ApiModelProperty(value = "组织结构信息")
    private transient List<ZdOrgObjectDTO> memberList;
}
