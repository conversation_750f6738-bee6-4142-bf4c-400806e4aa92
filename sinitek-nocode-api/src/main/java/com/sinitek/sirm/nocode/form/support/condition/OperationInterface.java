package com.sinitek.sirm.nocode.form.support.condition;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sinitek.sirm.nocode.common.utils.ZdJsonUtil;
import com.sinitek.sirm.nocode.form.constant.ZdPgSqlConstant;
import com.sinitek.sirm.nocode.form.enumerate.OperatorEnum;
import com.sinitek.sirm.nocode.form.support.condition.base.BetweenObject;
import com.sinitek.sirm.nocode.form.support.condition.base.ValueObject;

import java.util.List;
import java.util.Objects;

/**
 * 操作接口
 *
 * <AUTHOR>
 * @version 2025.0613
 * @since 1.0.0-SNAPSHOT
 */


public interface OperationInterface {
    /**
     * 操作
     *
     * @param wrapper   wrap
     * @param fieldName 字段名
     * @param value     值
     */
    void apply(QueryWrapper<?> wrapper, String fieldName, Object value);

    OperatorEnum type();


    /**
     * 获取pair
     *
     * @param value 值
     * @return pair
     */
    static BetweenObject betweenObject(Object value) {
        if (Objects.isNull(value)) {
            return null;
        }
        if (value.getClass().isArray()) {
            Object[] objects = (Object[]) value;
            if (objects.length == ZdPgSqlConstant.BETWEEN_SIZE) {
                return BetweenObject.of(objects[0], objects[1]);
            }
        } else if (List.class.isAssignableFrom(value.getClass())) {
            List<?> list = (List<?>) value;
            if (list.size() == ZdPgSqlConstant.BETWEEN_SIZE) {
                return BetweenObject.of(list.get(0), list.get(1));
            }
        }
        return null;
    }

    static String jsonb(Object value) {
        return ZdJsonUtil.toJson(ValueObject.of(value));
    }
}
