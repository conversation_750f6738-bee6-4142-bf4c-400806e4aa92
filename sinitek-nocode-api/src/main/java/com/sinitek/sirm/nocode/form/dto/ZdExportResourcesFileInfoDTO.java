package com.sinitek.sirm.nocode.form.dto;

import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import com.sinitek.sirm.common.attachment.dto.AttachmentDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2025-07-15 14:10
 */
@Data
@ApiModel("导出资源文件信息")
@EqualsAndHashCode
public class ZdExportResourcesFileInfoDTO {

    @ApiModelProperty("导出资源文件所属来源实体ID")
    private Long sourceId;

    @ApiModelProperty(value = "导出资源文件所属来源名称")
    private String sourceEntity;

    @ApiModelProperty(value = "导出资源文件所属附件type")
    private Integer type;

    @ApiModelProperty(value = "导出资源文件顺序(从0开始)")
    private Integer fileIndex;

    @ApiModelProperty("表单名称")
    private String formName;

    @ApiModelProperty(value = "操作时间")
    private Date operationTime;

    @ApiModelProperty(value = "附件与主表单数据实例ID缓存")
    private Map<String, Long> attachmentsIdCache;

    @ApiModelProperty(value = "附件子表单数据实例ID缓存")
    private Map<String, String> attachmentsChildIdCache;

    @ApiModelProperty(value = "附件")
    private List<AttachmentDTO> attachments;

    public ZdExportResourcesFileInfoDTO() {
        this.attachments = new LinkedList<>();
    }

    public void addAttachment(AttachmentDTO attachment) {
        this.attachments.add(attachment);
    }
}
