package com.sinitek.sirm.nocode.page.dto;

import com.sinitek.sirm.nocode.common.annotation.ApiEnumProperty;
import com.sinitek.sirm.nocode.common.dto.base.ZdIdDTO;
import com.sinitek.sirm.nocode.page.enumerate.PageAuthTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2025.0805
 * @since 1.0.0-SNAPSHOT
 */
@Data
public class ZdPageAuthSortDTO {
    /**
     * 数据权限类型，0:提交，1:数据权限
     */
    @NotNull(message = "数据权限类型不能为空", groups = {ZdIdDTO.Update.class})
    @ApiEnumProperty(required = true, description = "权限类型")
    private PageAuthTypeEnum authType;

    /**
     * 页面编码
     */
    @NotBlank(message = "表单编码不能为空", groups = {ZdIdDTO.Update.class})
    @ApiModelProperty(value = "表单编码", example = "page_114b522908e04cd09deeea7f4c25ca25", required = true)
    private String formCode;


    @ApiModelProperty(value = "权限ID列表", required = true)
    @NotEmpty(message = "权限ID列表不能为空", groups = {ZdIdDTO.Update.class})
    private List<Long> idList;


}
