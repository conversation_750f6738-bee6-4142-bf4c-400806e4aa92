package com.sinitek.sirm.nocode.form.service;

import com.sinitek.sirm.framework.frontend.support.TableResult;
import com.sinitek.sirm.nocode.form.dto.ZdFormDataBatchModelDTO;
import com.sinitek.sirm.nocode.form.dto.ZdFormDataModelDTO;
import com.sinitek.sirm.nocode.form.dto.ZdFormDataSearchParamDTO;
import com.sinitek.sirm.nocode.form.dto.ZdPageFormDataDTO;
import com.sinitek.sirm.nocode.form.dto.ZdPageFormDataFillReportDTO;

import java.util.List;

public interface IZdPageFormDataService {


    /**
     * 查询数据列表
     *
     * @param param 查询参数
     * @return 数据列表
     */
    TableResult<ZdPageFormDataDTO> search(ZdFormDataSearchParamDTO param);

    /**
     * 修改或者保存数据
     *
     * @param zdPageFormData 表单数据
     * @return 实例id
     */
    Long saveOrUpdate(ZdPageFormDataDTO zdPageFormData);

    /**
     * 修改或者保存数据,使用嵌套事务
     *
     * @param zdPageFormData 表单数据
     * @return 实例id
     */

    Long saveOrUpdateWithNestedTransactional(ZdPageFormDataDTO zdPageFormData);

    /**
     * 批量修改创建者id和修改者id
     *
     * @param formCode 表单编码
     * @param list     数据集合
     */
    void updateBatchCreatorIdAndUpdaterId(String formCode, List<ZdPageFormDataDTO> list);


    /**
     * 删除数据
     *
     * @param zdFormDataModelDTO 表单数据
     * @return 是否删除成功
     */
    boolean delete(ZdFormDataModelDTO zdFormDataModelDTO);

    /**
     * 批量删除数据
     *
     * @param zdFormDataModelDTO 表单数据
     * @return 是否删除成功
     */
    boolean deleteBatch(ZdFormDataBatchModelDTO zdFormDataModelDTO);


    /**
     * 根据id获取表单数据
     *
     * @param id       数据主键
     * @param formCode 表单编码
     * @return 数据详情
     */
    ZdPageFormDataDTO getById(Long id, String formCode);

    /**
     * 是不是数据拥有者
     *
     * @param zdFormDataModelDTO 表单数据
     * @return 是不数据拥有者
     */
    boolean dataOwnerCheck(ZdFormDataModelDTO zdFormDataModelDTO);

    /**
     * 数据暂存
     *
     * @param zdPageFormDataDTO 表单数据
     * @return 数据主键
     */
    Long temporarySave(ZdPageFormDataDTO zdPageFormDataDTO);

    /**
     * 数据暂存,使用嵌套事务
     *
     * @param zdPageFormDataDTO 表单数据
     * @return 数据主键
     */
    Long temporarySaveWithNestedTransactional(ZdPageFormDataDTO zdPageFormDataDTO);

    /**
     * 获取表单暂存数据
     *
     * @return 暂存数据
     */
    ZdPageFormDataDTO getTemporary(String formCode, String orgId);


    /**
     * 查看某条数据详情
     *
     * @param formCode 表单编码
     * @param id       数据主键
     * @return 数据详情
     */

    ZdPageFormDataDTO view(String formCode, Long id);

    /**
     * 批量获取表单数据
     *
     * @param formCode 表单编码
     * @param idList   数据主键集合
     * @return 数据详情集合
     */
    List<ZdPageFormDataDTO> list(String formCode, List<Long> idList);

    /**
     * 获取存在的id
     *
     * @param formCode 表单编码
     * @param idList   数据主键集合
     * @return 数据详情集合
     */
    List<Long> existsId(String formCode, List<Long> idList);

    /**
     * 收集表场景结果报告
     *
     * @param formCode    表单编码
     * @param reportParam 来源网址参数上的值:&reportParam=abcdef,这个时候就需要把abcdef放到这个参数里面
     * @return 数据详情集合
     */
    ZdPageFormDataFillReportDTO pageFormDataFillReport(String formCode, String reportParam);


}
