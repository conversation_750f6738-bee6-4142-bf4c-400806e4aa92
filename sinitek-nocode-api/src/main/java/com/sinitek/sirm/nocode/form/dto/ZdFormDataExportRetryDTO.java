package com.sinitek.sirm.nocode.form.dto;

import com.sinitek.sirm.nocode.common.dto.base.ZdIdDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 2025.07.04
 * @description
 * @since 1.0.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(description = "表单数据导出重试参数")
public class ZdFormDataExportRetryDTO extends ZdFormDataBaseModelDTO {

    @NotNull(message = "任务ID不能为空", groups = {ZdIdDTO.View.class})
    @ApiModelProperty(value = "任务ID", example = "1")
    private Long taskId;

    @ApiModelProperty(value = "用户token")
    private String accessToken;

    @ApiModelProperty(value = "请求地址(用于拼接下载地址)")
    private String requestHost;

    @ApiModelProperty(value = "操作人ID(后端自动生成)")
    private String operatorId;

    @ApiModelProperty(value = "操作时间(后端自动生成)")
    private Date operateTime;
}
