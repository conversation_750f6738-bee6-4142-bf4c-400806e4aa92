package com.sinitek.sirm.nocode.common.dto.base;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 2025.0714
 * @since 1.0.0-SNAPSHOT
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class BaseAuditEntityDTO extends BaseEntityDTO {
    @ApiModelProperty("创建人orgId")
    private String creatorId;
    @ApiModelProperty("更新人orgId")
    private String updaterId;

}
