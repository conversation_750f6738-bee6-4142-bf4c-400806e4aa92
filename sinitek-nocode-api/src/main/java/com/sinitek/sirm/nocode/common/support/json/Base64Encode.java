package com.sinitek.sirm.nocode.common.support.json;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang.StringUtils;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
 * base64加密，用于序列化
 *
 * <AUTHOR>
 * @version 2025.0312
 * @since 1.0.0-SNAPSHOT
 */
public class Base64Encode extends JsonSerializer<String> {
    @Override
    public void serialize(String s, JsonGenerator gen, SerializerProvider serializerProvider) throws IOException {
        gen.writeObject(encode(s));
    }

    public static String encode(String content) {
        if (StringUtils.isNotBlank(content)) {
            content = Base64.encodeBase64String(content.getBytes(StandardCharsets.UTF_8));
        }

        return content;
    }
}
