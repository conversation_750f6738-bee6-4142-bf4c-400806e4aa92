package com.sinitek.sirm.nocode.common.support.json;

import cn.hutool.core.util.NumberUtil;
import com.fasterxml.jackson.core.JacksonException;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.sinitek.sirm.nocode.common.utils.ZdDateUtil;

import java.io.IOException;
import java.time.LocalDate;
import java.util.Date;

/**
 * 自定义LocalDate反序列化器
 * <p>
 * 用于处理JSON中日期字符串的反序列化，支持处理时间戳格式和标准日期格式的字符串。
 * 当输入为时间戳时，会将其转换为对应的LocalDate对象；
 * 当输入为其他格式时，使用原始的反序列化器进行处理。
 * </p>
 *
 * <AUTHOR>
 * @version 2025.0730
 * @since 1.0.0-SNAPSHOT
 */
public class CustomLocalDateDeserializer extends JsonDeserializer<LocalDate> {
    /**
     * 原始的LocalDate反序列化器
     */
    private final JsonDeserializer<LocalDate> org;

    /**
     * 构造函数
     *
     * @param org 原始的LocalDate反序列化器
     */
    public CustomLocalDateDeserializer(JsonDeserializer<LocalDate> org) {
        this.org = org;
    }


    /**
     * 反序列化方法，将JSON中的日期字符串转换为LocalDate对象
     *
     * @param jsonParser             JSON解析器
     * @param deserializationContext 反序列化上下文
     * @return 解析得到的LocalDate对象，解析失败时返回null
     * @throws IOException      IO异常
     * @throws JacksonException Jackson处理异常
     * @implNote 支持处理时间戳格式(如 : 1672531200000)和标准日期格式的日期字符串，
     * 如果输入是带双引号的字符串，则先去除首尾的双引号再进行处理
     */
    @Override
    public LocalDate deserialize(JsonParser jsonParser, DeserializationContext deserializationContext) throws IOException, JacksonException {
        String str;
        if (jsonParser == null || (str = jsonParser.getText()) == null) {
            return null;
        }
        if (NumberUtil.isNumber(str)) {
            return ZdDateUtil.toLocalDate(new Date(NumberUtil.parseLong(str)));
        }
        return org.deserialize(jsonParser, deserializationContext);
    }
}