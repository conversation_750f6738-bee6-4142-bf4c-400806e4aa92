package com.sinitek.sirm.nocode.common.dto.base;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 2025.0714
 * @since 1.0.0-SNAPSHOT
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class BaseVersionEntityDTO extends BaseEntityDTO {


    @ApiModelProperty("发布版本")
    private Integer publishVersion;


    //@ApiEnumProperty(description = "发布状态，应用于版本模型", viewEnum = false, enumClazz = VersionStatusEnum.class)
    @ApiModelProperty("发布状态,1:未发布，2:已发布，4:历史发布，5:已废弃，100:已删除")
    private Integer publishStatus;

    @ApiModelProperty(value = "线索id", notes = "同一个表单的多个版本，这个id是一样的")
    private Long threadId;

    @ApiModelProperty("最新有效")
    private Integer latestFlag;

    @ApiModelProperty("最新版本")
    private Integer threadLatestFlag;

    @ApiModelProperty("发布日期")
    private Date publishDate;
}
