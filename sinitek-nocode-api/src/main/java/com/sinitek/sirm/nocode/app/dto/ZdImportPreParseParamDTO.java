package com.sinitek.sirm.nocode.app.dto;

import com.sinitek.sirm.framework.frontend.dto.UploadDTO;
import com.sinitek.sirm.nocode.form.support.FormCodeSupplier;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("预导入解析结果")
public class ZdImportPreParseParamDTO implements FormCodeSupplier {

    @NotBlank(message = "表单编码不能为空")
    @ApiModelProperty("表单编码")
    private String formCode;

    @NotNull(message = "上传文件不能为空")
    @ApiModelProperty("上传文件")
    private UploadDTO upload;

    @ApiModelProperty(value = "操作人ID(后端自动生成)")
    private String operatorId;

    @ApiModelProperty(value = "操作时间(后端自动生成)")
    private Date operateTime;
}
