package com.sinitek.sirm.nocode.app.service;

import com.sinitek.sirm.nocode.app.dto.ZdAppAiSettingBatchDTO;
import com.sinitek.sirm.nocode.app.dto.ZdAppAiSettingDTO;
import com.sinitek.sirm.nocode.common.dto.ZdOptionDTO;
import com.sinitek.sirm.nocode.llm.config.AiModelConfig;
import com.sinitek.sirm.nocode.llm.enumerate.AiSourceEnum;

import java.util.List;

/**
 * 应用AI设置Service接口
 *
 * <AUTHOR>
 * @version 2025.0729
 * @description 针对表【zd_app_ai_setting(应用AI设置)】的数据库操作Service
 * @since 1.0.0-SNAPSHOT
 */
public interface IZdAppAiSettingService {

    /**
     * 保存或更新AI设置信息
     *
     * @param settingDTO AI设置信息传输对象，包含应用编码、AI源、模型类型key、应用名称和API密钥等信息
     * @return 返回保存或更新后的记录ID
     */
    Long saveOrUpdate(ZdAppAiSettingDTO settingDTO);

    /**
     * 批量保存或更新AI设置信息
     *
     * @param batchDTO AI设置信息传输对象列表，包含应用编码、AI源、模型类型key、应用名称和API密钥等信息
     * @return 返回保存或更新后的记录ID列表
     */
    boolean saveOrUpdate(ZdAppAiSettingBatchDTO batchDTO);

    /**
     * 根据应用编码查询AI设置列表
     *
     * @param appCode 应用编码
     * @return 返回指定应用编码下的所有AI设置信息列表
     */
    List<ZdAppAiSettingDTO> findByAppCode(String appCode);

    /**
     * 根据应用编码和AI源查询AI设置列表
     *
     * @param appCode  应用编码
     * @param aiSource AI源枚举值
     * @return 返回指定应用编码和AI源下的所有AI设置信息列表
     */
    List<ZdAppAiSettingDTO> findByAppCode(String appCode, AiSourceEnum aiSource);

    /**
     * 根据ID删除AI设置记录
     *
     * @param id AI设置记录的主键ID
     * @return 删除成功返回true，否则返回false
     */
    boolean deleteById(Long id);


    /**
     * 根据应用编码获取AI模型配置信息
     *
     * @param appCode 应用编码
     * @return 返回对应应用的AI模型配置信息
     */
    AiModelConfig getConfig(String appCode);

    /**
     * 根据应用密钥获取AI模型配置信息
     *
     * @param appKey 应用密钥
     * @return 返回对应应用密钥的AI模型配置信息
     */
    AiModelConfig getByAppKeyConfig(String appKey);


    /**
     * 根据AI源枚举获取可用的模型类型选项列表
     *
     * @param sourceEnum AI源枚举值
     * @return 返回指定AI源下的模型类型选项列表
     */
    List<ZdOptionDTO<String>> typeList(AiSourceEnum sourceEnum);


}
