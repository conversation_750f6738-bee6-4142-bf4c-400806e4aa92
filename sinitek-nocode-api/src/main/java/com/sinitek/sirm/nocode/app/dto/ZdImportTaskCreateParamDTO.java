package com.sinitek.sirm.nocode.app.dto;

import com.sinitek.sirm.framework.frontend.dto.UploadDTO;
import com.sinitek.sirm.nocode.form.dto.ZdImportFormHeaderFieldDTO;
import com.sinitek.sirm.nocode.form.support.FormCodeSupplier;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.Map;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("导入任务创建参数")
public class ZdImportTaskCreateParamDTO implements FormCodeSupplier {

    @NotBlank(message = "表单编码不能为空")
    @ApiModelProperty("表单编码")
    private String formCode;

    @NotNull(message = "上传文件不能为空")
    @ApiModelProperty("上传文件")
    private UploadDTO upload;

    @NotNull(message = "导入模式不能为空")
    @ApiModelProperty("导入模式")
    private Integer importMode;

    @NotNull(message = "字段校验标识不能为空")
    @ApiModelProperty("字段校验标识")
    private Integer fieldValidFlag;

    @NotNull(message = "提交人配置不能为空")
    @ApiModelProperty("导入数据提交人模式")
    private Integer importDataCreatorMode;

    @ApiModelProperty("用户自定义导入字段")
    private Map<Integer, ZdImportFormHeaderFieldDTO> userColumnSettingField;

    @ApiModelProperty(value = "操作人ID(后端自动生成)")
    private String operatorId;

    @ApiModelProperty(value = "操作时间(后端自动生成)")
    private Date operateTime;
}
