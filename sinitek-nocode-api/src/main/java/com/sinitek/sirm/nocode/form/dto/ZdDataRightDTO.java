package com.sinitek.sirm.nocode.form.dto;

import com.sinitek.sirm.nocode.page.enumerate.OperationAuthEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 2025.0721
 * @since 1.0.0-SNAPSHOT
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ZdDataRightDTO extends ZdFormCodeDTO {

    /**
     * 操作权限类型,默认查看
     */
    private OperationAuthEnum operationAuthType = OperationAuthEnum.VIEW;

    /**
     * 是否检查应用状态
     */
    private boolean checkStatus;
}
