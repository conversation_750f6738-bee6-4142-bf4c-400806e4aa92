package com.sinitek.sirm.nocode.form.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sinitek.sirm.nocode.app.dto.ZdExportTaskAttachmentDTO;
import com.sinitek.sirm.nocode.app.dto.ZdExportTaskSearchParamDTO;
import com.sinitek.sirm.nocode.app.dto.ZdExportTaskSearchResultDTO;
import com.sinitek.sirm.nocode.form.dto.ZdExportDataErrorMsgDTO;
import com.sinitek.sirm.nocode.form.dto.ZdExportDataTaskExecParamDTO;
import com.sinitek.sirm.nocode.form.dto.ZdExportResourceTaskExecParamDTO;
import java.util.List;

/**
 * 导出任务Service接口
 *
 * <AUTHOR>
 * @version 1.0.0
 */
public interface IZdExportTaskService {

    /**
     * 分页查询导出任务列表
     *
     * @param param 查询参数
     * @return 查询结果
     */
    IPage<ZdExportTaskSearchResultDTO> search(ZdExportTaskSearchParamDTO param);

    ZdExportDataErrorMsgDTO getExportTaskErrorDetail(Long taskId);

    List<ZdExportTaskAttachmentDTO> findExportExcelAttachment(Long taskId);

    String getExportExcelAttachmentId(Long taskId);

    /**
     * 异步执行导出任务
     *
     * @param param 参数
     */
    void runExportTaskAsync(ZdExportDataTaskExecParamDTO param);

    void runExportResourceAsync(ZdExportResourceTaskExecParamDTO param);

    void updateFailureTaskStatusWithNewTransaction(Long taskId, ZdExportDataErrorMsgDTO errorMsg);

    /**
     * 删除给定天数前的已完成任务记录
     * 
     * @param days 天数
     */
    void cleanHistoryExportTask(int days);

    /**
     * 给定分钟数，检查长时间处于"处理中"或"待处理"状态的任务，超过给定分钟数自动标记为失败
     * 
     * @param minutes 分钟数
     */
    void checkExportTaskStatus(int minutes);
}