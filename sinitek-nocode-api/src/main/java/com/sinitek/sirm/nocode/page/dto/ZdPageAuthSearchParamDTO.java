package com.sinitek.sirm.nocode.page.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.sinitek.sirm.nocode.common.dto.ZdSearchParamDTO;
import com.sinitek.sirm.nocode.page.enumerate.PageAuthTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version 2025.0411
 * @description 权限查询条件
 * @since 1.0.0-SNAPSHOT
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "权限查询条件")
@Data
public class ZdPageAuthSearchParamDTO extends ZdSearchParamDTO {

    /**
     * 数据权限类型，0:提交，1:数据权限
     */
    @NotNull(message = "权限类型不能为空")
    @ApiModelProperty(value = "数据权限类型，0:提交，1:数据权限", example = "0", required = true)
    private PageAuthTypeEnum authType;

    /**
     * 表单编码(同页面编码)
     */
    @JsonIgnore
    @NotBlank(message = "页面编码不能为空")
    @ApiModelProperty(value = "页面编码", example = "b10000", required = true)
    private String pageCode;

    @ApiModelProperty(value = "表单编码", example = "page_114b522908e04cd09deeea7f4c25ca25", required = true)
    public void setFormCode(String formCode) {
        this.pageCode = formCode;
    }

    @NotBlank(message = "页面编码不能为空")
    public String getFormCode() {
        return this.pageCode;
    }
}
