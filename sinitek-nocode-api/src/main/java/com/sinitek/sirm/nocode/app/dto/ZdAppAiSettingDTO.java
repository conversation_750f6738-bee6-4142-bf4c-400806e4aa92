package com.sinitek.sirm.nocode.app.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.sinitek.sirm.nocode.common.annotation.ApiEnumProperty;
import com.sinitek.sirm.nocode.common.dto.base.ZdIdDTO;
import com.sinitek.sirm.nocode.llm.enumerate.AiSourceEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @version 2025.0729
 * @since 1.0.0-SNAPSHOT
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ZdAppAiSettingDTO extends ZdIdDTO {
    /**
     * 应用编码
     */
    @NotBlank(message = "应用编码不能为空")
    @Length(max = 50, message = "应用编码不能超过50个字符")
    @ApiModelProperty(value = "应用编码", example = "app_123456", required = true)
    private String appCode;


    /**
     * AI源的key
     */
    @JsonIgnore
    @ApiEnumProperty(required = true)
    private AiSourceEnum aiSource;


    /**
     * 模型类型key
     */
    @NotBlank(message = "模型类型key不能为空")
    @Length(max = 50, message = "模型类型key不能超过50个字符")
    @ApiModelProperty(value = "模型类型key", example = "llm", required = true)
    private String typeKey;

    /**
     * 模型应用名称
     */
    @NotBlank(message = "模型应用名称不能为空")
    @Length(max = 50, message = "模型应用名称不能超过50个字符")
    @ApiModelProperty(value = "模型应用名称", example = "OpenAI", required = true)
    private String appName;

    /**
     * 应用密钥
     */
    @NotBlank(message = "应用密钥不能为空")
    @Length(max = 100, message = "应用密钥不能超过100个字符")
    @ApiModelProperty(value = "应用密钥", example = "openai", required = true)
    private String apiKey;
}
