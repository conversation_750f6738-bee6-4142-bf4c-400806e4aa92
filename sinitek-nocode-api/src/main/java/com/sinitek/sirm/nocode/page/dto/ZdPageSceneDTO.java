package com.sinitek.sirm.nocode.page.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.sinitek.sirm.nocode.common.annotation.ApiEnumProperty;
import com.sinitek.sirm.nocode.common.dto.base.ZdIdDTO;
import com.sinitek.sirm.nocode.common.enumerate.YesOrNoEnum;
import com.sinitek.sirm.nocode.common.support.validator.ValidatorAll;
import com.sinitek.sirm.nocode.page.enumerate.NoteTypeEnum;
import com.sinitek.sirm.nocode.page.enumerate.SceneRepeatTypeEnum;
import com.sinitek.sirm.nocode.page.enumerate.SceneSubmitEnum;
import com.sinitek.sirm.nocode.page.enumerate.SceneTypeEnum;
import com.sinitek.sirm.nocode.page.support.validator.ZdPageSceneValidator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;

/**
 * 页面场景设置
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(description = "场景DTO")
@ValidatorAll(value = ZdPageSceneValidator.class)
public class ZdPageSceneDTO extends ZdIdDTO {
    private static final long serialVersionUID = -1278909476792640804L;
    /**
     * 页面编码
     */
    @NotBlank(message = "页面编码不能为空")
    @ApiModelProperty(value = "页面编码", example = "page_114b522908e04cd09deeea7f4c25ca25", required = true)
    private String formCode;

    /**
     * 场景类型
     */
    @NotNull(message = "场景类型不能为空")
    @ApiEnumProperty(example = "0", required = true)
    private SceneTypeEnum sceneType;

    /**
     * 提交类型，0：无限制，1：每个人限制一次
     */
    @NotNull(message = "提交规则不能为空")
    @ApiEnumProperty(example = "0")
    private SceneSubmitEnum submitType;

    /**
     * 重复类型，0：每天，1：每周，2：每月
     */
    @ApiEnumProperty(example = "0")
    private SceneRepeatTypeEnum repeatType;

    /**
     * 每天开始时间
     */
    @JsonFormat(pattern = "HH:mm")
    @ApiModelProperty(value = "每天开始时间", example = "09:40")
    private LocalTime startTime;

    /**
     * 节假日是否顺延
     */
    @ApiModelProperty(value = "节假日是否顺延,0:否，1：是,数值类型", example = "0")
    @ApiEnumProperty(description = "节假日是否顺延", viewEnum = false, example = "0")
    private YesOrNoEnum holidayStrategy;

    /**
     * 截至时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "表单收集截至时间", example = "2025-03-25")
    private LocalDate endDate;

    /**
     * 报告发送人，多个用逗号隔开
     */
    @ApiModelProperty(value = "报告接收人，存储人员id,是个人员id数组", example = "[121212,11111]")
    private List<String> reportSendOrgIds;


    @ApiEnumProperty(enumClazz = NoteTypeEnum.class, keepDataType = true, example = "[0]")
    private List<Integer> noteType;

    @ApiModelProperty(value = "每月几号重复，当重复类型为每月时，需要指定具体日期", example = "1")
    private Integer monthDay;

    @ApiModelProperty(value = "每周几重复，当重复类型为每周时，需要指定具体日期", example = "1")
    private Integer weekDay;
}
