package com.sinitek.sirm.nocode.app.event;

import lombok.Data;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 2025.0331
 * @since 1.0.0-SNAPSHOT
 */
@Data
public class AppDeleteEvent {
    /**
     * 下一步
     */
    private String next;
    /**
     * id
     */
    private List<Long> idList;
    /**
     * code
     */
    private List<String> codeList;

    /**
     * map
     */
    private Map<String, Object> map;

    public AppDeleteEvent(String next, List<String> codeList) {
        this.next = next;
        this.codeList = codeList;
    }

    public AppDeleteEvent(String next, String code) {
        this.next = next;
        this.codeList = Collections.singletonList(code);
    }

    public AppDeleteEvent(String next, List<String> codeList, List<Long> idList) {
        this.next = next;

        this.codeList = codeList;
        this.idList = idList;
    }

    public AppDeleteEvent() {
    }
}
