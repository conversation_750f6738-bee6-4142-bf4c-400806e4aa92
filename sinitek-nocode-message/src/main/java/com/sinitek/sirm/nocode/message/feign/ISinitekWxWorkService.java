package com.sinitek.sirm.nocode.message.feign;

import com.sinitek.sirm.framework.frontend.support.TableResult;
import com.sinitek.sirm.wxwork.dto.WxWorkAccountQueryDTO;
import com.sinitek.sirm.wxwork.dto.WxWorkAccountResultDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @version 2025.0812
 * @since 1.0.0-SNAPSHOT
 */
@FeignClient(
        name = "${sinicube.sirmapp.remote.service-name:CLOUD-SIRMAPP}",
        contextId = "sinitekWxWorkService",
        url = "${sinicube.sirmapp.remote.url:}"
)
public interface ISinitekWxWorkService {
    @GetMapping("/frontend/api/wxwork/account/list")
    TableResult<WxWorkAccountResultDTO> list(@RequestParam WxWorkAccountQueryDTO wxWorkAccountQueryDTO);
}
