package com.sinitek.sirm.nocode.message.feign;

import com.sinitek.sirm.framework.frontend.support.PageDataParam;
import com.sinitek.sirm.framework.frontend.support.RequestResult;
import com.sinitek.sirm.framework.frontend.support.TableResult;
import com.sinitek.sirm.nocode.message.dto.SirmpmWxAccountDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2025.0812
 * @since 1.0.0-SNAPSHOT
 */
@FeignClient(
        name = "${sinicube.sirmapp.remote.service-name:CLOUD-SIRMAPP}",
        contextId = "sirmpmWxService",
        url = "${sinicube.sirmapp.remote.url:}"
)
public interface ISirmpmWxService {
    @GetMapping("/frontend/api/sirmpm/wxnumrel/list")
    TableResult<SirmpmWxAccountDTO> list(@RequestParam PageDataParam param);

    @ResponseBody
    @GetMapping(value = "/frontend/api/remote/sirmpm/wx/list", produces = MediaType.APPLICATION_JSON_VALUE)
    RequestResult<String> findByOrgIdList(@RequestBody List<String> orgIdList);
}
