package com.sinitek.sirm.nocode.message.config;

import com.google.common.collect.Maps;
import com.sinitek.sirm.common.utils.JsonUtil;
import com.sinitek.sirm.wxwork.config.WxCpProperties;
import lombok.Getter;
import me.chanjar.weixin.cp.api.WxCpService;
import me.chanjar.weixin.cp.api.impl.WxCpServiceImpl;
import me.chanjar.weixin.cp.config.impl.WxCpRedissonConfigImpl;
import me.chanjar.weixin.cp.message.WxCpMessageRouter;
import org.apache.commons.collections.MapUtils;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 2025.0427
 * @description
 * @since 1.0.0-SNAPSHOT
 */
@Configuration
@EnableConfigurationProperties({WxCpProperties.class})
public class ZdWxCpConfig {

    @Resource
    private RedissonClient redissonClient;


    private final WxCpProperties properties;
    @Getter
    private static final Map<String, WxCpMessageRouter> routers = Maps.newHashMap();
    private static final Map<String, WxCpService> cpServices = Maps.newHashMap();


    private static ZdWxCpConfig self;

    @Autowired
    public ZdWxCpConfig(WxCpProperties properties) {
        this.properties = properties;
        self = this;
    }

    public static WxCpService getCpService(String name) {
        return cpServices.get(name);
    }

    /**
     * 根据参数获取服务
     *
     * @param param 参数
     * @param key   key
     * @return 服务
     */

    public static WxCpService getCpServiceByParam(String param, String key) {
        WxCpService wxCpService = cpServices.get(key);
        if (Objects.nonNull(wxCpService)) {
            return wxCpService;
        }
        WxCpProperties.AppConfig appConfig = JsonUtil.toJavaObject(param, WxCpProperties.AppConfig.class);
        Map<String, WxCpProperties.AppConfig> appConfigs = new HashMap<>();
        appConfigs.put(key, appConfig);
        self.init(appConfigs);
        return cpServices.get(key);

    }

    @PostConstruct
    public void initServices() {
        Map<String, WxCpProperties.AppConfig> appConfigs = this.properties.getAppConfigs();
        init(appConfigs);
    }


    private void init(Map<String, WxCpProperties.AppConfig> appConfigs) {
        if (MapUtils.isNotEmpty(appConfigs)) {
            for (Map.Entry<String, WxCpProperties.AppConfig> entry : appConfigs.entrySet()) {
                init(entry);
            }
        }
    }


    private void init(Map.Entry<String, WxCpProperties.AppConfig> entry) {
        WxCpServiceImpl service = getWxCpService(entry);
        routers.put(entry.getKey(), this.newRouter(service));
        cpServices.put(entry.getKey(), service);
    }

    private WxCpServiceImpl getWxCpService(Map.Entry<String, WxCpProperties.AppConfig> entry) {
        WxCpRedissonConfigImpl configStorage = new WxCpRedissonConfigImpl(redissonClient, "wecom");
        configStorage.setCorpId(this.properties.getCorpId());
        configStorage.setAgentId(entry.getValue().getAgentId());
        configStorage.setCorpSecret(entry.getValue().getSecret());
        configStorage.setAesKey(entry.getValue().getAesKey());
        configStorage.setToken(entry.getValue().getToken());
        WxCpServiceImpl service = new WxCpServiceImpl();
        service.setWxCpConfigStorage(configStorage);
        return service;
    }


    private WxCpMessageRouter newRouter(WxCpService wxCpService) {
        return new WxCpMessageRouter(wxCpService);
    }
}
