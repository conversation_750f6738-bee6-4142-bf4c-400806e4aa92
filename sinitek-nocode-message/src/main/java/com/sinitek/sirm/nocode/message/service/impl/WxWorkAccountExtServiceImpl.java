package com.sinitek.sirm.nocode.message.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.sinitek.sirm.nocode.message.service.IWxWorkAccountExtService;
import com.sinitek.sirm.org.service.IOrgService;
import com.sinitek.sirm.wxwork.entity.WxWorkAccount;
import com.sinitek.sirm.wxwork.service.impl.WxWorkAccountServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 2025.0813
 * @since 1.0.0-SNAPSHOT
 */
@Service
@RequiredArgsConstructor
public class WxWorkAccountExtServiceImpl implements IWxWorkAccountExtService {
    private final WxWorkAccountServiceImpl wxWorkAccountService;
    private final IOrgService orgService;

    @Override
    public String getUserNameByWxWorkId(String wxWorkId) {
        WxWorkAccount one = wxWorkAccountService.getOne(Wrappers.<WxWorkAccount>lambdaQuery().eq(WxWorkAccount::getWxWorkId, wxWorkId).select(WxWorkAccount::getOrgId));
        if (one != null) {
            return orgService.getOrgIdByOrgName(one.getOrgId());
        }
        return wxWorkId;
    }
}
