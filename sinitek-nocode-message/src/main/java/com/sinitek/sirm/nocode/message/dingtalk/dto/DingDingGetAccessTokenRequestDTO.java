package com.sinitek.sirm.nocode.message.dingtalk.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @version 2025.0424
 * @description 钉钉参数
 * 原企业内部应用AgentId:3779443650
 * App ID:260d4157-ec47-4684-8f4a-408e1a85fbb3
 * @since 1.0.0-SNAPSHOT
 */
@Data
public class DingDingGetAccessTokenRequestDTO {
    @NotBlank(message = "Client ID不能为空")
    @ApiModelProperty(value = "Client ID", example = "dingaihzdfooeg2bjrhb", required = true)
    public String appKey;
    @NotBlank(message = "Client Secret不能为空")
    @ApiModelProperty(value = "Client Secret", example = "JRfHMrG6zNOlAusr39sjtRi6qrmor0C36ivlGS5Z4IGj0M5wjyyzBkhFx1D2_JQ9X", required = true)
    public String appSecret;
}
