package com.sinitek.sirm.nocode.ai.mind.core.app.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025-08-07 15:21
 */
@Data
@NoArgsConstructor
public class AppDTO {

    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("类型")
    private String type;

    @ApiModelProperty("版本")
    private String version;

    @ApiModelProperty("所有人")
    private String ownerId;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("介绍")
    private String intro;

    @ApiModelProperty("命名空间")
    private String namespace;
}
