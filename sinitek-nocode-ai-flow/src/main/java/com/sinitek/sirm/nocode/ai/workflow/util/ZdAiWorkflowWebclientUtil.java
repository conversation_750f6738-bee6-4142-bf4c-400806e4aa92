package com.sinitek.sirm.nocode.ai.workflow.util;

import com.sinitek.sirm.nocode.ai.workflow.dto.ZdAiWorkflowWebclientConfig;
import io.netty.handler.timeout.ReadTimeoutHandler;
import java.time.Duration;
import java.util.concurrent.TimeUnit;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.netty.http.client.HttpClient;
import reactor.netty.resources.ConnectionProvider;

/**
 * <AUTHOR>
 * @date 2025-08-13 11:02
 */
@Slf4j
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class ZdAiWorkflowWebclientUtil {

    private static final String CONNECTION_NAME = "ai-workflow-connection-pool";

    public static WebClient getWebClient(String baseUrl, String token,
        ZdAiWorkflowWebclientConfig config) {
        log.debug("ai-workflow request baseurl: {}", baseUrl);
        return WebClient.builder()
            .baseUrl(baseUrl)
            .defaultHeaders((headers) -> {
                headers.add(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE);
                headers.setBasicAuth(token);
            })
            .filter((request, next) -> {
                log.debug("ai-workflow request: {} {}", request.method(), request.url());
                return next.exchange(request)
                    .doOnNext(response ->
                        log.debug("ai-workflow response: {}", response.statusCode()));
            })
            .clientConnector(
                new ReactorClientHttpConnector(
                    HttpClient.create(ConnectionProvider.builder(CONNECTION_NAME)
                            .maxConnections(config.getMaxConnections())
                            .pendingAcquireTimeout(
                                Duration.ofSeconds(config.getPendingAcquireTimeout()))
                            .maxIdleTime(Duration.ofSeconds(config.getMaxIdleTime()))
                            .build())
                        .responseTimeout(Duration.ofSeconds(config.getTimeout()))
                        .doOnConnected(conn ->
                            conn.addHandlerLast(
                                new ReadTimeoutHandler(config.getTimeout(),
                                    TimeUnit.SECONDS))
                        )))
            .build();
    }

}
