package com.sinitek.sirm.nocode.ai.workflow.dto;

import com.sinitek.sirm.nocode.form.support.FormCodeSupplier;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 创建app入参对象；对应type CreateAppBody
 */
@Data
@ApiModel("简单创建参数")
@EqualsAndHashCode
public class SimpleCreateAppParamWrapperDTO implements FormCodeSupplier {

    @NotBlank(message = "表单编码不能为空")
    @ApiModelProperty("表单编码")
    private String formCode;

    @NotBlank(message = "名称不能为空")
    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("介绍")
    private String intro;

}
