package com.sinitek.sirm.nocode.ai.mind.core.app.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Created by IntelliJ IDEA.
 * User: 王志华
 * Date: 2018/2/18
 * Time: 下午11:56
 * To change this template use File | Settings | File Templates.
 */
@Data
@ApiModel(value = "登陆请求参数")
public class MindLoginRequestParam {

    @ApiModelProperty(value = "登录密码")
    private String userpwd;

    @ApiModelProperty(value = "用户名")
    private String username;

}
