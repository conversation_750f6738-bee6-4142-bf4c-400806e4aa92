package com.sinitek.sirm.nocode.ai.workflow.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025-08-13 11:13
 */
@Data
@NoArgsConstructor
@ApiModel("ai工作流webclient配置")
public class ZdAiWorkflowWebclientConfig {
    
    @ApiModelProperty("ai连接超时时间（秒）")
    private Integer timeout = 300;

    @ApiModelProperty("ai最大连接数")
    private Integer maxConnections = 50;

    @ApiModelProperty("ai异步响应超时（秒）")
    private Integer asyncResponseTimeout = 90;

    @ApiModelProperty("等待获取连接的最大时间（秒）")
    private Integer pendingAcquireTimeout = 90;

    @ApiModelProperty("连接在池中空闲多久后会被回收/关闭（秒）")
    private Integer maxIdleTime = 30;

}
