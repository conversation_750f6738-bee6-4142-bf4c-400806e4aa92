package com.sinitek.sirm.nocode.ai.workflow.dto;

import com.sinitek.sirm.nocode.form.support.FormCodeSupplier;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025-08-11 17:26
 */
@Data
@ApiModel("工作流查询参数")
public class ListAppWrapperDTO implements FormCodeSupplier {

    @NotBlank(message = "表单编码不能为空")
    @ApiModelProperty("表单编码")
    private String formCode;

    @ApiModelProperty("搜索关键词")
    private String searchKey;
}
