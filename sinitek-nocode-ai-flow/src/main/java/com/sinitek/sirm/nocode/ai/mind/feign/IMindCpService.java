package com.sinitek.sirm.nocode.ai.mind.feign;

import com.sinitek.sirm.framework.frontend.support.RequestResult;
import com.sinitek.sirm.nocode.ai.mind.core.app.dto.MindLoginRequestParam;
import com.sinitek.sirm.nocode.ai.mind.core.app.dto.MindLoginResult;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * SiniCube对外开放的接口
 *
 * <AUTHOR>
 * @date 2022/11/1
 */
@FeignClient(
    name = "${sinicube.mind.remote.service-name:CLOUD-MIND}",
    contextId = "mindAppRemoteService",
    url = "${sinicube.mind.remote.url:}"
)
public interface IMindCpService {
    /**
     * 模拟登陆
     * @param param
     * @return
     */
    @PostMapping("/frontend/api/login")
    @ApiOperation(value = "根据用户名和密码登陆")
    RequestResult<MindLoginResult> login(@RequestBody MindLoginRequestParam param);
}
