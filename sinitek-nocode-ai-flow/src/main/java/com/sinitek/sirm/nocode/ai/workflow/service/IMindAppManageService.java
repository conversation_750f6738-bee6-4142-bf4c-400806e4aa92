package com.sinitek.sirm.nocode.ai.workflow.service;

import com.sinitek.sirm.nocode.ai.mind.core.app.dto.AppDTO;
import com.sinitek.sirm.nocode.ai.mind.core.app.dto.AppDetailDTO;
import com.sinitek.sirm.nocode.ai.mind.core.app.dto.AppListItemDTO;
import com.sinitek.sirm.nocode.ai.workflow.dto.ListAppWrapperDTO;
import com.sinitek.sirm.nocode.ai.workflow.dto.MindAppUpdateWrapperDTO;
import com.sinitek.sirm.nocode.ai.workflow.dto.SimpleCreateAppParamWrapperDTO;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-08-11 15:55
 */
public interface IMindAppManageService {

    List<AppListItemDTO> findAppList(ListAppWrapperDTO param);

    String simpleCreateApp(SimpleCreateAppParamWrapperDTO param);

    AppDetailDTO getAppDetail(String appId);

    void deleteApp(String appId);

    void deleteAppByNamespaces(List<String> namespaceList);

    String copyApp(String appId);

    AppDTO updateApp(MindAppUpdateWrapperDTO param);
}
