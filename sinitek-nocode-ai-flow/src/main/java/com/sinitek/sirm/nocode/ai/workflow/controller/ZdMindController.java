package com.sinitek.sirm.nocode.ai.workflow.controller;

import com.sinitek.sirm.framework.frontend.support.RequestResult;
import com.sinitek.sirm.nocode.ai.workflow.dto.MindConfigDTO;
import com.sinitek.sirm.nocode.ai.workflow.service.ISinitekMindAppService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 智脑Controller
 *
 * <AUTHOR>
 * @version 2025.0729
 * @since 1.0.0
 */
@Slf4j
@RestController
@Api(value = "/frontend/api/nocode/mind", tags = "智脑接口")
@RequestMapping("/frontend/api/nocode/mind")
@Validated
public class ZdMindController {

    @Autowired
    private ISinitekMindAppService mindAppService;

    @ApiOperation(value = "获取智脑配置")
    @PostMapping(path = "/get-config")
    public RequestResult<MindConfigDTO> getMindConfig() {
        return new RequestResult<>(this.mindAppService.getMindConfig());
    }
}
