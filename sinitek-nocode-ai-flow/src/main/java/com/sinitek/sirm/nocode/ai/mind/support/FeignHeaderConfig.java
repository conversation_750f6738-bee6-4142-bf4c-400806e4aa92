package com.sinitek.sirm.nocode.ai.mind.support;

import static com.sinitek.sirm.nocode.ai.workflow.constant.ZdAiWorkflowMessageConstant.AUTHORIZATION_TOKEN_CANT_BLANK;

import com.sinitek.sirm.common.spring.SpringFactory;
import com.sinitek.sirm.framework.exception.BussinessException;
import com.sinitek.sirm.nocode.ai.mind.properties.ZdMindProperties;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2025-08-07 13:53
 */
@Slf4j
public class FeignHeaderConfig implements RequestInterceptor {

    @Override
    public void apply(RequestTemplate template) {
        ZdMindProperties properties = SpringFactory.getBean(
            ZdMindProperties.class);
        String authorizationToken = properties.getAuthorizationToken();
        if (StringUtils.isBlank(authorizationToken)) {
            log.error("智脑大模型访问token配置为空");
            throw new BussinessException(AUTHORIZATION_TOKEN_CANT_BLANK);
        }
        template.header("Authorization", authorizationToken);
    }

}
