package com.sinitek.sirm.nocode.ai.workflow.support;

import static com.sinitek.sirm.nocode.ai.workflow.constant.ZdAiWorkflowMessageConstant.REQUEST_FAILED;
import static com.sinitek.sirm.nocode.ai.workflow.util.AiWorkflowResponseWriteUtil.writeContent;
import static com.sinitek.sirm.nocode.ai.workflow.util.AiWorkflowResponseWriteUtil.writeSseErrRes;

import com.sinitek.sirm.common.utils.Base64Utils;
import com.sinitek.sirm.common.utils.JsonUtil;
import com.sinitek.sirm.framework.exception.BussinessException;
import com.sinitek.sirm.nocode.ai.mind.enumerate.SseResponseEventEnum;
import com.sinitek.sirm.nocode.ai.mind.properties.ZdMindProperties;
import com.sinitek.sirm.nocode.ai.workflow.dto.AiWorkflowReqeustMessageWrapperDTO;
import com.sinitek.sirm.nocode.ai.workflow.dto.AiWorkflowReqeustMessageWrapperDTO.MessageItem;
import com.sinitek.sirm.nocode.ai.workflow.dto.ZdAiWorkflowTriggerParamDTO;
import com.sinitek.sirm.nocode.ai.workflow.dto.ZdAiWorkflowWebclientConfig;
import com.sinitek.sirm.nocode.ai.workflow.properties.ZdAiWorkflowProperties;
import com.sinitek.sirm.nocode.ai.workflow.util.ZdAiWorkflowConvertUtil;
import com.sinitek.sirm.nocode.ai.workflow.util.ZdAiWorkflowWebclientUtil;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CountDownLatch;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpStatus;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.util.function.Tuple3;

/**
 * ai工作流请求线程
 *
 * <AUTHOR>
 * @date 2025-08-13 14:46
 */
@Slf4j
public class AiWorkflowReqeustRunnable implements Runnable {

    private static final String TRIGGER_ID_KEY = "triggerId";
    private static final String FORM_CODE_KEY = "formCode";
    private static final String TYPE_KEY = "type";
    private static final String REF_KEY = "ref";
    private static final String CHILD_REF_KEY = "childRef";
    private static final String SCHEMA_KEY = "schema";
    private static final String MODEL_KEY = "model";

    private ZdAiWorkflowProperties properties;

    private ZdMindProperties mindProperties;

    private ZdAiWorkflowTriggerParamDTO param;

    private SseEmitter sseEmitter;

    private String eventType;

    private String apiKey;

    public AiWorkflowReqeustRunnable(ZdAiWorkflowProperties properties,
        ZdMindProperties mindProperties, ZdAiWorkflowTriggerParamDTO param,
        SseEmitter sseEmitter,
        String apiKey,
        String eventType) {
        this.properties = properties;
        this.mindProperties = mindProperties;
        this.param = param;
        this.sseEmitter = sseEmitter;
        this.apiKey = apiKey;
        this.eventType = eventType;
    }

    @Override
    public void run() {
        String mindBaseUrl = this.mindProperties.getMindBaseUrl();
        String mindChatUri = this.mindProperties.getMindChatUri();

        String userMessage = this.param.getUserMessage();

        AiWorkflowReqeustMessageWrapperDTO messageObj = new AiWorkflowReqeustMessageWrapperDTO();

        if (StringUtils.isNotBlank(userMessage)) {
            MessageItem messageItem = AiWorkflowReqeustMessageWrapperDTO.buildUserMessage(
                userMessage);
            messageObj.setMessages(Collections.singletonList(messageItem));
        }

        String triggerId = this.param.getTriggerId();
        String formCode = this.param.getFormCode();
        String schemaBase64 = this.param.getSchema();

        String decodeSchema = Base64Utils.decode(schemaBase64);
        Map<String, Object> schema = JsonUtil.toMap(decodeSchema);

        Map<String, Object> model = this.param.getModel();

        Map<String, Object> variables = new HashMap<>();
        Tuple3<String, String, String> eventWrapper = ZdAiWorkflowConvertUtil.getEventWrapper(
            this.eventType);
        variables.put(TRIGGER_ID_KEY, triggerId);
        variables.put(FORM_CODE_KEY, formCode);
        variables.put(TYPE_KEY, eventWrapper.getT1());
        variables.put(REF_KEY, eventWrapper.getT2());
        variables.put(CHILD_REF_KEY, eventWrapper.getT3());
        variables.put(SCHEMA_KEY, schema);
        variables.put(MODEL_KEY, model);
        messageObj.setVariables(variables);

        String message = JsonUtil.toJsonString(messageObj);

        ParameterizedTypeReference<ServerSentEvent<String>> type =
            new ParameterizedTypeReference<ServerSentEvent<String>>() {
            };
        WebClient webClient = ZdAiWorkflowWebclientUtil.getWebClient(mindBaseUrl,
            this.apiKey,
            new ZdAiWorkflowWebclientConfig());
        try {
            Flux<ServerSentEvent<String>> flux = webClient.post()
                .uri(mindChatUri)
                .bodyValue(message)
                .retrieve()
                .onStatus((HttpStatus::isError),
                    clientResponse -> {
                        return clientResponse
                            .bodyToMono(String.class)
                            .defaultIfEmpty("")
                            .flatMap(body -> {
                                log.error(
                                    "triggerId: {} ai workflow client response status: {}, body: {}",
                                    triggerId, clientResponse.statusCode(), body);
                                HttpStatus httpStatus = clientResponse.statusCode();
                                String reasonPhrase = httpStatus.getReasonPhrase();
                                return Mono.error(
                                    new BussinessException(REQUEST_FAILED, reasonPhrase));
                            });
                    })
                .bodyToFlux(type);

            CountDownLatch latch = new CountDownLatch(1);
            flux.doOnNext(event -> {
                    // 处理业务逻辑
                    String eventName = event.event();
                    log.debug("triggerId: {},eventName: {},data: {}", triggerId, eventName,
                        event.data());
                    if (Objects.equals(SseResponseEventEnum.ANSWER.getValue(), eventName)) {
                        writeContent(this.sseEmitter, eventName, event.data());
                    } else if (Objects.equals(SseResponseEventEnum.ERROR.getValue(), eventName)) {
                        log.error("triggerId: {},eventName: {},data: {}", triggerId, eventName,
                            event.data());
                        String data = event.data();
                        Map<String, Object> errMap = JsonUtil.toMap(data);
                        String code = MapUtils.getString(errMap, "code");
                        String statusText = MapUtils.getString(errMap, "statusText");
                        throw new BussinessException(REQUEST_FAILED,
                            String.format("%s,%s", code, statusText));
                    }
                })
                .doOnError(e -> {
                    try {
                        log.error("triggerId: {},ai-workflow SSE 请求失败,{}", triggerId,
                            e.getMessage(), e);
                        writeSseErrRes(this.sseEmitter, e);
                    } finally {
                        latch.countDown();
                    }
                })
                .doOnComplete(() -> {
                    try {
                        log.info("triggerId: {},ai-workflow SSE 请求完成", triggerId);
                    } finally {
                        latch.countDown();
                    }
                })
                .subscribe();
            latch.await();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        } finally {
            this.sseEmitter.complete();
        }
    }
}
