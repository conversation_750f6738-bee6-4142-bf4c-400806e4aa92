package com.sinitek.sirm.nocode.ai.workflow.util;

import com.sinitek.sirm.framework.exception.BussinessException;
import com.sinitek.sirm.nocode.ai.mind.enumerate.ChatCompletionRequestMessageRoleEnum;
import com.sinitek.sirm.nocode.ai.mind.enumerate.SseResponseEventEnum;
import com.sinitek.sirm.nocode.ai.mind.model.Choice;
import com.sinitek.sirm.nocode.ai.mind.model.Delta;
import com.sinitek.sirm.nocode.ai.mind.model.TextAdaptGptResponseParams;
import com.sinitek.sirm.nocode.ai.mind.model.sse.GptResponse;
import com.sinitek.sirm.nocode.ai.mind.model.sse.SseResponseType;
import java.util.Collections;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

/**
 * <AUTHOR>
 * @date 2025-08-13 14:58
 */
@Slf4j
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class AiWorkflowResponseWriteUtil {

    /**
     * 发送消息
     *
     * @param emitter sse
     * @param event 事件
     * @param data 数据
     */
    public static void write(SseEmitter emitter, String event, SseResponseType data) {
        try {
            SseEmitter.SseEventBuilder eventBuilder = SseEmitter.event()
                .name(event)
                .data(data);
            emitter.send(eventBuilder);
        } catch (Exception e) {
            log.error("SSE消息发送失败,SSE事件连接已断开或已complete或已超时,{}", e.getMessage(),
                e);
        }
    }

    /**
     * 发送内容，只有内容，没有字段等信息
     *
     * @param event 事件
     * @param content 数据，不能包含\n，否则\n会被修改为\ndata:
     */
    public static void writeContent(SseEmitter emitter, String event, String content) {
        try {
            SseEmitter.SseEventBuilder eventBuilder = SseEmitter.event()
                .name(event)
                .data(content);
            emitter.send(eventBuilder);
        } catch (Exception e) {
            log.error("SSE消息发送失败,SSE事件连接已断开或已complete或已超时,{}", e.getMessage(),
                e);
        }
    }

    /**
     * 发送结束消息
     */
    public static void writeDone(SseEmitter emitter) {
        writeContent(emitter, SseResponseEventEnum.ANSWER.getValue(), "[DONE]");
    }

    /**
     * 发送结束消息
     */
    public static void writeStop(SseEmitter emitter) {
        GptResponse gptResponse = textAdaptGptResponse(
            TextAdaptGptResponseParams.builder()
                .finishReason("stop")
                .build());
        write(emitter, SseResponseEventEnum.ANSWER.getValue(), gptResponse);
    }

    /**
     * 将文本内容适配为GPT响应格式
     *
     * @param params 参数对象
     * @return GPT响应格式的数据
     */
    private static GptResponse textAdaptGptResponse(TextAdaptGptResponseParams params) {
        return GptResponse.builder()
            .extraData(params.getExtraData())
            .id("")
            .object("")
            .created(0L)
            .model(params.getModel() != null ? params.getModel() : "")
            .choices(Collections.singletonList(Choice.builder()
                .delta(Delta.builder()
                    .role(ChatCompletionRequestMessageRoleEnum.ASSISTANT.getValue())
                    .content(params.getText())
                    .reasoningContent(params.getReasoningContent())
                    .build())
                .index(0)
                .finish_reason(params.getFinishReason())
                .build()))
            .build();
    }


    /**
     * 发送工作流结束消息
     * <p>writeStop + writeDone 两个个方法的集合体</p>
     */
    public static void writeSseCompleteRes(SseEmitter emitter) {
        writeStop(emitter);
        writeDone(emitter);
    }

    /**
     * 发送sse异常消息,并关闭sse
     */
    public static void writeSseErrRes(SseEmitter emitter, Throwable e) {
        if (e instanceof BussinessException) {
            // 业务上的异常
            writeContent(emitter, SseResponseEventEnum.ERROR.getValue(),
                ((BussinessException) e).getErrorMsg());
        } else {
            // 系统异常
            writeContent(emitter, SseResponseEventEnum.ERROR.getValue(), e.getMessage());
        }
    }
}
