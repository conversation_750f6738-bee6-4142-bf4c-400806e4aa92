package com.sinitek.sirm.nocode.ai.mind.core.app.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025-08-07 15:06
 */
@Data
@ApiModel(description = "获取应用列表请求参数")
public class ListAppDTO {

    @ApiModelProperty("父级ID")
    private String parentId;

    @ApiModelProperty("应用类型")
    private List<String> type; // AppTypeEnum

    @ApiModelProperty("是否获取最近聊天的应用")
    private Boolean getRecentlyChat;

    @ApiModelProperty("搜索关键词")
    private String searchKey;

    @ApiModelProperty("命名空间")
    private String namespace;
}
