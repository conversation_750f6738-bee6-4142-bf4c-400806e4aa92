package com.sinitek.sirm.nocode.ai.workflow.controller;

import com.sinitek.sirm.common.user.factory.CurrentUserFactory;
import com.sinitek.sirm.framework.frontend.support.RequestResult;
import com.sinitek.sirm.nocode.ai.mind.core.app.dto.AppDTO;
import com.sinitek.sirm.nocode.ai.mind.core.app.dto.AppDetailDTO;
import com.sinitek.sirm.nocode.ai.mind.core.app.dto.AppListItemDTO;
import com.sinitek.sirm.nocode.ai.mind.core.app.dto.DelAppByNamespaceParamDTO;
import com.sinitek.sirm.nocode.ai.workflow.dto.ListAppWrapperDTO;
import com.sinitek.sirm.nocode.ai.workflow.dto.MindAppBaseParamDTO;
import com.sinitek.sirm.nocode.ai.workflow.dto.MindAppUpdateWrapperDTO;
import com.sinitek.sirm.nocode.ai.workflow.dto.SimpleCreateAppParamWrapperDTO;
import com.sinitek.sirm.nocode.ai.workflow.dto.ZdAiWorkflowBindParamDTO;
import com.sinitek.sirm.nocode.ai.workflow.dto.ZdAiWorkflowConfigDTO;
import com.sinitek.sirm.nocode.ai.workflow.dto.ZdAiWorkflowListParamPO;
import com.sinitek.sirm.nocode.ai.workflow.dto.ZdAiWorkflowTriggerParamDTO;
import com.sinitek.sirm.nocode.ai.workflow.dto.ZdFormAiWorkflowInfoDTO;
import com.sinitek.sirm.nocode.ai.workflow.service.IMindAiWorkflowTriggerService;
import com.sinitek.sirm.nocode.ai.workflow.service.IMindAppManageService;
import com.sinitek.sirm.nocode.ai.workflow.service.IZdAiWorkflowService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.Date;
import java.util.List;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

/**
 * 应用AI设置Controller
 *
 * <AUTHOR>
 * @version 2025.0729
 * @description 针对表【zd_app_ai_setting(应用AI设置)】的控制器
 * @since 1.0.0-SNAPSHOT
 */
@Slf4j
@RestController
@Api(value = "/frontend/api/nocode/ai/workflow", tags = "ai工作流接口")
@RequestMapping("/frontend/api/nocode/ai/workflow")
@Validated
public class ZdAppAiWorkflowController {

    @Autowired
    private IMindAppManageService mindAppManageService;

    @Autowired
    private IZdAiWorkflowService zdAiWorkflowService;

    @Autowired
    private IMindAiWorkflowTriggerService mindAiWorkflowTriggerService;

    @ApiOperation(value = "触发工作流")
    @PostMapping(path = "/trigger")
    public SseEmitter triggerAiWorkFlow(
        @RequestBody @Valid ZdAiWorkflowTriggerParamDTO param) {
        String orgId = CurrentUserFactory.getOrgId();
        param.setOperatorId(orgId);
        param.setOperateTime(new Date());
        return this.mindAiWorkflowTriggerService.trigger(param);
    }

    @ApiOperation(value = "获取列表")
    @PostMapping(path = "/list")
    public RequestResult<List<AppListItemDTO>> list(@RequestBody @Valid ListAppWrapperDTO param) {
        return new RequestResult<>(this.mindAppManageService.findAppList(param));
    }

    @ApiOperation(value = "创建")
    @PostMapping(path = "/simple-create")
    public RequestResult<String> simpleCreate(
        @RequestBody @Valid SimpleCreateAppParamWrapperDTO param) {
        return new RequestResult<>(this.mindAppManageService.simpleCreateApp(param));
    }

    @ApiOperation(value = "获取应用详情")
    @PostMapping(path = "/detail")
    public RequestResult<AppDetailDTO> getDetailByAppId(
        @RequestBody @Valid MindAppBaseParamDTO param) {
        return new RequestResult<>(this.mindAppManageService.getAppDetail(param.getAppId()));
    }

    @ApiOperation(value = "复制应用")
    @PostMapping(path = "/copy")
    public RequestResult<String> copy(@RequestBody @Valid MindAppBaseParamDTO param) {
        return new RequestResult<>(this.mindAppManageService.copyApp(param.getAppId()));
    }

    @ApiOperation(value = "删除应用")
    @PostMapping(path = "/delete")
    public RequestResult<Void> delete(@RequestBody @Valid MindAppBaseParamDTO param) {
        this.mindAppManageService.deleteApp(param.getAppId());
        return new RequestResult<>();
    }

    @ApiOperation(value = "删除应用")
    @PostMapping(path = "/delete-by-namespace")
    public RequestResult<Void> deleteByNamespace(
        @RequestBody @Valid DelAppByNamespaceParamDTO param) {
        this.mindAppManageService.deleteAppByNamespaces(param.getNamespaceList());
        return new RequestResult<>();
    }

    @ApiOperation(value = "更新应用")
    @PostMapping(path = "/update")
    public RequestResult<AppDTO> update(@RequestBody @Valid MindAppUpdateWrapperDTO param) {
        return new RequestResult<>(this.mindAppManageService.updateApp(param));
    }

    @ApiOperation(value = "获取ai工作流绑定信息")
    @PostMapping(path = "/get-ai-work-flow-bind-info")
    public RequestResult<ZdAiWorkflowConfigDTO> getAiWorkflowBindInfoByAppId(
        @RequestBody @Valid MindAppBaseParamDTO param) {
        return new RequestResult<>(this.zdAiWorkflowService.getBindInfoByAppId(param.getAppId()));
    }

    @ApiOperation(value = "加载表单工作流")
    @PostMapping(path = "/list-by-form-code")
    public RequestResult<List<ZdFormAiWorkflowInfoDTO>> listByFormCode(
        @RequestBody @Valid ZdAiWorkflowListParamPO param) {
        return new RequestResult<>(
            this.zdAiWorkflowService.findConfigByFormCode(param.getFormCode()));
    }

    @ApiOperation(value = "更新应用")
    @PostMapping(path = "/bind-ai-work-flow")
    public RequestResult<AppDTO> bindAiWorkflow(
        @RequestBody @Valid ZdAiWorkflowBindParamDTO param) {
        String orgId = CurrentUserFactory.getOrgId();
        param.setOperatorId(orgId);
        param.setOperateTime(new Date());
        this.zdAiWorkflowService.bindAiWorkFlow(param);
        return RequestResult.success();
    }

    @ApiOperation(value = "删除ai工作流绑定关系")
    @PostMapping(path = "/delete-ai-work-flow-bind")
    public RequestResult<Void> deleteAiWorkflowBind(
        @RequestBody @Valid MindAppBaseParamDTO param) {
        this.zdAiWorkflowService.deleteByAppId(param.getAppId());
        return RequestResult.success();
    }
}
