package com.sinitek.sirm.nocode.ai.mind.support.openapi.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @date 2025-08-14 10:43
 */
@Data
@NoArgsConstructor
@ApiModel("API密钥创建请求DTO")
public class CreateApiKeyRequestDTO {

    /**
     * 关联的应用ID（可选）
     */
    @ApiModelProperty("关联的应用ID")
    private String appId;

    /**
     * 密钥名称
     */
    @ApiModelProperty("密钥名称")
    private String name;

    /**
     * 使用限制配置
     */
    @ApiModelProperty("使用限制配置")
    private LimitDTO limit;

    /**
     * 限制配置内嵌类
     */
    @Data
    @SuperBuilder
    @NoArgsConstructor
    @ApiModel("限制配置")
    public static class LimitDTO {

        /**
         * 过期时间（可选）
         */
        @ApiModelProperty("过期时间")
        private Date expiredTime;

        /**
         * 最大使用点数（可选，-1表示无限制）
         */
        @ApiModelProperty("最大使用点数")
        private Long maxUsagePoints;
    }
}
