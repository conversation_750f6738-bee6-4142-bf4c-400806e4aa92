package com.sinitek.sirm.nocode.ai.workflow.constant;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025-08-11 13:52
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class ZdAiWorkflowMessageConstant {

    /**
     * 3000024001=Spring属性[nocode.mind.userName]不能为空,请填写正确配置
     */
    public static final String USER_NAME_CANT_BLANK = "3000024001";

    /**
     * 3000024002=Spring属性[nocode.mind.userPwd]不能为空,请填写正确配置
     */
    public static final String USER_PWD_CANT_BLANK = "3000024002";

    /**
     * 3000024003=Spring属性[nocode.mind.authorizationToken]不能为空,请填写正确配置
     */
    public static final String AUTHORIZATION_TOKEN_CANT_BLANK = "3000024003";

    /**
     * 3000024004=请求失败,{0}
     */
    public static final String REQUEST_FAILED = "3000024004";

    /**
     * 3000024005=API Key不能为空
     */
    public static final String API_KEY_CANT_BLANK = "3000024005";

    /**
     * 3000024006=API Key为空,无法继续沿用
     */
    public static final String API_KEY_IS_EMPTY = "3000024006";

    /**
     * 3000024007=创建API Key失败,{0}
     */
    public static final String CREATE_API_KEY_FAILED = "3000024007";
}
