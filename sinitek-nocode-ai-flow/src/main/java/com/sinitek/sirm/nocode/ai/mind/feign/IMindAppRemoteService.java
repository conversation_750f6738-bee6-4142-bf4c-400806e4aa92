package com.sinitek.sirm.nocode.ai.mind.feign;


import com.sinitek.sirm.nocode.ai.mind.common.support.ApiResponse;
import com.sinitek.sirm.nocode.ai.mind.core.app.dto.AppDTO;
import com.sinitek.sirm.nocode.ai.mind.core.app.dto.AppDetailDTO;
import com.sinitek.sirm.nocode.ai.mind.core.app.dto.AppListItemDTO;
import com.sinitek.sirm.nocode.ai.mind.core.app.dto.AppUpdateDTO;
import com.sinitek.sirm.nocode.ai.mind.core.app.dto.DelAppByNamespaceParamDTO;
import com.sinitek.sirm.nocode.ai.mind.core.app.dto.ListAppDTO;
import com.sinitek.sirm.nocode.ai.mind.core.app.dto.SimpleCreateAppParamDTO;
import com.sinitek.sirm.nocode.ai.mind.support.FeignHeaderConfig;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @date 2025-08-07 08:45
 */
@FeignClient(
    name = "${sinicube.mind.remote.service-name:CLOUD-MIND}",
    contextId = "mindCoreAppRemoteService",
    url = "${sinicube.mind.remote.url:}",
    configuration = FeignHeaderConfig.class
)
public interface IMindAppRemoteService {

    @PostMapping("/mind/open-api/core/app/list")
    @ApiOperation("获取应用列表")
    ApiResponse<List<AppListItemDTO>> getAppList(@RequestBody ListAppDTO request);

    @PostMapping("/mind/open-api/core/app/simple-create")
    @ApiOperation("创建应用")
    ApiResponse<String> simpleCreateApp(@RequestBody SimpleCreateAppParamDTO param);

    @GetMapping("/mind/open-api/core/app/detail")
    @ApiOperation("应用详情")
    ApiResponse<AppDetailDTO> getAppDetail(@RequestParam("appId") String appId);

    @PostMapping("/mind/open-api/core/app/del")
    @ApiOperation("删除应用")
    ApiResponse<Void> deleteApp(@RequestParam("appId") String appId);

    @PostMapping("/mind/open-api/core/app/del-by-namespace")
    @ApiOperation("删除应用-通过namespace字段")
    ApiResponse<Void> deleteAppByNamespace(@RequestBody DelAppByNamespaceParamDTO request);

    @GetMapping("/mind/open-api/core/app/copy")
    @ApiOperation("复制应用")
    ApiResponse<String> copyApp(@RequestParam("appId") String appId);

    @PostMapping("/mind/open-api/core/app/update")
    @ApiOperation("更新应用")
    ApiResponse<AppDTO> updateApp(@RequestParam String appId,
        @RequestBody AppUpdateDTO params);
}
