package com.sinitek.sirm.nocode.ai.workflow.util;

import com.sinitek.sirm.common.utils.IdEncryptUtil;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2025-08-13 14:58
 */
@Slf4j
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class AiWorkflowApiKeyUtil {

    public static String encryptApiKey(String rowApiKey) {
        return IdEncryptUtil.encrypt(rowApiKey);
    }

    public static String decryptApiKey(String encryptApiKey) {
        return IdEncryptUtil.decrypt(encryptApiKey);
    }
}
