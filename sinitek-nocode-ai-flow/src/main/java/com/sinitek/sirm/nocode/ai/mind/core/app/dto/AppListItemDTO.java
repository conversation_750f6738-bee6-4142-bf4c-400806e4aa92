package com.sinitek.sirm.nocode.ai.mind.core.app.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.sinitek.sirm.common.utils.GlobalConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * app列表查询返回值DTO,对应type:AppListItemType
 */
@Data
@ApiModel(description = "应用列表项")
@NoArgsConstructor
public class AppListItemDTO {

    @ApiModelProperty("应用ID")
    private String _id;

    @ApiModelProperty("应用名称")
    private String name;

    @ApiModelProperty("应用介绍")
    private String intro;

    @ApiModelProperty("应用类型")
    private String type;

    @JsonFormat(pattern = GlobalConstant.TIME_FORMAT_THIRTEEN)
    @ApiModelProperty("更新时间")
    private Date updateTime;

    public String getId() {
        return this._id;
    }
} 