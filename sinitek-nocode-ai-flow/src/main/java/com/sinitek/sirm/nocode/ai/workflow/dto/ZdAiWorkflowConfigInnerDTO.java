package com.sinitek.sirm.nocode.ai.workflow.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025-08-12 16:55
 */
@Data
@NoArgsConstructor
@ApiModel(value = "ZdAiWorkflowConfigInnerDTO", description = "AI工作流配置(内部使用,含有apikey不允许返回给前端)")
public class ZdAiWorkflowConfigInnerDTO {

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("事件类型")
    private String eventType;

    @ApiModelProperty("工作流应用id")
    private String appId;

    @ApiModelProperty("表单编码")
    private String formCode;

    @ApiModelProperty("加密的apiKey")
    private String encryptApikey;

}
