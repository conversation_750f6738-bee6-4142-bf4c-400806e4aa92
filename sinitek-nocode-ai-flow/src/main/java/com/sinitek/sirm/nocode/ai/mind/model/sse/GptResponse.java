package com.sinitek.sirm.nocode.ai.mind.model.sse;

import com.sinitek.sirm.nocode.ai.mind.model.Choice;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * Gpt返回结果
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class GptResponse implements SseResponseType {

    private Map<String, Object> extraData;
    private String id;
    private String object;
    private Long created;
    private String model;
    private List<Choice> choices;
}