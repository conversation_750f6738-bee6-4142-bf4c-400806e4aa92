package com.sinitek.sirm.nocode.ai.mind.common.support;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 统一API响应DTO
 *
 * <AUTHOR>
 * date 2025-07-01
 * 描述：所有接口统一返回结构
 */
@Data
@ApiModel(description = "统一API响应")
public class ApiResponse<T> {

    @ApiModelProperty("状态码")
    private int code;

    @ApiModelProperty("状态文本")
    private String statusText;

    @ApiModelProperty("消息")
    private String message;

    @ApiModelProperty("数据")
    private T data;

    public ApiResponse() {
    }

    public ApiResponse(int code, String statusText, String message, T data) {
        this.code = code;
        this.statusText = statusText;
        this.message = message;
        this.data = data;
    }

    /**
     * 判断是否成功
     */
    public boolean isSuccess() {
        return this.getCode() == 200;
    }

    public static <T> ApiResponse<T> success(T data) {
        return new ApiResponse<>(200, "", "", data);
    }

    public static <T> ApiResponse<T> success() {
        return new ApiResponse<>(200, "", "", null);
    }

    public static <T> ApiResponse<T> error(int code, String message) {
        return new ApiResponse<>(code, "error", message, null);
    }

    public static <T> ApiResponse<T> error(String message) {
        return new ApiResponse<>(500, "error", message, null);
    }
} 