package com.sinitek.sirm.nocode.ai.workflow.service.impl;

import com.sinitek.sirm.common.utils.JsonUtil;
import com.sinitek.sirm.framework.exception.BussinessException;
import com.sinitek.sirm.framework.frontend.support.RequestResult;
import com.sinitek.sirm.nocode.ai.mind.core.app.dto.MindLoginRequestParam;
import com.sinitek.sirm.nocode.ai.mind.core.app.dto.MindLoginResult;
import com.sinitek.sirm.nocode.ai.mind.feign.IMindCpService;
import com.sinitek.sirm.nocode.ai.mind.properties.ZdMindProperties;
import com.sinitek.sirm.nocode.ai.workflow.constant.ZdAiWorkflowMessageConstant;
import com.sinitek.sirm.nocode.ai.workflow.dto.MindConfigDTO;
import com.sinitek.sirm.nocode.ai.workflow.service.ISinitekMindAppService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025-08-11 11:38
 */
@Slf4j
@Service
public class SinitekMindAppServiceImpl implements ISinitekMindAppService {

    @Autowired
    private ZdMindProperties properties;

    @Autowired
    private IMindCpService mindCpService;

    @Override
    public MindConfigDTO getMindConfig() {
        String userName = this.properties.getUserName();
        String userPwd = this.properties.getUserPwd();

        if (StringUtils.isBlank(userName)) {
            log.error("智脑大模型用户名配置为空");
            throw new BussinessException(ZdAiWorkflowMessageConstant.USER_NAME_CANT_BLANK);
        }

        if (StringUtils.isBlank(userPwd)) {
            log.error("智脑大模型用户密码配置为空");
            throw new BussinessException(ZdAiWorkflowMessageConstant.USER_PWD_CANT_BLANK);
        }

        MindLoginRequestParam param = new MindLoginRequestParam();
        param.setUsername(userName);
        param.setUserpwd(userPwd);
        RequestResult<MindLoginResult> requestResult = this.mindCpService.login(param);
        if (requestResult.isSuccess()) {
            MindLoginResult result = requestResult.getData();
            MindConfigDTO config = new MindConfigDTO();
            config.setMindHost(this.properties.getMindHost());
            config.setAccessToken(result.getAccesstoken());
            return config;
        } else {
            if (log.isErrorEnabled()) {
                log.error("获取智脑token失败 param: {},result: {}", JsonUtil.toJsonString(param),
                    JsonUtil.toJsonString(requestResult));
            }
            throw new BussinessException(true, requestResult.getMessage());
        }

    }
}
