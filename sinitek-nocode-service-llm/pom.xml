<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <artifactId>sinitek-nocode-service-llm</artifactId>

    <dependencies>

        <dependency>
            <groupId>com.sinitek.sirm</groupId>
            <artifactId>sinitek-nocode-common</artifactId>
        </dependency>
        <!-- 测试依赖 -->
        <dependency>
            <artifactId>spring-boot-starter-test</artifactId>
            <groupId>org.springframework.boot</groupId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-webflux</artifactId> <!-- 非阻塞客户端 -->
        </dependency>
    </dependencies>
    <modelVersion>4.0.0</modelVersion>

    <packaging>jar</packaging>

    <parent>
        <artifactId>sinitek-nocode</artifactId>
        <groupId>com.sinitek.sirm</groupId>
        <version>${revision}</version>
    </parent>

    <properties>
        <skipTests>false</skipTests>
        <maven.test.skip>false</maven.test.skip>
    </properties>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.22.2</version>
                <configuration>
                    <skipTests>false</skipTests>
                    <testFailureIgnore>false</testFailureIgnore>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
