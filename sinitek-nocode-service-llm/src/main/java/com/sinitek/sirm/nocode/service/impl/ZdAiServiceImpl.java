package com.sinitek.sirm.nocode.service.impl;

import cn.hutool.json.JSONUtil;
import com.sinitek.sirm.common.utils.JsonUtil;
import com.sinitek.sirm.framework.exception.BussinessException;
import com.sinitek.sirm.nocode.constant.LlmGeneratorConstant;
import com.sinitek.sirm.nocode.dto.LlmAgentCompletionsRequestDTO;
import com.sinitek.sirm.nocode.dto.LlmAgentCompletionsResponseDTO;
import com.sinitek.sirm.nocode.dto.LlmAgentGenFunctionDTO;
import com.sinitek.sirm.nocode.dto.LlmChatCompletionsRequestDTO;
import com.sinitek.sirm.nocode.dto.LlmChatCompletionsResponseDTO;
import com.sinitek.sirm.nocode.dto.LlmChatGenDiagramDTO;
import com.sinitek.sirm.nocode.dto.LlmChatGenDiagramResponseDTO;
import com.sinitek.sirm.nocode.dto.LlmChatSqlDTO;
import com.sinitek.sirm.nocode.form.dto.ZdPageFormDTO;
import com.sinitek.sirm.nocode.form.service.IZdPageFormService;
import com.sinitek.sirm.nocode.service.ILlmService;
import com.sinitek.sirm.nocode.service.IZdAiService;
import com.sinitek.sirm.nocode.support.util.SqlQueryUtil;
import com.sinitek.sirm.nocode.util.LlmTextUtil;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.StandardCopyOption;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

/**
 * 简化的AI服务实现
 *
 * <p>直接调用统一的LLM服务，消除复杂的中间层，实现简洁的调用结构。
 * 调用层次：Controller → SimplifiedAiService → UnifiedLlmService</p>
 *
 * <AUTHOR>
 * @since 2025/1/18
 */
@Service
@Slf4j
public class ZdAiServiceImpl implements IZdAiService {

    @Autowired
    private ILlmService llmService;

    @Autowired
    private SqlQueryUtil sqlQueryUtil;

    @Autowired
    private IZdPageFormService pageFormService;

    /**
     * 智能体生成脚本
     * 
     * <p>调用层次：agentGenerateFunction → llmService.agentCompletions</p>
     */
    @Override
    public LlmChatCompletionsResponseDTO agentGenerateFunction(LlmAgentGenFunctionDTO request, List<MultipartFile> images) {
        log.info("开始智能体生成脚本，提示词: {}", request.getPrompt());
        
        // 构建代理请求
        LlmChatCompletionsRequestDTO chatRequest = new LlmChatCompletionsRequestDTO();
        chatRequest.setPrompt(request.getPrompt());
        chatRequest.setChatId(LlmGeneratorConstant.CHAT_GEN_SCHEMA);
        chatRequest.setSessionId(request.getSessionId());
        
        // 设置参数
        Map<String, Object> params = new HashMap<>();
        params.put("schema", request.getSchema());
        chatRequest.setParams(params);
        
        // 转换图片文件
        List<File> files = convertMultipartFilesToFiles(images);
        chatRequest.setImages(files);
        
        // 调用LLM服务
        LlmChatCompletionsResponseDTO response = llmService.chatCompletions(chatRequest);
        
        // 处理响应文本
        String processedText = processAgentResponseText(response);
        response.setText(LlmTextUtil.formatToJS(processedText));
        
        log.info("智能体生成脚本完成，响应长度: {}", response.getText().length());
        return response;
    }

    /**
     * 智能生成图表
     * 
     * <p>调用层次：agentGenerateDiagram → generateSqlAndQueryData → generateDiagramFromData</p>
     */
    @Override
    public LlmChatGenDiagramResponseDTO agentGenerateDiagram(LlmChatGenDiagramDTO request, List<MultipartFile> images) {
        log.info("开始生成图表，表单代码: {}, 需求: {}", request.getFormCode(), request.getPrompt());
        
        List<File> files = convertMultipartFilesToFiles(images);
        
        try {
            // 1. 生成SQL并查询数据
            List<Map<String, Object>> queryResult = generateSqlAndQueryData(request, files);
            
            // 2. 基于数据生成图表
            String diagramCode = generateDiagramFromData(request.getPrompt(), queryResult);
            
            // 3. 构建响应
            LlmChatGenDiagramResponseDTO response = new LlmChatGenDiagramResponseDTO();
            response.setSessionId(request.getSessionId());
            response.setText(diagramCode);
            
            log.info("图表生成完成，会话ID: {}", request.getSessionId());
            return response;
            
        } catch (Exception e) {
            log.error("图表生成失败，表单代码: {}, 错误: {}", request.getFormCode(), e.getMessage(), e);
            
            // 如果SQL执行失败，尝试重新生成
            if (e.getMessage().contains("SQL") || e.getMessage().contains("PostgreSQL")) {
                return retryGenerateDiagram(request, files, e);
            }
            
            throw new BussinessException("图表生成失败: " + e.getMessage(), e);
        }
    }

    /**
     * 生成统计提示
     * 
     * <p>调用层次：generateStatisticPrompt → llmService.chatCompletions</p>
     */
    @Override
    public List<String> generateStatisticPrompt(String formCode) {
        log.info("开始生成统计提示，表单代码: {}", formCode);
        
        // 获取表单配置
        ZdPageFormDTO formConfig = pageFormService.view(formCode);
        if (formConfig == null) {
            throw new BussinessException("表单不存在，表单代码: " + formCode);
        }

        // 构建AI提示词
        String prompt = JSONUtil.toJsonStr(formConfig.getPageData()) + 
            "\n\n上面是一个表格数据配置，请你分析出用户可能想做的数据统计，用自然语言表达,给出5个用户可能要的例子，返回给我一个JSON数组,比如" +
            "\n\n```json\n[\"统计一下数据提交数量，按天统计\",\"统计一下数据提交数量，按月统计\"]\n```";

        LlmChatCompletionsRequestDTO requestDTO = new LlmChatCompletionsRequestDTO();
        requestDTO.setPrompt(prompt);
        requestDTO.setFastLlm(true);

        // 调用LLM服务
        LlmChatCompletionsResponseDTO responseDTO = llmService.chatCompletions(requestDTO);
        String text = LlmTextUtil.formatToJson(responseDTO.getText());
        
        try {
            List<String> result = JSONUtil.toList(text, String.class);
            log.info("统计提示生成完成，生成了{}个建议", result.size());
            return result;
        } catch (Exception e) {
            log.error("解析统计建议失败: {}", e.getMessage(), e);
            throw new BussinessException("解析统计建议失败:" + text);
        }
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 生成SQL并查询数据
     */
    private List<Map<String, Object>> generateSqlAndQueryData(LlmChatGenDiagramDTO request, List<File> files) {
        // 构建SQL生成请求
        LlmChatSqlDTO sqlRequest = new LlmChatSqlDTO();
        sqlRequest.setPrompt(request.getPrompt());
        sqlRequest.setFormCode(request.getFormCode());
        sqlRequest.setSessionId(request.getSessionId());
        sqlRequest.setImages(files);

        // 生成SQL
        String generatedSql = llmService.formSqlGenerate(sqlRequest);
        log.debug("生成的SQL语句: {}", generatedSql);

        // 执行SQL查询
        List<Map<String, Object>> queryResult = sqlQueryUtil.executeQuery(generatedSql);
        log.debug("查询结果记录数: {}", queryResult.size());
        
        return queryResult;
    }

    /**
     * 基于查询数据生成图表代码
     */
    private String generateDiagramFromData(String userPrompt, List<Map<String, Object>> data) {
        // 预处理数据
        List<Map<String, Object>> processedData = preprocessDataForDiagram(data);

        // 构建图表生成提示词
        String diagramPrompt = String.format(
            "需求：%s \n\n数据：%s \n\n请根据上面的需求和数据生成mermaid代码。",
            userPrompt,
            JSONUtil.toJsonStr(processedData)
        );

        // 生成图表
        String diagramText = llmService.mermaidGenerate(diagramPrompt);
        return "```mermaid\n" + LlmTextUtil.formatToMermaid(diagramText) + "\n```";
    }

    /**
     * 重试图表生成
     */
    private LlmChatGenDiagramResponseDTO retryGenerateDiagram(LlmChatGenDiagramDTO request, List<File> files, Exception sqlE) {
        log.warn("SQL执行失败，准备重试。错误信息: {}", sqlE.getMessage());
        
        // 构建包含错误信息的增强提示词
        String enhancedPrompt = request.getPrompt() + 
            "\n\n上次SQL执行出现错误：" + sqlE.getMessage() +
            "\n请根据错误信息调整SQL语句。";

        // 创建重试请求
        LlmChatGenDiagramDTO retryRequest = new LlmChatGenDiagramDTO();
        retryRequest.setPrompt(enhancedPrompt);
        retryRequest.setFormCode(request.getFormCode());
        retryRequest.setSessionId(request.getSessionId());

        log.info("开始重试图表生成，增强后的需求: {}", enhancedPrompt);
        
        try {
            // 重新生成SQL并查询数据
            List<Map<String, Object>> queryResult = generateSqlAndQueryData(retryRequest, files);
            
            // 基于数据生成图表
            String diagramCode = generateDiagramFromData(request.getPrompt(), queryResult);
            
            // 构建响应
            LlmChatGenDiagramResponseDTO response = new LlmChatGenDiagramResponseDTO();
            response.setSessionId(request.getSessionId());
            response.setText(diagramCode);
            
            log.info("重试图表生成完成");
            return response;
            
        } catch (Exception e) {
            log.error("重试后仍然失败，表单代码: {}, 错误: {}", request.getFormCode(), e.getMessage(), e);
            throw new BussinessException("重试后仍然失败: " + e.getMessage(), e);
        }
    }

    /**
     * 处理智能体响应文本
     */
    private String processAgentResponseText(LlmChatCompletionsResponseDTO responseDTO) {
        String text;
        if (responseDTO.getFinishFlag() == LlmGeneratorConstant.FINISH_FLAG_COMPLETE) {
            text = responseDTO.getText();
            if (!text.startsWith("{") || !text.endsWith("}")) {
                text = LlmGeneratorConstant.END_FUNCTION;
            } else {
                Map<String, Object> dataMap = JsonUtil.toMap(text);
                text = (String) dataMap.get(LlmGeneratorConstant.ACTION_INPUT);
            }
        } else {
            text = responseDTO.getText();
        }
        //在第一个 function 后增加一个空格
        if (text.contains("function")) {
            text = text.replaceFirst("function", " function ");
        }
        return text;
    }

    /**
     * 预处理数据，主要处理日期时间字段
     */
    private List<Map<String, Object>> preprocessDataForDiagram(List<Map<String, Object>> data) {
        if (data == null || data.isEmpty()) {
            return data;
        }

        List<Map<String, Object>> processedData = new ArrayList<>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        for (Map<String, Object> row : data) {
            Map<String, Object> processedRow = new HashMap<>();

            for (Map.Entry<String, Object> entry : row.entrySet()) {
                String key = entry.getKey();
                Object value = entry.getValue();

                // 处理日期时间字段
                if (value instanceof Date) {
                    value = sdf.format((Date) value);
                }

                processedRow.put(key, value);
            }

            processedData.add(processedRow);
        }

        return processedData;
    }

    /**
     * 将MultipartFile转换为File
     */
    private List<File> convertMultipartFilesToFiles(List<MultipartFile> mfiles) {
        if (mfiles == null || mfiles.isEmpty()) {
            return null;
        }
        
        List<File> list = new ArrayList<>();
        try {
            for (MultipartFile mfile : mfiles) {
                // 获取原始文件名和扩展名
                String originalFilename = mfile.getOriginalFilename();
                String suffix = originalFilename != null && originalFilename.contains(".")
                    ? originalFilename.substring(originalFilename.lastIndexOf("."))
                    : ".tmp";
                
                // 创建临时文件
                File tempFile = File.createTempFile("upload_", suffix);
                
                // 将MultipartFile内容复制到临时文件
                Files.copy(mfile.getInputStream(), tempFile.toPath(), StandardCopyOption.REPLACE_EXISTING);
                
                log.debug("成功转换MultipartFile到临时文件: {}, 大小: {} bytes",
                    tempFile.getAbsolutePath(), tempFile.length());
                
                list.add(tempFile);
            }
            return list;
        } catch (IOException e) {
            log.error("转换MultipartFile到File失败: {}", e.getMessage(), e);
            throw new BussinessException("文件转换失败: " + e.getMessage(), e);
        }
    }
} 