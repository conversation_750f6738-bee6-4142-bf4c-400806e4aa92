package com.sinitek.sirm.nocode.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * LLM通用工具类
 * 
 * <p>提供LLM服务中必要的工具方法。
 * 重构后只保留真正通用的工具方法。</p>
 * 
 * <AUTHOR>
 * @since 2025/1/18
 */
@Slf4j
public class LlmCommonUtils {
    
    /**
     * 检查是否为有效的图像文件
     * 
     * @param fileName 文件名
     * @return 是否为有效图像文件
     */
    public static boolean isValidImageFile(String fileName) {
        if (StringUtils.isBlank(fileName)) {
            return false;
        }
        
        String lowerFileName = fileName.toLowerCase();
        String[] validExtensions = {".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp"};
        
        for (String ext : validExtensions) {
            if (lowerFileName.endsWith(ext)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 清理思考标签
     * 
     * @param text 原始文本
     * @return 清理后的文本
     */
    public static String cleanThinkTags(String text) {
        if (StringUtils.isBlank(text)) {
            return text;
        }
        return text.replaceAll("(?s)<think>.*?</think>", "");
    }
} 