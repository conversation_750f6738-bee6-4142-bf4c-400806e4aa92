package com.sinitek.sirm.nocode.service;

import com.sinitek.sirm.nocode.dto.LlmAgentCompletionsResponseDTO;
import com.sinitek.sirm.nocode.dto.LlmAgentGenFunctionDTO;
import com.sinitek.sirm.nocode.dto.LlmChatCompletionsResponseDTO;
import com.sinitek.sirm.nocode.dto.LlmChatGenDiagramDTO;
import com.sinitek.sirm.nocode.dto.LlmChatGenDiagramResponseDTO;
import java.util.List;
import org.springframework.web.multipart.MultipartFile;

/**
 * AI服务接口
 *
 * <p>提供AI相关的高级业务功能，包括智能体功能生成、图表生成、
 * 统计提示生成等服务。封装复杂的AI业务逻辑，提供简洁的API接口。</p>
 *
 * <AUTHOR>
 * @since 2024/12/26
 */
public interface IZdAiService {

    /**
     * 智能体生成脚本
     *
     * <p>根据用户提示词和可选的图片描述，通过智能体生成相应的脚本代码。
     * 支持上传图片进行图像识别，增强生成效果。</p>
     *
     * @param request 智能体生成请求，包含提示词、schema等信息
     * @param images 可选的图片文件列表，用于图像识别
     * @return 智能体生成响应，包含生成的脚本代码
     * @throws RuntimeException 当文件处理失败或智能体调用失败时
     */
    LlmChatCompletionsResponseDTO agentGenerateFunction(LlmAgentGenFunctionDTO request, List<MultipartFile> images);

    /**
     * 智能生成图表
     *
     * <p>根据用户需求和表单数据，生成相应的 Mermaid 格式图表。
     * 该方法会先根据表单代码生成 SQL 查询，执行查询获取数据，
     * 然后基于数据和用户需求生成合适的图表类型。</p>
     *
     * <p>支持的图表类型由 AI 根据数据特征和用户需求自动判断，
     * 包括但不限于：饼图、柱状图、折线图、流程图等。</p>
     *
     * @param request 图表生成请求，包含用户需求描述和表单代码
     * @return 包含 Mermaid 格式图表代码的响应对象
     * @throws RuntimeException 当 SQL 生成失败、数据查询失败或图表生成失败时抛出
     */
    LlmChatGenDiagramResponseDTO agentGenerateDiagram(LlmChatGenDiagramDTO request, List<MultipartFile> images);

    /**
     * 生成统计提示
     *
     * <p>根据表单配置分析用户可能想要统计的内容，
     * 生成自然语言描述的统计建议列表。</p>
     *
     * @param formCode 表单代码
     * @return 统计提示列表
     * @throws RuntimeException 当表单不存在或AI服务调用失败时
     */
    List<String> generateStatisticPrompt(String formCode);
} 