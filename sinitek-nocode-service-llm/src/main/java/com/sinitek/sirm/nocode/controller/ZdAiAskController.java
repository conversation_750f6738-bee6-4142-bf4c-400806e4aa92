package com.sinitek.sirm.nocode.controller;

import com.sinitek.sirm.framework.frontend.support.RequestResult;
import com.sinitek.sirm.nocode.dto.LImChatReplyResponseBaseDTO;
import com.sinitek.sirm.nocode.form.dto.ZdPageFormAiQuestionSettingDTO;
import com.sinitek.sirm.nocode.service.IZdAiAskService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 2025.0710
 * @since 1.0.0-SNAPSHOT
 */
@RestController
@Api(value = "/frontend/api/nocode/ai-ask", tags = "ai问答")
@RequestMapping("/frontend/api/nocode/ai-ask")
public class ZdAiAskController {


    @Resource
    private IZdAiAskService aiAskService;


    @ApiOperation(value = "提交问题，阻塞式", notes = "获取整个结果")
    @PostMapping("/blocking")
    public Mono<RequestResult<LImChatReplyResponseBaseDTO>> ask(
            @RequestBody ZdPageFormAiQuestionSettingDTO questionDTO
    ) {
        return aiAskService.ask(questionDTO);
    }

    @ApiOperation(value = "提交问题，流式", notes = "获取流式结果")
    @PostMapping(value = "/stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<LImChatReplyResponseBaseDTO> askAsync(
            @RequestBody ZdPageFormAiQuestionSettingDTO questionDTO
    ) {
        return aiAskService.askAsync(questionDTO);
    }


    @ApiOperation(value = "markdown转换助手，异步", notes = "异步获取markdown转换助手结果")
    @PostMapping(value = "/markdown-async", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<LImChatReplyResponseBaseDTO> markdownAsync(
            @RequestBody ZdPageFormAiQuestionSettingDTO questionDTO
    ) {
        return aiAskService.markdownAsync(questionDTO);
    }

    @ApiOperation(value = "markdown转换助手，同步", notes = "同步获取markdown转换助手结果")
    @PostMapping(value = "/markdown")
    public Mono<RequestResult<LImChatReplyResponseBaseDTO>> markdown(
            @RequestBody ZdPageFormAiQuestionSettingDTO questionDTO
    ) {
        return aiAskService.markdown(questionDTO);

    }
}
