package com.sinitek.sirm.nocode.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.File;
import java.util.List;
import java.util.Map;
import lombok.Data;

/**
 * 智能体请求DTO
 *
 * <AUTHOR>
 * @since 2024/4/23
 */
@Data
@ApiModel(description = "智能体请求DTO")
public class LlmAgentCompletionsRequestDTO {
    @ApiModelProperty("agentId")
    private String agentId;
    @ApiModelProperty("提示词")
    private String prompt;
    @ApiModelProperty("参数")
    private Map<String,  Object> params;
    @ApiModelProperty("会话id")
    private String sessionId;
    @ApiModelProperty("后端web服务信息")
    private String webInfo;
    @ApiModelProperty("枚举信息")
    private String enums;
    @ApiModelProperty("模型信息")
    private String models;
    @ApiModelProperty("图片内容描述")
    private String imageDesc;
    @ApiModelProperty("图片")
    private List<File> images;
}
