package com.sinitek.sirm.nocode.controller;

import com.sinitek.sirm.framework.frontend.support.RequestResult;
import com.sinitek.sirm.nocode.dto.LlmAgentCompletionsResponseDTO;
import com.sinitek.sirm.nocode.dto.LlmAgentGenFunctionDTO;
import com.sinitek.sirm.nocode.dto.LlmChatCompletionsResponseDTO;
import com.sinitek.sirm.nocode.dto.LlmChatGenDiagramDTO;
import com.sinitek.sirm.nocode.dto.LlmChatGenDiagramResponseDTO;
import com.sinitek.sirm.nocode.service.IZdAiService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * 大模型控制器
 *
 * <p>提供AI相关的Web接口，包括智能体脚本生成、图表生成、统计提示等功能。
 * 封装AI服务，对外提供RESTful API接口。</p>
 *
 * <AUTHOR>
 * @since 2024/4/24
 */
@RestController
@Api(value = "/frontend/api/nocode/llm", tags = "大模型服务")
@RequestMapping("/frontend/api/nocode/llm")
@Slf4j
public class ZdLlmController {

    @Autowired
    private IZdAiService zdAiService;
    
    /**
     * 智能体生成脚本
     *
     * <p>根据用户提示词和可选的图片，通过智能体生成相应的脚本代码。
     * 支持图片上传和识别，增强AI生成效果。</p>
     *
     * @param request 智能体生成请求，包含提示词、schema等信息
     * @param images 可选的图片文件列表，用于图像识别辅助生成
     * @return 包含生成脚本代码的响应结果
     */
    @ApiOperation(value = "智能体生成脚本")
    @PostMapping("/agent-generate-function")
    public RequestResult<LlmChatCompletionsResponseDTO> agentGenerateFunction (
        LlmAgentGenFunctionDTO request,
        @RequestParam(value = "images", required = false) List<MultipartFile> images) {
        return new RequestResult<>(zdAiService.agentGenerateFunction(request, images));
    }

    /**
     * 智能生成图表
     *
     * <p>根据用户需求和表单数据，自动生成对应的Mermaid格式图表。
     * 流程包括：SQL生成 → 数据查询 → 图表类型判断 → Mermaid代码生成。</p>
     *
     * @param request 图表生成请求，包含用户需求描述和表单代码
     * @return 包含Mermaid格式图表代码的响应结果
     */
    @ApiOperation(value = "智能生成图表", notes = "根据用户需求和表单数据生成 Mermaid 格式图表")
    @PostMapping("/agent-generate-diagram")
    public RequestResult<LlmChatGenDiagramResponseDTO> agentGenerateDiagram(
        LlmChatGenDiagramDTO request,
        @RequestParam(value = "images", required = false) List<MultipartFile> images) {
        return new RequestResult<>(zdAiService.agentGenerateDiagram(request, images));
    }

    /**
     * 生成统计提示
     *
     * <p>分析表单配置，智能推荐用户可能关心的统计维度，
     * 帮助用户快速发现数据价值。</p>
     *
     * @param formCode 表单代码
     * @return 统计建议列表
     */
    @ApiOperation(value = "生成统计提示", notes = "根据当前的表单分析用户可能要统计的内容")
    @PostMapping("/generate-statistic-prompt")
    public RequestResult<List<String>> generateStatisticPrompt(String formCode) {
        return new RequestResult<>(zdAiService.generateStatisticPrompt(formCode));
    }

}
