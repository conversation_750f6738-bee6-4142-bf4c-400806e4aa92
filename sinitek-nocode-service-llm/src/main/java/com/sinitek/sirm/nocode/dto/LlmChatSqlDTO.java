package com.sinitek.sirm.nocode.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.File;
import java.util.List;
import java.util.Map;
import lombok.Data;

/**
 * 智能体请求DTO
 *
 * <AUTHOR>
 * @since 2024/4/23
 */
@Data
@ApiModel(description = "智能体生成SQL请求DTO")
public class LlmChatSqlDTO {
    @ApiModelProperty("提示词")
    private String prompt;
    @ApiModelProperty("参数")
    private Map<String, Object> param;
    @ApiModelProperty("formCode")
    private String formCode;
    @ApiModelProperty("会话id")
    private String sessionId;
    @ApiModelProperty("图片")
    private List<File> images;
}
