package com.sinitek.sirm.nocode.support.ask.base;

import com.sinitek.sirm.framework.frontend.support.RequestResult;
import com.sinitek.sirm.nocode.dto.LImChatReplyResponseBaseDTO;
import com.sinitek.sirm.nocode.form.dto.ZdPageFormAiQuestionSettingDTO;
import com.sinitek.sirm.nocode.form.enumerate.AiMarkdownConversionTypeEnum;
import com.sinitek.sirm.nocode.form.enumerate.AiOutFormatEnum;
import com.sinitek.sirm.nocode.llm.config.AiModelConfig;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StopWatch;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.CorePublisher;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 大模型参数处理与响应转换接口
 * 提供统一的参数处理和响应转换方法，支持不同格式的输出转换
 *
 * <AUTHOR>
 * @version 2025.0708
 * @since 1.0.0-SNAPSHOT
 */
public interface ParamAndResponseHandler {
    Logger log = LoggerFactory.getLogger(ParamAndResponseHandler.class);

    /**
     * 处理请求参数
     * 将AI问题设置DTO转换为大模型需要的参数映射
     *
     * @param settingDTO   页面表单AI问题设置数据传输对象
     * @param modelAppInfo 模型应用信息
     * @return 参数映射表，包含模型所需的所有参数
     */
    Map<String, Object> handleParam(ZdPageFormAiQuestionSettingDTO settingDTO, AiModelConfig.ModelAppInfo modelAppInfo);

    /**
     * 处理HTTP响应
     * 将WebClient的响应转换为指定类型的响应对象
     *
     * @param settingDTO  页面表单AI问题设置数据传输对象
     * @param model       模型信息
     * @param headersSpec WebClient请求头规格
     * @param clazz       返回类型
     * @param <R>         响应类型
     * @param <T>         响应发布者类型
     * @return 转换后的响应对象
     */
    <R, T extends CorePublisher<R>> T handleResponse(ZdPageFormAiQuestionSettingDTO settingDTO, AiModelConfig.Model model, WebClient.RequestHeadersSpec<?> headersSpec, Class<T> clazz);


    @SuppressWarnings({"unchecked", "java:S4276"})
    default <R, T extends CorePublisher<R>> T handleResponse(ZdPageFormAiQuestionSettingDTO settingDTO, AiModelConfig.Model model, WebClient.RequestHeadersSpec<?> headersSpec, Class<T> clazz, Function<String, LImChatReplyResponseBaseDTO> mapper) {


        // 创建并启动性能计时器
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("大模型调用:" + model.getName());

        // 处理响应流
        Flux<LImChatReplyResponseBaseDTO> flux = headersSpec.
                retrieve()
                .bodyToFlux(String.class)
                .delayElements(Duration.ofMillis(10))
                .filter(a -> StringUtils.isNotBlank(a) && a.startsWith("{"))
                .map(mapper)
                .filter(a -> Objects.nonNull(a) && !Objects.isNull(a.getAnswer()))
                .doOnComplete(() -> watch(stopWatch));

        // 根据返回类型处理响应
        if (Objects.equals(clazz, Mono.class)) {
            return (T) fluxToMono(flux);
        } else {
            return (T) flux;
        }
    }


    /**
     * 记录性能计时
     * 在日志中记录大模型调用的耗时信息
     *
     * @param stopWatch 停止的计时器
     */
    static void watch(StopWatch stopWatch) {
        stopWatch.stop();
        log.info("{}{}耗时{}毫秒", stopWatch.prettyPrint(), System.lineSeparator(), stopWatch.getTotalTimeMillis());
    }


    /**
     * 是否支持该模型应用
     *
     * @param modelAppInfo 模型应用信息
     * @return true:支持;false:不支持
     */
    boolean support(AiModelConfig.ModelAppInfo modelAppInfo);


    /**
     * 获取提示词
     * 根据设置生成大模型提示词
     *
     * @param settingDTO 页面表单AI问题设置数据传输对象
     * @return 生成的提示词字符串
     */
    static String prompt(ZdPageFormAiQuestionSettingDTO settingDTO) {
        String inputContent = settingDTO.getInputContent();
        // 输出格式
        AiOutFormatEnum outFormat = settingDTO.getOutFormat();

        // 根据输出格式添加不同的提示词
        if (Objects.equals(outFormat, AiOutFormatEnum.MARKDOWN)) {
            inputContent += "\n\n请用markdown格式回答上面问题。";
            List<Integer> markdownConversionType = settingDTO.getMarkdownConversionType();
            if (CollectionUtils.isEmpty(markdownConversionType)) {
                // 添加markdown转换规则
                String rules = "\n\n##补充规则:\n" + markdownConversionType.stream()
                        .map(AiMarkdownConversionTypeEnum::fromValue)
                        .filter(Objects::nonNull)
                        .map(AiMarkdownConversionTypeEnum::getRule)
                        .collect(Collectors.joining(System.lineSeparator()));
                inputContent += rules;
            }
        } else if (Objects.equals(outFormat, AiOutFormatEnum.TEXT)) {
            inputContent += "\n\n请用纯文本格式回答上面问题。";
        } else if (Objects.equals(outFormat, AiOutFormatEnum.PIC)) {
            inputContent += "\n\n请用图片格式输出回答内容。";
        }
        return inputContent;
    }

    /**
     * 将Flux流转换为字符串
     * 该方法会将Flux中的所有字符串元素连接成一个完整的字符串，并移除其中的<think>标签内容
     *
     * @param flux 包含字符串元素的Flux流
     * @return 处理后的完整字符串，不包含<think>标签内容
     */
    static LImChatReplyResponseBaseDTO fluxToString(Flux<LImChatReplyResponseBaseDTO> flux) {
        LImChatReplyResponseBaseDTO response = new LImChatReplyResponseBaseDTO();
        String answer = flux.toStream()
                .map(a -> {
                    response.setConversationId(a.getConversationId());
                    return a.getAnswer();
                })
                .collect(Collectors.joining());
        response.setAnswer(answer.replaceAll("(?s)<think>.*?</think>", ""));
        return response;

    }

    /**
     * 将Flux流转换为RequestResult对象
     * 该方法将Flux流转换为RequestResult对象，并确保在转换过程中使用boundedElastic调度器
     *
     * @param flux 待转换的Flux流
     * @return 转换后的RequestResult对象
     */
    static RequestResult<LImChatReplyResponseBaseDTO> fluxToRequestResult(Flux<LImChatReplyResponseBaseDTO> flux) {
        return new RequestResult<>(fluxToString(flux));
    }

    /**
     * 将Flux流转换为Mono对象
     * 该方法将Flux流转换为Mono对象，并确保在转换过程中使用boundedElastic调度器
     *
     * @param flux 待转换的Flux流
     * @return 转换后的Mono对象
     */
    static Mono<RequestResult<LImChatReplyResponseBaseDTO>> fluxToMono(Flux<LImChatReplyResponseBaseDTO> flux) {
        return Mono.just(fluxToRequestResult(flux))
                .subscribeOn(Schedulers.boundedElastic());
    }


}