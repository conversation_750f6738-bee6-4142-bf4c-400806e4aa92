package com.sinitek.sirm.nocode.support.ask;

import com.sinitek.sirm.common.utils.JsonUtil;
import com.sinitek.sirm.nocode.common.dto.ZdKeyAndValueDTO;
import com.sinitek.sirm.nocode.dto.LImChatReplyResponseBaseDTO;
import com.sinitek.sirm.nocode.form.dto.ZdPageFormAiQuestionSettingDTO;
import com.sinitek.sirm.nocode.llm.config.AiModelConfig;
import com.sinitek.sirm.nocode.llm.enumerate.AiSourceEnum;
import com.sinitek.sirm.nocode.support.ask.base.ParamAndResponseHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.CorePublisher;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * dify 实现模式
 *
 * <AUTHOR>
 * @version 2025.0708
 * @since 1.0.0-SNAPSHOT
 */
@Slf4j
@Component
public class DifyParamAndResponseHandler implements ParamAndResponseHandler {
    @Override
    public Map<String, Object> handleParam(ZdPageFormAiQuestionSettingDTO settingDTO, AiModelConfig.ModelAppInfo modelAppInfo) {
        Map<String, Object> paramMap = new HashMap<>();
        // 先固定为流式
        paramMap.put("response_mode", "streaming");
        // 用户
        paramMap.put("user", "sinitek");
        HashMap<Object, Object> inputs = new HashMap<>();
        List<ZdKeyAndValueDTO> inputVar = settingDTO.getInputVar();
        if (CollectionUtils.isNotEmpty(inputVar)) {
            inputVar.forEach(zdKeyAndValueDTO -> inputs.put(zdKeyAndValueDTO.getKey(), zdKeyAndValueDTO.getValue()));
        }
        // 变量
        paramMap.put("inputs", inputs);
        // 聊天内容
        paramMap.put("query", settingDTO.getInputContent());
        return paramMap;


    }

    @Override
    public <R, T extends CorePublisher<R>> T handleResponse(ZdPageFormAiQuestionSettingDTO settingDTO, AiModelConfig.Model model, WebClient.RequestHeadersSpec<?> headersSpec, Class<T> clazz) {
        return handleResponse(settingDTO, model, headersSpec, clazz, str -> JsonUtil.toJavaObject(str, LImChatReplyResponseBaseDTO.class));
    }


    /**
     * sk-7f168e1cbf884b73be3981c5da293242
     * 是否支持
     *
     * @param modelAppInfo 模型应用信息
     * @return true 支持
     */
    @Override
    public boolean support(AiModelConfig.ModelAppInfo modelAppInfo) {
        AiModelConfig.Model model = modelAppInfo.getModel();
        return AiSourceEnum.DIFY.getValue().equals(model.getKey());
    }
}
