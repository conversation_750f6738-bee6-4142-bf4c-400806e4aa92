package com.sinitek.sirm.nocode.service;

import com.sinitek.sirm.framework.frontend.support.RequestResult;
import com.sinitek.sirm.nocode.dto.LImChatReplyResponseBaseDTO;
import com.sinitek.sirm.nocode.form.dto.ZdPageFormAiQuestionSettingDTO;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @version 2025.0703
 * @since 1.0.0-SNAPSHOT
 */

public interface IZdAiAskService {
    /**
     * 智能问答
     *
     * @param request 请求
     * @return 响应
     */
    Mono<RequestResult<LImChatReplyResponseBaseDTO>> ask(ZdPageFormAiQuestionSettingDTO request);

    /**
     * 智能问答
     *
     * @param request 请求
     * @return 响应
     */
    Flux<LImChatReplyResponseBaseDTO> askAsync(ZdPageFormAiQuestionSettingDTO request);


    Flux<LImChatReplyResponseBaseDTO> markdownAsync(ZdPageFormAiQuestionSettingDTO request);

    Mono<RequestResult<LImChatReplyResponseBaseDTO>> markdown(ZdPageFormAiQuestionSettingDTO request);


}
