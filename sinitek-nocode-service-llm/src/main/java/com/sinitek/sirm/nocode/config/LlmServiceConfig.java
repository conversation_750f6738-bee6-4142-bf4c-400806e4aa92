package com.sinitek.sirm.nocode.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * LLM服务配置类
 *
 * <p>统一管理所有LLM服务的配置信息，包括服务器地址、API密钥等。
 * 避免在各个服务实现类中重复配置相同的属性。</p>
 *
 * <AUTHOR>
 * @since 2025/1/18
 */
@Data
@Component
@ConfigurationProperties(prefix = "llm")
public class LlmServiceConfig {

    /**
     * 快速模型配置
     */
    private ModelConfig sinitekChatFast = new ModelConfig();

    /**
     * 标准模型配置
     */
    private ModelConfig sinitekChat = new ModelConfig();

    /**
     * 图像处理模型配置
     */
    private ModelConfig sinitekImage = new ModelConfig();

    /**
     * Schema生成代理配置
     */
    private ModelConfig schemaGenAgent = new ModelConfig();

    /**
     * Schema生成聊天配置
     */
    private ModelConfig schemaGenChat = new ModelConfig();

    /**
     * 教程生成代理配置
     */
    private ModelConfig manualAgent = new ModelConfig();

    /**
     * SQL生成服务配置
     */
    private ModelConfig sqlGenChat = new ModelConfig();

    /**
     * 图表生成服务配置
     */
    private ModelConfig diagramGenChat = new ModelConfig();

    /**
     * Markdown生成服务配置
     */
    private ModelConfig markdownGenChat;

    /**
     * 单个模型配置
     */
    @Data
    public static class ModelConfig {
        /**
         * 服务器地址
         */
        private String server;

        /**
         * API密钥
         */
        private String apiKey;

        /**
         * 检查配置是否有效
         */
        public boolean isValid() {
            return server != null && !server.trim().isEmpty()
                    && apiKey != null && !apiKey.trim().isEmpty();
        }
    }
} 