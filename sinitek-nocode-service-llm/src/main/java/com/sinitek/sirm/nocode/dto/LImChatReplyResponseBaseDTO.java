package com.sinitek.sirm.nocode.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.deser.std.StringDeserializer;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 2025.0723
 * @since 1.0.0-SNAPSHOT
 */
@Data
public class LImChatReplyResponseBaseDTO {
    // 反序列化的时候，由于 TrimJacksonConverter 的影响，content 会被 trim，导致 单纯的换行符会被置为空字符串
    @JsonDeserialize(using = StringDeserializer.class)
    private String answer;
    // 在进行序列化的时候，会对特殊字符进行转义，例如换行符 会转换为 字符串"\n" 这个在文本中没有效果，不转换的话，会破坏json的结构。所以返回文本最合适了
    @JsonProperty(value = "conversation_id")
    private String conversationId;
}
