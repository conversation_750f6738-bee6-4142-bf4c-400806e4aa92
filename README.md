# sinitek-nocode-backend

## 项目介绍

携宁智搭系统，致力于为企业提供企业级零码平台，让企业快速搭建企业级应用。

## 后端项目信息

[http://192.168.21.121:61034/sinitek-nocode-backend](http://192.168.21.121:61034/sinitek-nocode-backend)

## 开发环境jenkins信息

[智搭系统jenkins](http://192.168.21.127:8080/view/%E7%A0%94%E5%8F%91%E9%83%A8/job/%E6%99%BA%E6%90%AD%E7%B3%BB%E7%BB%9F-%E6%B5%8B%E8%AF%95%E7%8E%AF%E5%A2%83/)

## 开发环境rancher信息

[nocode-backend-test pod](https://192.168.23.123/dashboard/c/c-srjjs/explorer/apps.deployment/sirmapp/nocode-backend-test#pods)

### 后端deployment名称

```txt
nocode-backend-test
```

swagger地址

[http://192.168.21.121:61034/sinitek-nocode-backend/doc.html#/home](http://192.168.21.121:61034/sinitek-nocode-backend/doc.html#/home)

swagger用户名密码: admin/sinitek-swagger

### 后端使用数据库

```txt
数据库名: lowcode_saas_10_dev
jdbc地址: *********************************************************************************************************************************************
```

### 使用框架

* spring cloud
* 数据库：PostgreSQL数据库
* orm框架：mybatis
* orm增强框架：mybatis-plus
* 注册和配置中心：nacos
* 缓存：redis

### git提交规范

|  前缀  |                    备注                     |
| :----: | :-----------------------------------------: |
|   rq   | 表示“需求”。描述格式：需求名称 - 需求编号。 |
|  bug   |  表示“BUG”。描述格式：BUG编号 - BUG描述。   |
|   ft   |             表示“特性”。慎用。              |
|  doc   |              表示“文档修改”。               |
|  spec  |             表示“因为规范调整。             |
|  pub   |                 发布版本。                  |
|  pom   |                 pom文件调整                 |
| gitlab |      gitlab文件调整，比如.gitlab-cicd       |

#### sql文件

- 新增表或者是修改sql 的话，到模块 sinitek-nocode-dal 下 resource 下的 db.postgresql 目录下，新增sql 文件。规则参照里面有的sql
  例如：V100_20250605_01__create.sql 其中 V100 固定 中间写日期，后面的 _create 是描述。
- 按照这样的格式，flyway 会正常解析。v 后面的100 是版本前置号，这个固定。中间的是日期。后面的是描述。
- sql 最好写成 if not exists 要求。例如 create table if not exists ,alter table zd_page add if not exists publish_type
  smallint 等。
- 不允许有drop 表语句。

