-- 导出任务表
CREATE TABLE IF NOT EXISTS zd_export_task (
    id bigint PRIMARY KEY,
    version INTEGER NOT NULL,
    createtimestamp TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updatetimestamp TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    file_name VARCHAR(300) NOT NULL,
    status SMALLINT NOT NULL,
    operator_id VARCHAR(30) NOT NULL,
    operate_time TIMESTAMP NOT NULL,
    export_params JSONB,
    error_msg JSONB,
    file_count INTEGER,
    form_code VARCHAR(50)
);

-- 导出任务表注释
COMMENT ON TABLE zd_export_task IS '导出任务表';
COMMENT ON COLUMN zd_export_task.id IS '主键';
COMMENT ON COLUMN zd_export_task.version IS '乐观锁';
COMMENT ON COLUMN zd_export_task.createtimestamp IS '创建时间';
COMMENT ON COLUMN zd_export_task.updatetimestamp IS '更新时间';
COMMENT ON COLUMN zd_export_task.file_name IS '文件名';
COMMENT ON COLUMN zd_export_task.status IS '状态：0-待处理，1-处理中，2-处理成功，3-处理失败';
COMMENT ON COLUMN zd_export_task.operator_id IS '操作人id';
COMMENT ON COLUMN zd_export_task.operate_time IS '操作时间';
COMMENT ON COLUMN zd_export_task.export_params IS '导出参数';
COMMENT ON COLUMN zd_export_task.error_msg IS '错误信息';
COMMENT ON COLUMN zd_export_task.file_count IS '文件个数';
COMMENT ON COLUMN zd_export_task.form_code IS '表单编码';

-- 导出任务表索引
CREATE INDEX IF NOT EXISTS idx_zd_export_task_form_code ON zd_export_task(form_code);