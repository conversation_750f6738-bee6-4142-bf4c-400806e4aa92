-- 应用ai设置表
create table if not exists zd_app_ai_setting
(
    id        bigint       not null
        primary key,
    app_code  varchar(50)  not null,
    ai_source varchar(50)  not null,
    type_key  varchar(50)  not null,
    app_name  varchar(50)  not null,
    api_key   varchar(100) not null

);

-- 应用ai设置表
comment on table zd_app_ai_setting is '页面收藏表';
comment on column zd_app_ai_setting.id is '主键';
comment on column zd_app_ai_setting.ai_source is 'ai的来源';
comment on column zd_app_ai_setting.type_key is '模型类型key';
comment on column zd_app_ai_setting.app_name is '模型应用名称';
comment on column zd_app_ai_setting.api_key is '应用密钥';