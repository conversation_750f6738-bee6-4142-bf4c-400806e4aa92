create table if not exists zd_page_scene_task
(
    page_code   varchar(50) not null,
    recipient   jsonb       not null,
    note_type   jsonb,
    finish_date date        not null,
    PRIMARY KEY (page_code, finish_date)
) PARTITION BY RANGE (finish_date);

comment on table zd_page_scene_task is '场景收集表任务表';

comment on column zd_page_scene_task.page_code is '页面编码';
comment on column zd_page_scene_task.recipient is '接收人';
comment on column zd_page_scene_task.note_type is '通知方式';
comment on column zd_page_scene_task.finish_date is '完成日期';

CREATE TABLE if not exists zd_page_scene_task_2025 PARTITION OF zd_page_scene_task
    FOR VALUES FROM ('2025-01-01') TO ('2026-01-01');
CREATE TABLE if not exists zd_page_scene_task_2026 PARTITION OF zd_page_scene_task
    FOR VALUES FROM ('2026-01-01') TO ('2027-01-01');
CREATE TABLE if not exists zd_page_scene_task_2027 PARTITION OF zd_page_scene_task
    FOR VALUES FROM ('2027-01-01') TO ('2028-01-01');
CREATE TABLE if not exists zd_page_scene_task_2028 PARTITION OF zd_page_scene_task
    FOR VALUES FROM ('2028-01-01') TO ('2029-01-01');
CREATE TABLE if not exists zd_page_scene_task_2029 PARTITION OF zd_page_scene_task
    FOR VALUES FROM ('2029-01-01') TO ('2030-01-01');