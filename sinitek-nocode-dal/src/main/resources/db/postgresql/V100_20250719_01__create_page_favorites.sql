-- 页面收藏表
create table if not exists zd_page_favorites
(
    id              bigint      not null
        primary key,
    page_code       varchar(50) not null,
    org_id          varchar(30) not null,
    version         integer     not null,
    createtimestamp timestamp   not null default current_timestamp,
    updatetimestamp timestamp   not null default current_timestamp
);

-- 页面收藏表注释
comment on table zd_page_favorites is '页面收藏表';
comment on column zd_page_favorites.id is '主键';
comment on column zd_page_favorites.version is '乐观锁';
comment on column zd_page_favorites.createtimestamp is '创建时间';
comment on column zd_page_favorites.updatetimestamp is '更新时间';

comment on column zd_page_favorites.page_code is '页面编码';
comment on column zd_page_favorites.org_id is '收藏人员id';


-- 索引
create index if not exists idx_zd_page_favorites_page_code on zd_page_favorites (page_code, org_id);