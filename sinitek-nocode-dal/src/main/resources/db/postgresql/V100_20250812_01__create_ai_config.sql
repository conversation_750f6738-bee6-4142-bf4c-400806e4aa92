-- 导出任务表
CREATE TABLE IF NOT EXISTS zd_ai_workflow_config (
    id bigint PRIMARY KEY,
    version INTEGER NOT NULL,
    createtimestamp TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updatetimestamp TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    event_type VARCHAR(300) NOT NULL,
    app_id VARCHAR(300) NOT NULL,
    operator_id VARCHAR(30) NOT NULL,
    operate_time TIMESTAMP NOT NULL,
    form_code VARCHAR(50)
);

-- 导出任务表注释
COMMENT ON TABLE zd_ai_workflow_config IS '导出任务表';
COMMENT ON COLUMN zd_ai_workflow_config.id IS '主键';
COMMENT ON COLUMN zd_ai_workflow_config.version IS '乐观锁';
COMMENT ON COLUMN zd_ai_workflow_config.createtimestamp IS '创建时间';
COMMENT ON COLUMN zd_ai_workflow_config.updatetimestamp IS '更新时间';
COMMENT ON COLUMN zd_ai_workflow_config.event_type IS '事件类型';
COMMENT ON COLUMN zd_ai_workflow_config.app_id IS '工作流应用id';
COMMENT ON COLUMN zd_ai_workflow_config.operator_id IS '操作人id';
COMMENT ON COLUMN zd_ai_workflow_config.operate_time IS '操作时间';
COMMENT ON COLUMN zd_ai_workflow_config.form_code IS '表单编码';

-- 导出任务表索引
CREATE INDEX IF NOT EXISTS idx_zd_ai_workflow_config_form_code ON zd_ai_workflow_config(form_code, event_type);