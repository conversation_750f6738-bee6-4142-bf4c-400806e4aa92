create table if not exists sirm_wx_work_account
(
    id              bigint       not null
        primary key,
    org_id          varchar(30)  not null,
    wxwork_id       varchar(30)  not null,
    tenant_id       varchar(60),
    version         integer      not null,
    createtimestamp timestamp(0) not null,
    updatetimestamp timestamp(0) not null
);
comment on table sirm_wx_work_account is '企业微信映射关系表';
comment on column sirm_wx_work_account.org_id is '员工id';
comment on column sirm_wx_work_account.wxwork_id is '员工企业微信账号';
comment on column sirm_wx_work_account.tenant_id is '多租户id';
comment on column sirm_wx_work_account.version is '乐观锁';
comment on column sirm_wx_work_account.createtimestamp is '创建时间';
comment on column sirm_wx_work_account.updatetimestamp is '更新时间';

