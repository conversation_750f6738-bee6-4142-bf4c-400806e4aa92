package com.sinitek.sirm.nocode.form.po;

import java.util.Date;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 导出任务查询结果PO
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "导出任务查询结果PO")
public class ZdExportTaskSearchResultPO {

    @ApiModelProperty("任务ID")
    private Long id;

    @ApiModelProperty("文件名")
    private String fileName;

    @ApiModelProperty("文件个数")
    private Integer fileCount;

    @ApiModelProperty("状态：0-待处理，1-处理中，2-处理成功，3-处理失败")
    private Integer status;

    @ApiModelProperty("操作人id")
    private String operatorId;

    @ApiModelProperty("操作时间")
    private Date operateTime;

    @ApiModelProperty("表单编码")
    private String formCode;
}
