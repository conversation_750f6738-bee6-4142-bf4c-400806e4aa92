<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinitek.sirm.nocode.page.mapper.ZdPageMapper">


    <resultMap id="squareSearchMap" type="com.sinitek.sirm.nocode.page.po.ZdPageSquarePO">
        <id property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="code" column="code"/>
        <result property="favoritesFlag" column="favoritesFlag"/>
        <result property="pageType" column="page_type"/>
        <result property="publishTime" column="publishTime"/>
        <result property="appCode" column="appCode"/>
        <result property="appName" column="appName"/>
        <result property="iconfont" column="iconfont"/>
        <result property="themeColor" column="theme_color"/>
        <!-- 映射集合属性 -->
        <collection property="creatorIds" ofType="java.lang.String" select="queryOrgId" column="appCode"/>
    </resultMap>


    <select id="squareSearch" resultMap="squareSearchMap">
        select
        p.id,
        p.name,
        p.code,
        p.page_type,
        p.updatetimestamp as publishTime,
        a.name as appName,
        a.code as appCode,
        a.iconfont,
        a.theme_color,
        <choose>
            <when test="params.favoritesFlag != null">
                <choose>
                    <when test="params.favoritesFlag">
                        true as favoritesFlag
                    </when>
                    <otherwise>
                        false as favoritesFlag
                    </otherwise>
                </choose>
            </when>
            <otherwise>
                EXISTS(select 1 from zd_page_favorites where page_code = p.code and org_id = #{params.currentOrgId}) as
                favoritesFlag
            </otherwise>
        </choose>
        from zd_page p
        inner join
        zd_app a on p.app_code = a.code
        <where>
            p.publish_type = 0
            <if test="params.type != null">
                and p.page_type = #{params.type}
            </if>
            <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(params.name)">
                <bind name="name_like" value="@com.sinitek.sirm.common.utils.SQLUtils@like(params.name)"/>
                and upper(p.name) like upper(#{name_like}) escape '/'
            </if>
            and exists( select 1 from zd_page_form where page_code = p.code and publish_status =2)
            <if test="params.favoritesFlag != null and params.favoritesFlag">
                <choose>
                    <when test="params.favoritesFlag">
                        and exists( select 1 from zd_page_favorites where page_code = p.code and org_id =
                        #{params.currentOrgId})
                    </when>
                    <otherwise>
                        and not exists( select 1 from zd_page_favorites where page_code = p.code and org_id =
                        #{params.currentOrgId})
                    </otherwise>
                </choose>
            </if>
        </where>
        order by p.updatetimestamp desc
    </select>

    <select id="queryOrgId" resultType="java.lang.String">
        select org_id
        from zd_app_manager
        where app_code = #{appCode}
    </select>

    <select id="listTree" resultType="com.sinitek.sirm.nocode.page.entity.ZdPage">
        select
        p.id,
        p.name,
        p.code,
        p.parent_id,
        <if test="hasPublishForm != null and hasPublishForm">
            (select 1 from zd_page_form where page_code = p.code and publish_status = 2) as formPublishFlag,
        </if>
        p.page_type
        from zd_page p
        <where>
            p.app_code = #{appCode}
        </where>
        order by p.sort asc, p.createTimeStamp desc
    </select>
</mapper>
