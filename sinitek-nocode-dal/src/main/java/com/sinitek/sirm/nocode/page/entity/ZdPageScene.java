package com.sinitek.sirm.nocode.page.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.sinitek.sirm.nocode.common.base.IdBaseEntity;
import com.sinitek.sirm.nocode.common.enumerate.YesOrNoEnum;
import com.sinitek.sirm.nocode.page.enumerate.SceneRepeatTypeEnum;
import com.sinitek.sirm.nocode.page.enumerate.SceneSubmitEnum;
import com.sinitek.sirm.nocode.page.enumerate.SceneTypeEnum;
import com.sinitek.sirm.nocode.support.mybatis.handler.ListIntegerTypeHandler;
import com.sinitek.sirm.nocode.support.mybatis.handler.ListStringTypeHandler;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;

/**
 * 页面场景设置
 *
 * @TableName zd_page_scene
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value = "zd_page_scene", autoResultMap = true)
@Data
@ApiModel(description = "场景实体")
public class ZdPageScene extends IdBaseEntity {
    /**
     * 页面编码
     */
    @ApiModelProperty(value = "页面编码", example = "page_114b522908e04cd09deeea7f4c25ca25", required = true)
    private String pageCode;

    /**
     * 场景类型
     */

    private SceneTypeEnum sceneType;

    /**
     * 提交类型，0：无限制，1：每个人限制一次
     */

    private SceneSubmitEnum submitType;

    /**
     * 重复类型，0：每天，1：每周，2：每月
     */
    private SceneRepeatTypeEnum repeatType;

    /**
     * 每天开始时间
     */
    @JsonFormat(pattern = "HH:mm")
    @ApiModelProperty(value = "每天开始时间", example = "09:40")
    private LocalTime startTime;

    /**
     * 是否跳过法定节假日
     */
    @ApiModelProperty(value = "是否跳过节假日,0:不跳过，1：跳过,数值类型", example = "0")
    private YesOrNoEnum holidayStrategy;

    /**
     * 截至时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "表单收集截至时间", example = "2025-03-25")
    private LocalDate endDate;

    /**
     * 报告发送人，多个用逗号隔开
     */
    @TableField(typeHandler = ListStringTypeHandler.class)
    @ApiModelProperty(value = "报告发送人，存储人员id,是个人员id数组", example = "[121212,11111]")
    private List<String> reportSendOrgIds;

    @TableField(typeHandler = ListIntegerTypeHandler.class)
    private List<Integer> noteType;

    @ApiModelProperty("下一个任务执行日期")
    private LocalDate nextExecuteDate;

    @ApiModelProperty("完成日期")
    private LocalDate finishDate;

    @ApiModelProperty(value = "每月几号重复，当重复类型为每月时，需要指定具体日期", example = "1")
    private Integer monthDay;

    @ApiModelProperty(value = "每周几重复，当重复类型为每周时，需要指定具体日期", example = "1")
    private Integer weekDay;
}
