package com.sinitek.sirm.nocode.form.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sinitek.sirm.nocode.form.entity.ZdExportTask;
import com.sinitek.sirm.nocode.form.po.ZdExportTaskSearchParamPO;
import com.sinitek.sirm.nocode.form.po.ZdExportTaskSearchResultPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 导出任务Mapper
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Mapper
public interface ZdExportTaskMapper extends BaseMapper<ZdExportTask> {

    /**
     * 分页查询导出任务列表
     *
     * @param page 分页参数
     * @param param 查询参数
     * @return 查询结果
     */
    IPage<ZdExportTaskSearchResultPO> search(Page<ZdExportTaskSearchResultPO> page,
        @Param("param") ZdExportTaskSearchParamPO param);
} 