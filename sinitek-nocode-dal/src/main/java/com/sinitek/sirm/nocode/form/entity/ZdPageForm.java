package com.sinitek.sirm.nocode.form.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.sinitek.data.model.version.entity.BaseVersionEntity;
import com.sinitek.sirm.nocode.support.mybatis.CustomBaseAudit;
import com.sinitek.sirm.nocode.support.mybatis.handler.JsonbTypeHandler;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * 页面表单表
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value = "zd_page_form", autoResultMap = true)
@Data
@ApiModel(description = "表单实体")
public class ZdPageForm extends BaseVersionEntity implements CustomBaseAudit {


    /**
     * 页面编码
     */
    @ApiModelProperty(value = "页面编码", example = "a10000")
    private String pageCode;

    /**
     * 表单编码
     */
    @NotBlank(message = "表单编码不能为空")
    @ApiModelProperty(value = "表单编码", example = "as256")
    private String code;

    /**
     * 表单配置
     */
    @ApiModelProperty(value = "表单配置,base64格式", example = "\"{\"list\":[{\"type\":\"text\",\"name\":\"文本\",\"icon\":\"icon-text\",\"hiddenName\":false,\"hiddenTitle\":false,\"options\":{\"defaultValue\":\"这是一段文本\",\"format\":{\"type\":\"custom\",\"formatter\":\"return data\"},\"hide\":false,\"enableWidthConfig\":false,\"enableHeightConfig\":false,\"showContentTooltip\":false,\"contentTooltip\":{\"placement\":\"top\",\"effect\":\"light\"},\"height\":\"\",\"width\":\"100%\",\"showTooltip\":false,\"showLabelTooltip\":false,\"dataBind\":true,\"showEllipsis\":false,\"showFullscreenIcon\":false,\"fullscreen\":false,\"innerTop\":\"\",\"labelIcon\":\"\",\"remoteFunc\":\"func_1709090684000_38537\",\"auth\":false},\"key\":\"1709090684000_38537\",\"model\":\"text_1709090684000_38537\",\"titleWidth\":100}],\"config\":{\"width\":\"\",\"labelWidth\":100,\"showEllipsis\":false,\"labelPosition\":\"right\",\"size\":\"small\",\"cssClass\":\"blank-container\",\"labelSuffix\":false,\"isInit\":false,\"initEvent\":\"\",\"initDemo\":{},\"model\":\"\",\"currModelType\":\"\",\"isShowValidateFailMsg\":false,\"styleSheet\":\"\",\"isDialog\":true,\"buttons\":[{\"type\":\"button\",\"name\":\"按钮\",\"icon\":\"icon-button\",\"titleWidth\":10,\"options\":{\"defaultValue\":\"保存\",\"type\":\"primary\",\"eventCode\":\"save\",\"toCode\":\"\",\"tableKey\":\"\",\"echartKey\":\"\",\"dialog\":true,\"treeKey\":\"\",\"componentKey\":\"\",\"hide\":false,\"auth\":false,\"syscode\":\"\",\"isExecuteCallbackOnDialogCancel\":true},\"key\":\"1709090268000_41184\",\"model\":\"button_1709090268000_41184\"},{\"type\":\"button\",\"name\":\"按钮\",\"icon\":\"icon-button\",\"titleWidth\":10,\"options\":{\"defaultValue\":\"取消\",\"type\":\"info\",\"eventCode\":\"cancel\",\"toCode\":\"\",\"tableKey\":\"\",\"echartKey\":\"\",\"dialog\":true,\"treeKey\":\"\",\"componentKey\":\"\",\"hide\":false,\"auth\":false,\"height\":\"\",\"syscode\":\"\",\"isExecuteCallbackOnDialogCancel\":true},\"key\":\"1709090268000_96935\",\"model\":\"button_1709090268000_96935\"}],\"events\":[],\"title\":\"供应商表单\",\"sourceId\":\"id\",\"afterFunc\":\"\",\"loadParam\":{},\"isCachePageScroll\":true,\"isDialogNoButton\":false},\"decode\":true}\"")
    @TableField(typeHandler = JsonbTypeHandler.class)
    private String pageData;

    @JsonIgnore
    @ApiModelProperty(value = "更新人ID")
    @TableField(
            value = "updater_id",
            fill = FieldFill.INSERT_UPDATE
    )
    private String updaterId;

    @Override
    public void setCreatorId(String creatorId) {

    }
}
