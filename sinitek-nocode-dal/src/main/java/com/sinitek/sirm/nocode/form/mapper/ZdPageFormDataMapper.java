package com.sinitek.sirm.nocode.form.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.Mapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.sinitek.sirm.nocode.form.constant.FormConstant;
import com.sinitek.sirm.nocode.form.entity.ZdPageFormData;
import org.apache.ibatis.annotations.Param;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;

public interface ZdPageFormDataMapper extends Mapper<ZdPageFormData> {
    /**
     * 插入
     *
     * @param entity 实体
     * @return 影响行数
     */
    int insert(ZdPageFormData entity);

    /**
     * 根据id 删除数据
     *
     * @param entity 实体
     * @return 影响行数
     */
    int deleteById(ZdPageFormData entity);

    /**
     * 批量删除数据
     *
     * @param idList    idList
     * @param tableName 表明
     * @return 影响行数
     */
    int deleteBatchIds(@Param(Constants.COLLECTION) Collection<? extends Serializable> idList, @Param(FormConstant.TABLE_NAME) String tableName);

    /**
     * 条件删除
     *
     * @param queryWrapper 查询对象
     * @param tableName    表名称
     * @return 影响行数
     */
    int delete(@Param(Constants.WRAPPER) Wrapper<ZdPageFormData> queryWrapper, @Param(FormConstant.TABLE_NAME) String tableName);


    /**
     * 根据 ID 修改
     *
     * @param entity    实体对象
     * @param tableName 表名称
     */
    int updateById(@Param(Constants.ENTITY) ZdPageFormData entity, @Param(FormConstant.TABLE_NAME) String tableName);


    /**
     * 根据 whereEntity 条件，更新记录
     *
     * @param entity        实体对象 (set 条件值,可以为 null)
     * @param updateWrapper 实体对象封装操作类（可以为 null,里面的 entity 用于生成 where 语句）
     */
    int update(@Param(Constants.ENTITY) ZdPageFormData entity, @Param(Constants.WRAPPER) Wrapper<ZdPageFormData> updateWrapper, @Param(FormConstant.TABLE_NAME) String tableName);


    /**
     * 查询（根据ID 批量查询）
     *
     * @param idList    主键ID列表(不能为 null 以及 empty)
     * @param tableName 表名称
     */
    List<ZdPageFormData> selectBatchIds(@Param(Constants.COLLECTION) Collection<? extends Serializable> idList, @Param(FormConstant.TABLE_NAME) String tableName);


    /**
     * 根据 entity 条件，查询一条记录
     *
     * @param queryWrapper 实体对象封装操作类（可以为 null）
     * @param tableName    表名称
     */
    ZdPageFormData selectOne(@Param(Constants.WRAPPER) Wrapper<ZdPageFormData> queryWrapper, @Param(FormConstant.TABLE_NAME) String tableName);

    /**
     * 根据 Wrapper 条件，查询总记录数
     *
     * @param queryWrapper 实体对象封装操作类（可以为 null）
     * @param tableName    表名称
     */
    Integer selectCount(@Param(Constants.WRAPPER) Wrapper<ZdPageFormData> queryWrapper, @Param(FormConstant.TABLE_NAME) String tableName);


    /**
     * 根据 entity 条件，查询全部记录
     *
     * @param queryWrapper 实体对象封装操作类（可以为 null）
     * @param tableName    表名称
     */
    List<ZdPageFormData> selectList(@Param(Constants.WRAPPER) Wrapper<ZdPageFormData> queryWrapper, @Param(FormConstant.TABLE_NAME) String tableName);


    /**
     * 根据 entity 条件，查询全部记录（并翻页）
     *
     * @param page         分页查询条件（可以为 RowBounds.DEFAULT）
     * @param queryWrapper 实体对象封装操作类（可以为 null）
     * @param tableName    表名称
     */
    <E extends IPage<ZdPageFormData>> E selectPage(E page, @Param(Constants.WRAPPER) Wrapper<ZdPageFormData> queryWrapper, @Param(FormConstant.TABLE_NAME) String tableName);

    /**
     * 更新人员orgId
     *
     * @param tableName 表名称
     * @param id        主键
     * @param creatorId 创建者
     * @param updaterId 修改者
     * @return 影响行数
     */
    Integer updateOrgId(@Param(FormConstant.TABLE_NAME) String tableName, @Param("id") Long id, @Param("creatorId") String creatorId, @Param("updaterId") String updaterId);
}
