package com.sinitek.sirm.nocode.form.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sinitek.data.mybatis.base.BaseAuditEntity;
import com.sinitek.sirm.nocode.support.mybatis.handler.JsonbTypeHandler;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 页面表单历史表
 *
 * @TableName zd_page_form_history
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value = "zd_page_form_history")
@Data
@ApiModel(description = "历史表单实体")
public class ZdPageFormHistory extends BaseAuditEntity {
    /**
     * 表单配置
     */
    @TableField(typeHandler = JsonbTypeHandler.class)
    @ApiModelProperty(value = "表单配置")
    private String pageData;

    /**
     * 页面表单id
     */
    @ApiModelProperty(value = "页面表单id")
    private Long pageFormId;

    @ApiModelProperty("修改版本")
    private Integer updateVersion;
}
