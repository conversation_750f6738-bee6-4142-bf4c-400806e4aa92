package com.sinitek.sirm.nocode.app.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.sinitek.sirm.nocode.app.enumerate.PlatformTypeEnum;
import com.sinitek.sirm.nocode.common.base.IdBaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * 应用OA集成表
 *
 * @TableName zd_app_oa_integration
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(description = "应用OA系统集成实体")
@TableName(value = "zd_app_oa_integration")
public class ZdAppOaIntegration extends IdBaseEntity {
    /**
     * 应用编码
     */
    @NotEmpty(message = "应用编码不能未空")
    @ApiModelProperty(value = "应用编码", example = "app_24e8d143087149ab8b0d1114f1464816", required = true)
    private String appCode;

    /**
     * 平台类型
     */
    @NotNull(message = "平台类型不能为空")
    @ApiModelProperty(value = "平台类型，0:企微，1:钉钉", example = "0", required = true)
    private PlatformTypeEnum platformType;

    /**
     * 参数，不同平台的参数类型不一样
     */
    @NotEmpty(message = "参数不能为空")
    @ApiModelProperty(value = "参数，接入不同平台的参数类型不一样，是个json字符串", example = "\"{\"appKey\":\"weixing123456\",\"appSecret\":\"MDJIGDKSGDGDGDSDDG\",\"agentId\":\"123456\"}\"", required = true)
    private String param;
}
