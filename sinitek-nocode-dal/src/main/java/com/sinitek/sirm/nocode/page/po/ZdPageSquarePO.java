package com.sinitek.sirm.nocode.page.po;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2025.0718
 * @since 1.0.0-SNAPSHOT
 */
@Data
public class ZdPageSquarePO {
    @ApiModelProperty("应用编码")
    private String appCode;
    @ApiModelProperty("应用名称")
    private String appName;
    /**
     * 应用图标类型
     */
    @ApiModelProperty("应用图标类型")
    private String iconfont;


    /**
     * 主题颜色
     */
    @ApiModelProperty("主题颜色")
    private String themeColor;

    @JsonIgnore
    @ApiModelProperty("应用管理员orgId")
    private List<String> creatorIds;

    @ApiModelProperty("应用管理员名称")
    private List<String> creators;

    @ApiModelProperty("页面类型")
    private Integer pageType;
    @ApiModelProperty("页面名称")
    private String name;
    @ApiModelProperty("表单编码")
    private String code;
    @ApiModelProperty("发布时间")
    private Date publishTime;
    @ApiModelProperty("主键")
    private Long id;
    @ApiModelProperty("是否收藏")
    private Boolean favoritesFlag;
}
