<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinitek.sirm.nocode.form.mapper.ZdExportTaskMapper">
    <sql id="Base_Column_List">
        id, version, createtimestamp, updatetimestamp, file_name, file_count, status, operator_id, operate_time, export_params, error_msg, form_code
    </sql>
    
    <select id="search" resultType="com.sinitek.sirm.nocode.form.po.ZdExportTaskSearchResultPO">
        SELECT 
            id,
            file_name,
            file_count,
            status,
            operator_id,
            operate_time,
            form_code
        FROM zd_export_task
        <where>
            form_code = #{param.formCode}
            <if test="param.fileName != null and param.fileName != ''">
                <bind name="fileName_like" value="@com.sinitek.sirm.common.utils.SQLUtils@like(param.fileName)"/>
                AND file_name LIKE #{fileName_like} ESCAPE '/'
            </if>
            <if test="param.status != null and param.status.size() > 0">
                AND status in
                <foreach item="item" collection="param.status" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="param.operatorIds != null and param.operatorIds.size() > 0">
                AND operator_id in
                <foreach item="item" collection="param.operatorIds" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="param.operateTimeStart != null and param.operateTimeStart != ''">
                AND operate_time <![CDATA[ >= ]]> #{param.operateTimeStart}
            </if>
            <if test="param.operateTimeEnd != null and param.operateTimeEnd != ''">
                AND operate_time <![CDATA[ <= ]]> #{param.operateTimeEnd}
            </if>
        </where>
        <if test="@org.apache.commons.lang.StringUtils@isBlank(param.orderName)">
            ORDER BY createtimestamp DESC
        </if>
    </select>
</mapper> 