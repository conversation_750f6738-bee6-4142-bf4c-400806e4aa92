package com.sinitek.sirm.nocode.form.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sinitek.sirm.nocode.common.base.IdBaseEntity;
import com.sinitek.sirm.nocode.common.enumerate.YesOrNoEnum;
import com.sinitek.sirm.nocode.form.enumerate.AiOutFormatEnum;
import com.sinitek.sirm.nocode.form.enumerate.AiOutWayEnum;
import com.sinitek.sirm.nocode.form.enumerate.AiResultOutPositionEnum;
import com.sinitek.sirm.nocode.support.mybatis.handler.JsonbTypeHandler;
import com.sinitek.sirm.nocode.support.mybatis.handler.ListIntegerTypeHandler;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.ibatis.type.JdbcType;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 智搭AI问答设置实体类
 *
 * <AUTHOR>
 * @version 2025.0619
 * @TableName zd_page_form_ai_question_setting
 * @since 1.0.0-SNAPSHOT
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value = "zd_page_form_ai_question_setting", autoResultMap = true)
@Data
public class ZdPageFormAiQuestionSetting extends IdBaseEntity {


    /**
     * 表单编码
     */
    @NotBlank(message = "表单编码不能为空")
    @Length(max = 50, message = "表单编码不能超过50个字符")
    @ApiModelProperty(value = "表单编码", example = "form_123456", required = true)
    private String formCode;


    /**
     * 模型编码
     */
    @NotBlank(message = "模型编码")
    @Length(max = 50, message = "模型编码不能超过50个字符")
    @ApiModelProperty(value = "模型编码", example = "10828dbb-37f3-4ed8-a6ea-e2e31f6d7308", required = true)
    private String modelCode;

    /**
     * 输入内容
     */
    @NotBlank(message = "输入内容不能为空")
    @Length(max = 3000, message = "输入内容不能超过3000个字符")
    @ApiModelProperty(value = "输入内容", example = "请根据以下信息生成报告", required = true)
    private String inputContent;

    /**
     * 输入参数
     */
    @TableField(typeHandler = JsonbTypeHandler.class)
    @ApiModelProperty(value = "输入参数")
    private String inputVar;

    /**
     * 输出内容
     */
    @Length(max = 3000, message = "输出内容不能超过3000个字符")
    @ApiModelProperty(value = "输出内容", example = "AI生成的回答内容")
    private String outContent;

    /**
     * 输出位置
     */
    @NotNull(message = "输出位置不能为空")
    private AiResultOutPositionEnum outPosition;

    /**
     * 输出格式
     */
    @NotNull(message = "输出格式不能为空")
    private AiOutFormatEnum outFormat;

    /**
     * 是否进行智能markdown转换
     */
    @NotNull(message = "是否进行智能markdown转换不能为空")
    private YesOrNoEnum markdownConversion;

    /**
     * 当进行markdown转换时，允许的格式
     */
    @TableField(typeHandler = ListIntegerTypeHandler.class, jdbcType = JdbcType.VARCHAR)
    private List<Integer> markdownConversionType;

    /**
     * 输出方式
     */
    @NotNull(message = "输出方式不能为空")
    private AiOutWayEnum outWay;
}
