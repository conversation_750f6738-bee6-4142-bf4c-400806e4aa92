package com.sinitek.sirm.nocode.form.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sinitek.sirm.nocode.form.entity.ZdImportTask;
import com.sinitek.sirm.nocode.form.po.ZdImportTaskSearchParamPO;
import com.sinitek.sirm.nocode.form.po.ZdImportTaskSearchResultPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 导入任务Mapper
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Mapper
public interface ZdImportTaskMapper extends BaseMapper<ZdImportTask> {

    IPage<ZdImportTaskSearchResultPO> search(Page<ZdImportTaskSearchResultPO> page,
            @Param("param") ZdImportTaskSearchParamPO param);
}