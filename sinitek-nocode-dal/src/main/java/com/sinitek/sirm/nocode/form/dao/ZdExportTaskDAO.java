package com.sinitek.sirm.nocode.form.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sinitek.sirm.nocode.form.entity.ZdExportTask;
import com.sinitek.sirm.nocode.form.mapper.ZdExportTaskMapper;
import com.sinitek.sirm.nocode.form.po.ZdExportTaskSearchParamPO;
import com.sinitek.sirm.nocode.form.po.ZdExportTaskSearchResultPO;
import org.springframework.stereotype.Repository;

/**
 * 导出任务DAO
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Repository
public class ZdExportTaskDAO extends ServiceImpl<ZdExportTaskMapper, ZdExportTask> {

    /**
     * 分页查询导出任务列表
     *
     * @param param 查询参数
     * @return 查询结果
     */
    public IPage<ZdExportTaskSearchResultPO> search(ZdExportTaskSearchParamPO param) {
        return this.baseMapper.search(param.buildPage(), param);
    }
} 