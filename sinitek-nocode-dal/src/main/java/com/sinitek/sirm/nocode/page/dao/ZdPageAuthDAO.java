package com.sinitek.sirm.nocode.page.dao;

import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sinitek.sirm.nocode.page.entity.ZdPageAuth;
import com.sinitek.sirm.nocode.page.mapper.ZdPageAuthMapper;
import org.apache.ibatis.binding.MapperMethod;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;

/**
 * <AUTHOR>
 * @version 2025.0407
 * @description
 * @since 1.0.0-SNAPSHOT
 */
@Repository
public class ZdPageAuthDAO extends ServiceImpl<ZdPageAuthMapper, ZdPageAuth> {


    /**
     * 批量更新页面权限的排序字段
     *
     * @param entityList 页面权限实体集合，包含需要更新排序的记录
     * @return 是否更新成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateSortBatchById(Collection<ZdPageAuth> entityList) {
        // 获取更新排序的 SQL 语句标识
        String sqlStatement = getSqlStatement("updateSortById");
        // 执行批量更新操作
        return executeBatch(entityList, DEFAULT_BATCH_SIZE, (sqlSession, entity) -> {
            MapperMethod.ParamMap<ZdPageAuth> param = new MapperMethod.ParamMap<>();
            param.put(Constants.ENTITY, entity);
            sqlSession.update(sqlStatement, param);
        });
    }

    /**
     * 根据方法名生成对应的 SQL 语句全限定名
     *
     * @param methodName 方法名
     * @return SQL 语句全限定名
     */
    private String getSqlStatement(String methodName) {
        return ZdPageAuthMapper.class.getName() + StringPool.DOT + methodName;

    }


}
