package com.sinitek.sirm.nocode.form.po;

import com.sinitek.sirm.framework.frontend.support.PageDataParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 导入任务查询参数PO
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "导入任务查询参数PO")
public class ZdImportTaskSearchParamPO extends PageDataParam {

    @ApiModelProperty(value = "表单编码", required = true)
    @NotBlank(message = "表单编码不能为空")
    private String formCode;

    @ApiModelProperty("文件名")
    private String fileName;

    @ApiModelProperty("状态：0-待处理，1-处理中，2-处理成功，3-处理失败")
    private List<Integer> status;

    @ApiModelProperty("操作人id")
    private List<String> operatorIds;

    @ApiModelProperty("操作时间开始")
    private String operateTimeStart;

    @ApiModelProperty("操作时间结束")
    private String operateTimeEnd;
} 