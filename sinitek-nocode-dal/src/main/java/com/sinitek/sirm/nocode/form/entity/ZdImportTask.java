package com.sinitek.sirm.nocode.form.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.sinitek.data.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 导入任务表
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@TableName("zd_import_task")
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "ZdImportTask", description = "导入任务表")
public class ZdImportTask extends BaseEntity {

    @ApiModelProperty("文件名")
    private String fileName;

    @ApiModelProperty("状态：0-待处理，1-处理中，2-处理成功，3-处理失败")
    private Integer status;

    @ApiModelProperty("操作人id")
    private String operatorId;

    @ApiModelProperty("操作时间")
    private Date operateTime;

    @ApiModelProperty("导入参数")
    private String param;

    @ApiModelProperty("导入数据")
    private String data;

    @ApiModelProperty("错误信息")
    private String errorMsg;

    @ApiModelProperty("总记录数")
    private Integer totalCount;

    @ApiModelProperty("成功记录数")
    private Integer successCount;

    @ApiModelProperty("失败记录数")
    private Integer failedCount;

    @ApiModelProperty("表单编码")
    private String formCode;
} 