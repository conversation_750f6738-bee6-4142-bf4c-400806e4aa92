package com.sinitek.sirm.nocode.page.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.sinitek.sirm.nocode.common.base.IdBaseEntity;
import com.sinitek.sirm.nocode.page.enumerate.SubmitJumpTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 页面的基本设置表
 *
 * @TableName zd_page_base_setting
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value = "zd_page_base_setting")
@Data
@ApiModel(description = "页面的基本设置实体")
public class ZdPageBaseSetting extends IdBaseEntity {

    /**
     * 页面编码
     */
    @NotBlank(message = "页面编码不能为空")
    @ApiModelProperty(value = "页面编码", example = "page_114b522908e04cd09deeea7f4c25ca25", required = true)
    private String pageCode;

    /**
     * 页面提交后跳转参数
     */
    @ApiModelProperty(value = "页面提交后跳转参数,应该是一个json数组", example = "\"[{name:\"id\",type:0}]\"")
    private String jumpParam;

    /**
     * 页面提交后跳转的类型，有三种，0:跳转默认页面，1：应用内页面，2:外部链接
     */
    @NotNull(message = "页面提交后跳转的类型不能为空")
    private SubmitJumpTypeEnum submitJumpType;

    /**
     * 页面提交后跳转的地址
     */
    @ApiModelProperty(value = "页面提交后跳转的地址", example = "0")
    private String submitJumpUrl;

    /**
     * 页面编码
     */
    @ApiModelProperty(value = "应用内页面编码，当跳转类型为1（应用内页面）时，这个字段有值", example = "page_114b522908e04cd09deeea7f4c25ca25")
    private String innerPageCode;
}
