package com.sinitek.sirm.nocode.page.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.sinitek.data.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * AI工作流配置表
 *
 * <AUTHOR>
 * @since 2025-08-12
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@TableName("zd_ai_workflow_config")
@ApiModel(value = "ZdAiWorkflowConfig", description = "AI工作流配置表")
public class ZdAiWorkflowConfig extends BaseEntity {

    public static final String ENTITY_NAME = "ZD_AI_WORKFLOW_CONFIG";

    @ApiModelProperty("事件类型")
    private String eventType;

    @ApiModelProperty("工作流应用id")
    private String appId;

    @ApiModelProperty("工作流应用apiKey")
    private String apiKey;

    @ApiModelProperty("操作人id")
    private String operatorId;

    @ApiModelProperty("操作时间")
    private Date operateTime;

    @ApiModelProperty("表单编码")
    private String formCode;
}