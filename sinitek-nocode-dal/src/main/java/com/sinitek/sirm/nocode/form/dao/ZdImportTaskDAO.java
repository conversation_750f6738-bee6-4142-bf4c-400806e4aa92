package com.sinitek.sirm.nocode.form.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sinitek.sirm.nocode.form.entity.ZdImportTask;
import com.sinitek.sirm.nocode.form.mapper.ZdImportTaskMapper;
import com.sinitek.sirm.nocode.form.po.ZdImportTaskSearchParamPO;
import com.sinitek.sirm.nocode.form.po.ZdImportTaskSearchResultPO;
import org.springframework.stereotype.Repository;

/**
 * 导入任务DAO
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Repository
public class ZdImportTaskDAO extends ServiceImpl<ZdImportTaskMapper, ZdImportTask> {

    public IPage<ZdImportTaskSearchResultPO> search(ZdImportTaskSearchParamPO param) {
        return this.baseMapper.search(param.buildPage(), param);
    }
}