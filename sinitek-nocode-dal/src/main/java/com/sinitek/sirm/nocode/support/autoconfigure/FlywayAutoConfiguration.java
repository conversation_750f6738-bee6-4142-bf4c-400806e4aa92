package com.sinitek.sirm.nocode.support.autoconfigure;

import cn.hutool.core.util.ReflectUtil;
import com.zaxxer.hikari.HikariDataSource;
import lombok.extern.slf4j.Slf4j;
import org.flywaydb.core.Flyway;
import org.flywaydb.core.api.configuration.ClassicConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.flyway.FlywayMigrationStrategy;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.EventListener;

import javax.sql.DataSource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 2025.0506
 * @description flyway自动配置
 * @since 1.0.0-SNAPSHOT
 */
@Configuration
@Slf4j
@ConditionalOnProperty(prefix = "spring.flyway", name = "enabled")
public class FlywayAutoConfiguration implements AutoCloseable {
    private final HikariDataSource hikariDataSource;


    public FlywayAutoConfiguration(Flyway flyway, DataSource dataSource) {
        // 由于druid.wall 对postgresql 的存储过程不支持，故重新设置数据源
        this.hikariDataSource = DataSourceBuilder.derivedFrom(dataSource)
                .type(HikariDataSource.class)
                .build();
        ClassicConfiguration configuration = (ClassicConfiguration) ReflectUtil.getFieldValue(flyway, "configuration");
        configuration.setDataSource(hikariDataSource);
        log.info("flyway jdbcUrl: {}", hikariDataSource.getJdbcUrl());

    }

    @ConditionalOnProperty(value = "spring.flyway.repair-on-migrate", havingValue = "true")
    @Bean
    public FlywayMigrationStrategy cleanMigrateStrategy() {
        return flyway -> {
            log.info("开始检查有没有执行错误的脚本，如果有则先清理掉");
            flyway.repair();
            log.info("开始执行SQL脚本");
            flyway.migrate();
        };
    }

    @Override
    public void close() throws Exception {
        if (Objects.nonNull(hikariDataSource)) {
            log.info("关闭flyway数据源");
            hikariDataSource.close();
        }

    }

    @EventListener(ApplicationReadyEvent.class)
    public void closeFlywayDataSource() {
        try {
            close();
        } catch (Exception e) {
            log.error("flyway关闭数据源失败: {}", e.getMessage(), e);
        }

    }
}
