package com.sinitek.sirm.nocode.app.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.sinitek.sirm.nocode.common.base.IdBaseEntity;
import com.sinitek.sirm.nocode.llm.enumerate.AiSourceEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 应用AI设置实体类
 *
 * <AUTHOR>
 * @version 2025.0729
 * @TableName zd_app_ai_setting
 * @since 1.0.0-SNAPSHOT
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "zd_app_ai_setting")
public class ZdAppAiSetting extends IdBaseEntity {

    /**
     * 应用编码
     */
    @ApiModelProperty(value = "应用编码", example = "app_123456", required = true)
    private String appCode;


    /**
     * AI源的key
     */
    @ApiModelProperty(value = "AI源的key", example = "gpt-4-turbo", required = true)
    private AiSourceEnum aiSource;


    /**
     * 模型类型key
     */
    @ApiModelProperty(value = "模型类型key", example = "llm", required = true)
    private String typeKey;

    /**
     * 模型应用名称
     */
    @ApiModelProperty(value = "模型应用名称", example = "OpenAI", required = true)
    private String appName;

    /**
     * 应用密钥
     */
    @ApiModelProperty(value = "应用密钥", example = "openai", required = true)
    private String apiKey;
}
