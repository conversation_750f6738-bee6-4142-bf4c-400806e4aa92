<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinitek.sirm.nocode.page.mapper.ZdPageSceneMapper">

    <select id="findTask" resultMap="com.sinitek.sirm.nocode.page.mapper.ZdPageSceneMapper.mybatis-plus_ZdPageScene">
        select s.id,
               s.repeat_type,
               s.start_time,
               s.report_send_org_ids,
               s.note_type,
               s.holiday_strategy,
               s.page_code,
               s.next_execute_date,
               s.month_day,
               s.week_day
        from zd_page_scene s
                 inner join zd_page_form f on s.page_code = f.page_code
        where (s.end_date is null or s.end_date > #{date})
          and s.scene_type = 1
          and (s.finish_date is null or s.finish_date <![CDATA[<>]]> #{date})
          and (s.next_execute_date is null or s.next_execute_date = #{date})
          and f.publish_status = 2
          and s.note_type is not null
          and s.start_time is not null
    </select>


</mapper>
