package com.sinitek.sirm.nocode.form.dao;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.enums.SqlMethod;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.sinitek.sirm.framework.exception.BussinessException;
import com.sinitek.sirm.nocode.common.mapper.CommonMapper;
import com.sinitek.sirm.nocode.form.constant.FormConstant;
import com.sinitek.sirm.nocode.form.entity.ZdPageFormData;
import com.sinitek.sirm.nocode.form.mapper.ZdPageFormDataMapper;
import com.sinitek.sirm.nocode.support.mybatis.conditions.query.LamWrapper;
import org.apache.ibatis.binding.MapperMethod;
import org.apache.ibatis.logging.Log;
import org.apache.ibatis.logging.LogFactory;
import org.apache.ibatis.session.SqlSession;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.function.BiConsumer;

/**
 * <AUTHOR>
 * @version 2025.0408
 * @description 表单数据dao层操作
 * @since 1.0.0-SNAPSHOT
 */
@Repository
public class ZdPageFormDataDAO {
    protected Log log = LogFactory.getLog(getClass());
    @Resource
    private ZdPageFormDataMapper mapper;
    @Resource
    private CommonMapper commonMapper;


    private static final Class<ZdPageFormData> entityClass = ZdPageFormData.class;
    private static final Class<ZdPageFormDataMapper> mapperClass = ZdPageFormDataMapper.class;

    /**
     * <p>
     * 表单数据保存
     * </p>
     */
    public boolean save(ZdPageFormData zdPageFormData) {
        checkTableName(zdPageFormData);
        return SqlHelper.retBool(mapper.insert(zdPageFormData));
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean saveBatch(Collection<ZdPageFormData> entityList, int batchSize) {
        String sqlStatement = getSqlStatement(SqlMethod.INSERT_ONE);
        return executeBatch(entityList, batchSize, (sqlSession, entity) -> sqlSession.insert(sqlStatement, entity));
    }


    /**
     * TableId 注解存在更新记录，否插入一条记录
     *
     * @param entity 实体对象
     * @return boolean
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean saveOrUpdate(ZdPageFormData entity) {
        if (null != entity) {
            return notExists(entity) ? save(entity) : updateById(entity);
        }
        return false;
    }

    /**
     * 获取一条数据
     *
     * @param queryWrapper 查询条件
     * @param tableName    表名
     * @return 数据
     */
    public ZdPageFormData getOne(Wrapper<ZdPageFormData> queryWrapper, String tableName) {
        return mapper.selectOne(queryWrapper, tableName);
    }


    /**
     * 根据id获取数据
     *
     * @param id        id
     * @param tableName 表名
     * @return 数据
     */
    public ZdPageFormData getById(Long id, String tableName) {
        checkTableName(tableName);
        LambdaQueryWrapper<ZdPageFormData> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(ZdPageFormData::getId, id);
        return mapper.selectOne(queryWrapper, tableName);
    }

    /**
     * 根据id列表获取数据
     *
     * @param idList    id列表
     * @param tableName 表名
     * @return 数据列表
     */
    public List<ZdPageFormData> listByIds(Collection<? extends Serializable> idList, String tableName) {
        return mapper.selectBatchIds(idList, tableName);
    }

    public List<ZdPageFormData> list(LamWrapper<ZdPageFormData> queryWrapper) {
        return mapper.selectList(queryWrapper, queryWrapper.getTableName());
    }

    public boolean updateById(ZdPageFormData entity) {
        checkTableName(entity);
        return SqlHelper.retBool(mapper.updateById(entity, entity.getTableName()));
    }


    public boolean update(ZdPageFormData entity, Wrapper<ZdPageFormData> updateWrapper, String tableName) {
        return SqlHelper.retBool(mapper.update(entity, updateWrapper, tableName));
    }

    public boolean update(Wrapper<ZdPageFormData> updateWrapper, String tableName) {
        return SqlHelper.retBool(mapper.update(null, updateWrapper, tableName));
    }


    @Transactional(rollbackFor = Exception.class)
    public boolean saveOrUpdateBatch(Collection<ZdPageFormData> entityList, int batchSize) {
        return SqlHelper.saveOrUpdateBatch(entityClass, mapperClass, this.log, entityList, batchSize, (sqlSession, entity) -> notExists(entity), (sqlSession, entity) -> {
            MapperMethod.ParamMap<Object> param = new MapperMethod.ParamMap<>();
            param.put(Constants.ENTITY, entity);
            param.put(FormConstant.TABLE_NAME, entity.getTableName());
            sqlSession.update(getSqlStatement(SqlMethod.UPDATE_BY_ID), param);
        });
    }


    public ZdPageFormData removeById(Long id, String tableName) {
        ZdPageFormData one = getById(id, tableName);
        getBaseMapper().deleteById(new ZdPageFormData(id, tableName));
        return one;
    }


    public List<ZdPageFormData> removeByIds(Collection<? extends Serializable> idList, String tableName) {
        List<ZdPageFormData> zdPageFormData = listByIds(idList, tableName);
        getBaseMapper().deleteBatchIds(idList, tableName);
        return zdPageFormData;
    }

    public boolean remove(Wrapper<ZdPageFormData> queryWrapper, String tableName) {
        return SqlHelper.retBool(getBaseMapper().delete(queryWrapper, tableName));
    }

    /**
     * 删除所有数据
     *
     * @param tableName 表名
     * @return 删除结果
     */
    public List<ZdPageFormData> removeAll(String tableName) {
        List<ZdPageFormData> list = new ArrayList<>();
        LamWrapper<ZdPageFormData> query = LamWrapper.<ZdPageFormData>ins(tableName)
                .max(ZdPageFormData::getId);
        Long value = commonMapper.longValue(query);
        if (Objects.nonNull(value)) {
            // 小于或者等于最大值，都要删除
            query.le(ZdPageFormData::getId, value)
                    .clearSqlSelect()
                    .select(ZdPageFormData::getId,
                            ZdPageFormData::getFormData,
                            ZdPageFormData::getSubmitField
                    );
            // 首先查询
            list = list(query);
            // 删除
            remove(query, tableName);
        }
        return list;

    }


    /**
     * 判断是否存在
     *
     * @param query 查询
     * @return 是否存在
     */
    public boolean exists(LamWrapper<ZdPageFormData> query) {
        return commonMapper.exists(query);
    }

    /**
     * 字符串值
     *
     * @param query 查询
     * @return 字符串值
     */
    public String stringValue(LamWrapper<ZdPageFormData> query) {
        return commonMapper.stringValue(query);
    }

    /**
     * 根据查询条件，查询满足条件的 Long 类型列表
     *
     * @param query 查询条件封装对象，包含查询字段和条件
     * @return 返回满足条件的 Long 类型数据列表
     */
    public List<Long> listLong(LamWrapper<ZdPageFormData> query) {
        return commonMapper.listLong(query);
    }


    /**
     * 检查表名
     *
     * @param entity 实体
     */
    private static void checkTableName(ZdPageFormData entity) {
        checkTableName(entity.getTableName());
    }

    /**
     * 检查表名
     *
     * @param tableName 表的名称
     */
    private static void checkTableName(String tableName) {
        if (StringUtils.isBlank(tableName)) {
            throw new BussinessException(true, "表名称不能为空！");
        }
    }

    /**
     * 获取 SQL 语句
     *
     * @param sqlMethod ignore
     * @return ignore
     * @since 3.3.1
     */
    protected String getSqlStatement(SqlMethod sqlMethod) {
        return SqlHelper.getSqlStatement(ZdPageFormDataMapper.class, sqlMethod);
    }


    /**
     * 不存在
     *
     * @param entity 实体
     * @return 是否不存在
     */
    protected boolean notExists(ZdPageFormData entity) {
        Long id = entity.getId();
        return StringUtils.checkValNull(id) || !exists(id, entity.getTableName());
    }

    private boolean exists(Long id, String tableName) {
        LamWrapper<ZdPageFormData> query = LamWrapper.<ZdPageFormData>ins(tableName)
                .eq(ZdPageFormData::getId, id);
        return commonMapper.exists(query);
    }

    /**
     * 获取查询条件
     *
     * @param tableName 表名
     * @return 查询条件
     */
    public LamWrapper<ZdPageFormData> query(String tableName) {
        return LamWrapper.ins(tableName);
    }


    /**
     * 执行批量操作
     *
     * @param list      数据集合
     * @param batchSize 批量大小
     * @param consumer  执行方法
     * @return 操作结果
     * @since 3.3.1
     */
    protected boolean executeBatch(Collection<ZdPageFormData> list, int batchSize, BiConsumer<SqlSession, ZdPageFormData> consumer) {
        return SqlHelper.executeBatch(entityClass, log, list, batchSize, consumer);
    }

    public ZdPageFormDataMapper getBaseMapper() {
        return mapper;
    }
}
