package com.sinitek.sirm.nocode.common.mapper;

import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.sinitek.sirm.nocode.support.mybatis.conditions.query.LamWrapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 2025.0509
 * @since 1.0.0-SNAPSHOT
 */

public interface CommonMapper {

    /**
     * 根据 Wrapper 条件，查询是否存在
     *
     * @param queryWrapper 实体对象封装操作类（可以为 null）
     */

    <T> boolean exists(@Param(Constants.WRAPPER) LamWrapper<T> queryWrapper);

    /**
     * 查询满足条件的 Long 类型值
     *
     * @param queryWrapper 实体对象封装操作类（可以为 null）
     * @param <T>          泛型类型
     * @return 返回查询到的 Long 类型值，若无结果则返回 null
     */
    <T> Long longValue(@Param(Constants.WRAPPER) LamWrapper<T> queryWrapper);

    /**
     * 字符串值
     *
     * @param queryWrapper 查询条件
     * @param <T>          泛型
     * @return 字符串值
     */
    <T> String stringValue(@Param(Constants.WRAPPER) LamWrapper<T> queryWrapper);

    /**
     * 根据 Wrapper 条件，查询满足条件的 String 类型列表
     *
     * @param queryWrapper 实体对象封装操作类（可以为 null）
     * @param <T>           泛型类型
     * @return 返回 String 类型的列表结果
     */
    <T> List<String> listString(@Param(Constants.WRAPPER) LamWrapper<T> queryWrapper);

    /**
     * 根据 Wrapper 条件，查询满足条件的 Long 类型列表
     *
     * @param queryWrapper 实体对象封装操作类（可以为 null）
     * @param <T>           泛型类型
     * @return 返回 Long 类型的列表结果
     */
    <T> List<Long> listLong(@Param(Constants.WRAPPER) LamWrapper<T> queryWrapper);

    /**
     * 执行动态SQL查询
     *
     * @param sql SQL查询语句
     * @return 查询结果
     */
    List<Map<String, Object>> executeDynamicQuery(@Param("sql") String sql);

    /**
     * 执行动态SQL查询（带参数）
     *
     * @param sql    SQL查询语句
     * @param params 参数Map
     * @return 查询结果
     */
    List<Map<String, Object>> executeDynamicQueryWithParams(@Param("sql") String sql, @Param("params") Map<String, Object> params);

}
