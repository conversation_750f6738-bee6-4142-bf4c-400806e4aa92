package com.sinitek.sirm.nocode.page.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025-08-12 16:37
 */
@Data
@NoArgsConstructor
@ApiModel("智搭ai工作流绑定参数")
public class ZdAiWorkflowBindParamPO {

    @ApiModelProperty("事件类型")
    private String eventType;

    @ApiModelProperty("应用id")
    private String appId;

    @ApiModelProperty("表单编码")
    private String formCode;

    @ApiModelProperty("操作人")
    private String operatorId;

    @ApiModelProperty("操作时间")
    private Date operateTime;
}
