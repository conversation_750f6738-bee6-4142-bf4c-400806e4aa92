<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinitek.sirm.nocode.form.mapper.ZdPageFormDataMapper">
    <select id="exists" resultType="java.lang.Boolean">
        select 1
        from ${tableName}
        where id = #{id}
    </select>

    <update id="updateOrgId">
        update ${tableName}
        <set>
            <if test="creatorId != null">
                creator_id = #{creatorId},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId},
            </if>
        </set>
        where id=#{id}
    </update>
</mapper>
