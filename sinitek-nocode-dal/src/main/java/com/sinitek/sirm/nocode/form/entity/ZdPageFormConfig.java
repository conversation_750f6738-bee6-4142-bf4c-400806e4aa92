package com.sinitek.sirm.nocode.form.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.sinitek.sirm.nocode.common.base.IdBaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 表单配置表
 *
 * @TableName zd_page_form_config
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value = "zd_page_form_config")
@Data
@ApiModel(description = "表单配置实体")
public class ZdPageFormConfig extends IdBaseEntity {
    /**
     * 表单编码
     */
    @ApiModelProperty(value = "表单编码", example = "a10000")
    private String formCode;

    /**
     * 数据表的名称,当为普通表单或者是流程表单时，这个字段有值
     */
    @ApiModelProperty(value = "数据表的名称,当为普通表单或者是流程表单时，这个字段有值", example = "zd_page_form_data_0")
    private String tableName;

    /**
     * 当表单为流程表单时，这个字段有值
     */
    @ApiModelProperty(value = "当表单为流程表单时，这个字段有值", example = "yearWorkFlow")
    private String processcode;
}