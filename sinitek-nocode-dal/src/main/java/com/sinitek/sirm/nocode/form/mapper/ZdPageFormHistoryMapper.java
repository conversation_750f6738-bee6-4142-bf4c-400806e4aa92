package com.sinitek.sirm.nocode.form.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sinitek.sirm.nocode.form.entity.ZdPageFormHistory;
import com.sinitek.sirm.nocode.form.po.ZdPageFormHistoryPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2025-03-13 16:13:23
 * @description 针对表【zd_page_form_history(页面表单历史表)】的数据库操作Mapper
 * @Entity com.sinitek.sirm.nocode.form.entity.ZdPageFormHistory
 */
public interface ZdPageFormHistoryMapper extends BaseMapper<ZdPageFormHistory> {
    /**
     * 根据表单id列表查询历史表单
     *
     * @param formIdList 表单id列表
     * @return 表单历史数据
     */
    List<ZdPageFormHistoryPO> findByFormIdList(@Param("formIdList") List<Long> formIdList);

}




