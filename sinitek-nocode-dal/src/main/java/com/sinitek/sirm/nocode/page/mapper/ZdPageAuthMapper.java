package com.sinitek.sirm.nocode.page.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.sinitek.sirm.nocode.page.entity.ZdPageAuth;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

/**
 * <AUTHOR>
 * @version 2025-03-12 13:38:21
 * @description 针对表【zd_page_auth(页面权限表)】的数据库操作Mapper
 * @Entity com.sinitek.sirm.nocode.page.entity.ZdPageAuth
 */
public interface ZdPageAuthMapper extends BaseMapper<ZdPageAuth> {
    /**
     * 通过权限id 获取应用的code
     *
     * @param id 权限id
     * @return 应用编码
     */
    String getAppCodeByPageAuthId(@Param("id") Long id);

    /**
     * 通过权限id 修改权限的排序
     *
     * @param zdPageAuth 权限对象
     * @return 修改成功
     */
    @Update("update zd_page_auth set sort = #{et.sort} where id = #{et.id}")
    boolean updateSortById(@Param(Constants.ENTITY) ZdPageAuth zdPageAuth);
}




