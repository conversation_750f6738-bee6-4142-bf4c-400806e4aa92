package com.sinitek.sirm.nocode.form.entity;

import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sinitek.data.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 导出任务表
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@TableName("zd_export_task")
@ApiModel(value = "ZdExportTask", description = "导出任务表")
public class ZdExportTask extends BaseEntity {

    public static final String ENTITY_NAME = "ZD_EXPORT_TASK";

    @ApiModelProperty("文件名")
    private String fileName;

    @ApiModelProperty("文件个数")
    private Integer fileCount;

    @ApiModelProperty("状态：0-待处理，1-处理中，2-处理成功，3-处理失败")
    private Integer status;

    @ApiModelProperty("操作人id")
    private String operatorId;

    @ApiModelProperty("操作时间")
    private Date operateTime;

    @ApiModelProperty("导出参数")
    private String exportParams;

    @ApiModelProperty("错误信息")
    private String errorMsg;

    @ApiModelProperty("表单编码")
    private String formCode;
} 