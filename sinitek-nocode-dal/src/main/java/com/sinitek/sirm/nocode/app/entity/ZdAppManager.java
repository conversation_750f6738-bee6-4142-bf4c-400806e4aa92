package com.sinitek.sirm.nocode.app.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.sinitek.sirm.nocode.common.base.IdBaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 2025.0312
 * @since 1.0.0-SNAPSHOT
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(description = "应用管理员实体")
@TableName(value = "zd_app_manager")
public class ZdAppManager extends IdBaseEntity {
    /**
     * 应用编码
     */
    @ApiModelProperty(value = "应用编码", example = "abc1000")
    private String appCode;
    /**
     * orgId
     */
    @ApiModelProperty("组织id")
    private String orgId;
}
