package com.sinitek.sirm.nocode.page.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sinitek.sirm.nocode.support.mybatis.handler.JsonbTypeHandler;
import com.sinitek.sirm.nocode.support.mybatis.handler.ListIntegerTypeHandler;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.time.LocalDate;
import java.util.List;

/**
 * 场景收集表任务表
 *
 * <AUTHOR>
 * @version 2025.0804
 * @TableName zd_page_scene_task
 * @since 1.0.0-SNAPSHOT
 */
@Data
@ApiModel(description = "场景收集表任务实体")
@TableName(value = "zd_page_scene_task", autoResultMap = true)
public class ZdPageSceneTask {


    /**
     * 页面编码
     */
    @NotBlank(message = "页面编码不能为空")
    @ApiModelProperty(value = "页面编码", example = "page_114b522908e04cd09deeea7f4c25ca25", required = true)
    private String pageCode;

    /**
     * 接收人
     */
    @TableField(typeHandler = JsonbTypeHandler.class)
    @ApiModelProperty(value = "接收人，JSON格式", example = "[\"user1\", \"user2\"]")
    private String recipient;

    /**
     * 通知方式
     */
    @TableField(typeHandler = ListIntegerTypeHandler.class)
    @ApiModelProperty(value = "通知方式，JSON格式", example = "[1, 2]")
    private List<Integer> noteType;

    @ApiModelProperty("完成日期")
    private LocalDate finishDate;
}
