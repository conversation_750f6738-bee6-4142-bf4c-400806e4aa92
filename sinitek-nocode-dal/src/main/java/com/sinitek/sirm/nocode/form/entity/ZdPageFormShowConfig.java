package com.sinitek.sirm.nocode.form.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sinitek.sirm.nocode.common.base.IdBaseEntity;
import com.sinitek.sirm.nocode.support.mybatis.handler.JsonbTypeHandler;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 表单数据显示设置
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value = "zd_page_form_show_config")
@Data
@ApiModel(description = "表单数据显示实体")
public class ZdPageFormShowConfig extends IdBaseEntity {

    /**
     * 表单编码
     */
    @ApiModelProperty(value = "表单编码")
    private String formCode;

    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    private String orgId;

    /**
     * 是个json对象，例如:[{key:name,showOrder:0}]
     */
    @ApiModelProperty(value = "显示配置，是个json对象")
    @TableField(typeHandler = JsonbTypeHandler.class)
    private String showConfig;
}
