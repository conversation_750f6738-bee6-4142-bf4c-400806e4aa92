package com.sinitek.sirm.nocode.form.enumerate;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.sinitek.sirm.nocode.common.support.enumeratebase.BaseStringEnum;
import com.sinitek.sirm.nocode.common.utils.ZdStringUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @version 2025.0326
 * @since 1.0.0-SNAPSHOT
 */
@ApiModel(description = "条件关系枚举")
@Getter
public enum LogicOperatorEnum implements BaseStringEnum {
    AND("and", "并且"),
    OR("or", "或者");

    @JsonValue
    @ApiModelProperty("值")
    private final String value;
    @ApiModelProperty("中文名称")
    private final String label;

    LogicOperatorEnum(String value, String label) {
        this.value = value;
        this.label = label;
    }


    @JsonCreator
    public static LogicOperatorEnum fromValue(String value) {
        if (ZdStringUtils.isBlank(value)) {
            return null;
        }
        return Arrays.stream(LogicOperatorEnum.values()).filter(a -> value.equalsIgnoreCase(a.value)).findAny().orElse(null);
    }
}
