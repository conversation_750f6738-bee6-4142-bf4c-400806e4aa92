package com.sinitek.sirm.nocode.page.enumerate;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.sinitek.sirm.nocode.common.support.enumeratebase.BaseIntegerEnum;
import io.swagger.annotations.ApiModel;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 2025.0417
 * @description 场景提交规则
 * @since 1.0.0-SNAPSHOT
 */
@ApiModel(description = "场景提交规则枚举")
@Getter
public enum SceneSubmitEnum implements BaseIntegerEnum {
    NONE(0, "无"),
    ONCE(1, "每人限一次"),
    ;
    /**
     * 值
     */
    @JsonValue
    private final Integer value;
    /**
     * 名称
     */
    private final String label;

    SceneSubmitEnum(Integer value, String label) {
        this.value = value;
        this.label = label;
    }

    @JsonCreator
    public static SceneSubmitEnum fromValue(Integer value) {
        return Arrays.stream(SceneSubmitEnum.values()).filter(a -> Objects.equals(value, a.getValue())).findAny().orElse(null);
    }
}
