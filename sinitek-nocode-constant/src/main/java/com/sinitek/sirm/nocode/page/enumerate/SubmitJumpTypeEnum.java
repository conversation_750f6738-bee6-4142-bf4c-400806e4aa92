package com.sinitek.sirm.nocode.page.enumerate;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.sinitek.sirm.nocode.common.support.enumeratebase.BaseIntegerEnum;
import io.swagger.annotations.ApiModel;
import java.util.Arrays;
import java.util.Objects;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 2025.0624
 * @since 1.0.0-SNAPSHOT
 */
@ApiModel(description = "页面提交后跳转的类")
@Getter
public enum SubmitJumpTypeEnum implements BaseIntegerEnum {
    DEFAULT_PAGE(0, "默认页面"),
    APP_PAGE(1, "应用内页面"),
    OUT_PAGE(2, "外部链接"),
    ;
    /**
     * 值
     */
    @JsonValue
    private final Integer value;
    /**
     * 名称
     */
    private final String label;

    SubmitJumpTypeEnum(Integer value, String label) {
        this.value = value;
        this.label = label;
    }

    @JsonCreator
    public static SubmitJumpTypeEnum fromValue(Integer value) {
        return Arrays.stream(SubmitJumpTypeEnum.values()).filter(a -> Objects.equals(value, a.getValue())).findAny().orElse(null);
    }

    @Override
    public String getLabel() {
        return this.label;
    }
}
