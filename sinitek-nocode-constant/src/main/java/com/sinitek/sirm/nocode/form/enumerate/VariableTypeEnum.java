package com.sinitek.sirm.nocode.form.enumerate;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.sinitek.sirm.nocode.common.support.enumeratebase.BaseIntegerEnum;
import io.swagger.annotations.ApiModel;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 2025.0523
 * @since 1.0.0-SNAPSHOT
 */
@ApiModel(description = "变量类型枚举")
@Getter
public enum VariableTypeEnum implements BaseIntegerEnum {
    FIXED(0, "固定值"),
    VARIABLE(1, "变量"),
    ;
    /**
     * 值
     */
    @JsonValue
    private final Integer value;
    /**
     * 名称
     */
    private final String label;

    VariableTypeEnum(Integer value, String label) {
        this.value = value;
        this.label = label;
    }

    @JsonCreator
    public static VariableTypeEnum fromValue(Integer value) {
        return Arrays.stream(VariableTypeEnum.values()).filter(a -> Objects.equals(value, a.getValue())).findAny().orElse(null);
    }
}
