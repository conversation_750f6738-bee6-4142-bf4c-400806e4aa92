package com.sinitek.sirm.nocode.form.enumerate;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.sinitek.sirm.nocode.common.support.enumeratebase.BaseIntegerEnum;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 2025.0623
 * @since 1.0.0-SNAPSHOT
 */
@Getter
public enum AiOutWayEnum implements BaseIntegerEnum {
    streaming(0, "流式"),
    blocking(1, "完整结果"),
    ;

    /**
     * 值
     */
    @JsonValue
    private final Integer value;
    /**
     * 名称
     */
    private final String label;

    AiOutWayEnum(Integer value, String label) {
        this.value = value;
        this.label = label;
    }


    @JsonCreator
    public static AiOutWayEnum fromValue(Integer value) {
        return Arrays.stream(AiOutWayEnum.values()).filter(a -> Objects.equals(value, a.getValue())).findAny().orElse(null);
    }
}
