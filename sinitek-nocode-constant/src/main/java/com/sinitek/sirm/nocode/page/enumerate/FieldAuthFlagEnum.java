package com.sinitek.sirm.nocode.page.enumerate;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.sinitek.sirm.nocode.common.support.enumeratebase.BaseIntegerEnum;
import io.swagger.annotations.ApiModel;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 2025.0521
 * @since 1.0.0-SNAPSHOT
 */
@ApiModel(description = "字段权限枚举类型")
@Getter
public enum FieldAuthFlagEnum implements BaseIntegerEnum {
    INHERIT(0, "继承表单设计中组件的状态"),
    CUSTOM(1, "自定义"),
    ;
    /**
     * 值
     */
    @JsonValue
    private final Integer value;
    /**
     * 名称
     */
    private final String label;

    FieldAuthFlagEnum(Integer value, String label) {
        this.value = value;
        this.label = label;
    }

    @JsonCreator
    public static FieldAuthFlagEnum fromValue(Integer value) {
        return Arrays.stream(FieldAuthFlagEnum.values()).filter(a -> Objects.equals(value, a.getValue())).findAny().orElse(null);
    }
}
