package com.sinitek.sirm.nocode.llm.enumerate;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.sinitek.sirm.nocode.common.support.enumeratebase.BaseStringEnum;
import io.swagger.annotations.ApiModel;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 2025.0729
 * @since 1.0.0-SNAPSHOT
 */
@ApiModel(description = "ai数据源")
@Getter
public enum AiSourceEnum implements BaseStringEnum {
    DIFY("dify", "dify"),
    ;


    /**
     * 值
     */
    @JsonValue
    private final String value;
    /**
     * 名称
     */
    private final String label;

    AiSourceEnum(String value, String label) {
        this.value = value;
        this.label = label;

    }

    @JsonCreator
    public static AiSourceEnum fromValue(String value) {
        return Arrays.stream(AiSourceEnum.values()).filter(a -> Objects.equals(value, a.getValue())).findAny().orElse(null);
    }
}
