package com.sinitek.sirm.nocode.page.enumerate;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.sinitek.sirm.nocode.common.support.enumeratebase.BaseIntegerEnum;
import io.swagger.annotations.ApiModel;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 2025.0324
 * @since 1.0.0-SNAPSHOT
 */
@ApiModel(description = "权限成员类型")
@Getter
public enum MemberTypeEnum implements BaseIntegerEnum {
    ALL(0, "所有成员"),
    DEPARTMENT(1, "部门"),
    ROLE(2, "角色"),
    TEAM(3, "组"),
    PERSON(4, "个人");
    /**
     * 值
     */
    @JsonValue
    private final Integer value;
    /**
     * 名称
     */
    private final String label;


    MemberTypeEnum(Integer value, String label) {
        this.value = value;
        this.label = label;
    }

    public String getMemberBeanName() {
        return name().toLowerCase() + "Member";
    }


    @JsonCreator
    public static MemberTypeEnum fromValue(Integer value) {
        return Arrays.stream(MemberTypeEnum.values()).filter(a -> Objects.equals(value, a.getValue())).findAny().orElse(null);
    }
}
