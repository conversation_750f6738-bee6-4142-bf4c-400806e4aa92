package com.sinitek.sirm.nocode.page.enumerate;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.sinitek.sirm.nocode.common.support.enumeratebase.BaseIntegerEnum;
import io.swagger.annotations.ApiModel;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 2025.0417
 * @description 场景类型枚举
 * @since 1.0.0-SNAPSHOT
 */
@ApiModel(description = "场景类型枚举")
@Getter
public enum SceneTypeEnum implements BaseIntegerEnum {
    COMMON(0, "通用场景"),
    COLLECTION_FORM(1, "收集表"),
    ;
    /**
     * 值
     */
    @JsonValue
    private final Integer value;
    /**
     * 名称
     */
    private final String label;

    SceneTypeEnum(Integer value, String label) {
        this.value = value;
        this.label = label;
    }

    @JsonCreator
    public static SceneTypeEnum fromValue(Integer value) {
        return Arrays.stream(SceneTypeEnum.values()).filter(a -> Objects.equals(value, a.getValue())).findAny().orElse(null);
    }
}
