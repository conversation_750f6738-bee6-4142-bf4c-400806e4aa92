package com.sinitek.sirm.nocode.form.enumerate;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.sinitek.sirm.nocode.common.support.enumeratebase.BaseIntegerEnum;
import io.swagger.annotations.ApiModel;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 2025.0623
 * @since 1.0.0-SNAPSHOT
 */
@ApiModel(description = "AI markdown转换类型枚举")
@Getter
public enum AiMarkdownConversionTypeEnum implements BaseIntegerEnum {
    TABLE(0, "数据表格", "- 当符合用数据表格表达时，用markdown的表格表示"),
    CHART(1, "图表", "- 当符合用图表表达时，在数据表格下方增加```marmaid 包裹，并且用最合适的marmaid图表类型"),
    BRAIN_MAP(2, "脑图", "- 当符合用脑图表达时，在数据表格下方增加```marmaid 包裹，并且用mindmap图表类型表示"),
    GANTT_CHART(3, "甘特图", "- 当符合用甘特图表达时，在数据表格下方增加```marmaid 包裹，并且用gantt图表类型表示"),
    ;

    /**
     * 值
     */
    @JsonValue
    private final Integer value;
    /**
     * 名称
     */
    private final String label;

    private final String rule;

    AiMarkdownConversionTypeEnum(Integer value, String label, String rule) {
        this.value = value;
        this.label = label;
        this.rule = rule;
    }


    @JsonCreator
    public static AiMarkdownConversionTypeEnum fromValue(Integer value) {
        return Arrays.stream(AiMarkdownConversionTypeEnum.values()).filter(a -> Objects.equals(value, a.getValue())).findAny().orElse(null);
    }
}
