package com.sinitek.sirm.nocode.common.support.enumeratebase;

import com.baomidou.mybatisplus.annotation.IEnum;
import com.sinitek.sirm.nocode.common.utils.ZdStringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.Serializable;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2025.0324
 * @since 1.0.0-SNAPSHOT
 */

public interface BaseEnum<T extends Serializable> extends IEnum<T> {
    Logger log = LoggerFactory.getLogger(BaseEnum.class);

    /**
     * 获取枚举名称
     *
     * @return 枚举名称
     */
    String getLabel();

    /**
     * 获取枚举描述
     *
     * @return 枚举描述
     */
    default String getDesc() {
        return null;
    }

    /**
     * 获取枚举类型,默认是0
     *
     * @return 枚举类型
     */
    default int getType() {
        return 0;
    }

    /**
     * 获取枚举类型,默认是0
     *
     * @return 枚举类型
     */
    default int findTypeWithTarget(int targetType) {
        return getType();
    }

    /**
     * 是否启用
     *
     * @return 是否启用
     */
    default boolean isEnable() {
        return true;
    }

    /**
     * 获取成员bean名称
     *
     * @return bean的名称
     */
    @SuppressWarnings("unchecked")
    default <E extends Enum<E>> String beanName(String suffix) {
        return ZdStringUtils.underlineToCamel(((E) this).name()) + suffix;
    }

    /**
     * 组装map
     *
     * @param cClass 子类
     * @param <T>    value泛型
     * @param <C>    children 泛型
     * @return map
     */
     static <T extends Serializable, C extends BaseEnum<T>> Map<T, C> map(Class<C> cClass) {
        Map<T, C> map = new LinkedHashMap<>();
        if (cClass.isEnum()) {
            C[] enumConstants = cClass.getEnumConstants();
            for (C c : enumConstants) {
                map.put(c.getValue(), c);
            }
        }
        return map;
    }

    /**
     * 组装list
     *
     * @param cClass 枚举类
     * @param <T>    value泛型
     * @param <C>    children 泛型
     * @return list
     */
     static <T extends Serializable, C extends BaseEnum<T>> List<C> list(Class<C> cClass, int type) {
        return BaseEnum.map(cClass).values().stream().filter(e -> e.findTypeWithTarget(type) == type && e.isEnable()).collect(Collectors.toList());
    }
}
