package com.sinitek.sirm.nocode.form.enumerate;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.sinitek.sirm.nocode.common.support.enumeratebase.BaseStringEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * 页面数据组件类型枚举
 *
 * <AUTHOR>
 * @version 2025.0609
 * @since 1.0.0-SNAPSHOT
 */
@ApiModel(description = "页面数据组件类型")
@Getter
public enum PageDataComponentTypeEnum implements BaseStringEnum {
    ZD_PAGE("ZDPage", "页面", null, false, false),
    ZD_FORM("ZDForm", "表单", null, false, false),
    ZD_INPUT("ZDInput", "输入框", null, true, false),
    ZD_TEXTAREA("ZDTextarea", "多行文本", null, true, false),
    ZD_NUMBER("ZDNumber", "数值", null, true, false),
    ZD_RADIO("ZDRadio", "单选", null, true, false),
    ZD_CHECKBOX("ZDCheckbox", "多选", List.class, true, false),
    ZD_DATE("ZDDate", "日期", null, true, false),
    ZD_DATE_RANGE("ZDDateRange", "日期区间", null, true, false),
    ZD_SELECT_SINGLE("ZDSelectSingle", "下拉框-单选", null, true, false),
    ZD_SELECT_MULTIPLE("ZDSelectMultiple", "下拉框-多选", List.class, true, false),
    ZD_EMPLOYEE("ZDEmployee", "组织结构", null, true, false),
    ZD_DEPARTMENT("ZDDepartment", "部门", null, true, false),
    ZD_UPLOAD("ZDUpload", "附件", null, true, false),
    ZD_ASSOCIATION("ZDAssociation", "关联表单", null, true, true),
    ZD_CHILD_FORM("ZDChildForm", "子表单", null, false, false),
    ZD_GRID("ZDGrid", "容器布局", null, false, true),
    ZD_GRID_ITEM("ZDGridItem", "容器子布局", null, false, true),
    ;

    /**
     * 值
     */
    @JsonValue
    @ApiModelProperty("值")
    private final String value;

    /**
     * 名称
     */
    @ApiModelProperty("中文名称")
    private final String label;

    @ApiModelProperty("数据库字段类型")
    private final Class<?> fieldType;

    @ApiModelProperty("是不是基础组件")
    private final boolean baseComponent;

    @ApiModelProperty("是不是布局组件")
    private final boolean layout;

    PageDataComponentTypeEnum(String value, String label, Class<?> fieldType, boolean baseComponent, boolean layout) {
        this.value = value;
        this.label = label;
        this.fieldType = fieldType;
        this.baseComponent = baseComponent;
        this.layout = layout;
    }

    @JsonCreator
    public static PageDataComponentTypeEnum fromValue(String value) {
        return Arrays.stream(PageDataComponentTypeEnum.values())
                .filter(a -> Objects.equals(value, a.getValue()))
                .findAny()
                .orElse(null);
    }

    /**
     * 从ref中获取枚举
     *
     * @param key ref
     * @return 枚举
     */
    public static PageDataComponentTypeEnum formKey(String key) {
        if (Objects.isNull(key)) {
            return null;
        }
        if (key.contains(".")) {
            key = key.substring(key.lastIndexOf(".") + 1);
        }
        return fromValue(key.split("_")[0]);
    }
}
