package com.sinitek.sirm.nocode.page.enumerate;

import cn.hutool.core.text.CharSequenceUtil;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.sinitek.sirm.nocode.common.support.enumeratebase.BaseIntegerEnum;
import io.swagger.annotations.ApiModel;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 2025.0417
 * @description 场景重复类型枚举
 * @since 1.0.0-SNAPSHOT
 */
@ApiModel(description = "场景重复类型枚举")
@Getter
public enum SceneRepeatTypeEnum implements BaseIntegerEnum {
    DATE(0, "每天"),
    WEEK(1, "每周"),
    MONTH(2, "每月"),
    ;
    /**
     * 值
     */
    @JsonValue
    private final Integer value;
    /**
     * 名称
     */
    private final String label;

    SceneRepeatTypeEnum(Integer value, String label) {
        this.value = value;
        this.label = label;
    }

    @JsonCreator
    public static SceneRepeatTypeEnum fromValue(Integer value) {
        return Arrays.stream(SceneRepeatTypeEnum.values()).filter(a -> Objects.equals(value, a.getValue())).findAny().orElse(null);
    }

    /**
     * 获取成员bean名称
     *
     * @return bean的名称
     */
    @Override
    public String beanName(String suffix) {
        return "zd" + CharSequenceUtil.upperFirst(this.name().toLowerCase()) + "Creator";
    }
}
