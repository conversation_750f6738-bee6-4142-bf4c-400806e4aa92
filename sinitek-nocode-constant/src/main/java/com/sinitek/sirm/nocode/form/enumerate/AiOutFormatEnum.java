package com.sinitek.sirm.nocode.form.enumerate;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.sinitek.sirm.nocode.common.support.enumeratebase.BaseIntegerEnum;
import io.swagger.annotations.ApiModel;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 2025.0623
 * @since 1.0.0-SNAPSHOT
 */
@ApiModel(description = "AI输出格式")
@Getter
public enum AiOutFormatEnum implements BaseIntegerEnum {
    TEXT(0, "默认"),
    MARKDOWN(1, "markdown"),
    PIC(2, "图片"),
    ;

    /**
     * 值
     */
    @JsonValue
    private final Integer value;
    /**
     * 名称
     */
    private final String label;

    AiOutFormatEnum(Integer value, String label) {
        this.value = value;
        this.label = label;
    }


    @JsonCreator
    public static AiOutFormatEnum fromValue(Integer value) {
        return Arrays.stream(AiOutFormatEnum.values()).filter(a -> Objects.equals(value, a.getValue())).findAny().orElse(null);
    }
}
