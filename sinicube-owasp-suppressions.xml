<?xml version="1.0" encoding="UTF-8"?>
<suppressions xmlns="https://jeremylong.github.io/DependencyCheck/dependency-suppression.1.3.xsd">

    <suppress>
        <notes><![CDATA[
   file name: mybatis-plus-boot-starter-3.4.1.jar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/com\.baomidou/mybatis\-plus\-boot\-starter@.*$</packageUrl>
        <cve>CVE-2020-26945</cve>
    </suppress>

    <suppress>
        <notes><![CDATA[
      file name: mybatis-plus-3.4.1.jar
      ]]></notes>
        <packageUrl regex="true">^pkg:maven/com\.baomidou/mybatis\-plus@.*$</packageUrl>
        <vulnerabilityName>CVE-2022-25517</vulnerabilityName>
    </suppress>

    <suppress>
        <notes><![CDATA[
   file name: mybatis-plus-3.4.1.jar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/com\.baomidou/mybatis\-plus@.*$</packageUrl>
        <cve>CVE-2020-26945</cve>
    </suppress>

    <suppress>
        <notes><![CDATA[
   file name: mybatis-plus-core-3.4.1.jar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/com\.baomidou/mybatis\-plus\-core@.*$</packageUrl>
        <cve>CVE-2020-26945</cve>
    </suppress>

    <suppress>
        <notes><![CDATA[
   file name: mybatis-plus-extension-3.4.1.jar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/com\.baomidou/mybatis\-plus\-extension@.*$</packageUrl>
        <cve>CVE-2020-26945</cve>
    </suppress>

    <suppress>
        <notes><![CDATA[
   file name: log4j-core-2.17.1.jar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/org\.apache\.logging\.log4j/log4j\-core@.*$</packageUrl>
        <cve>CVE-2022-33915</cve>
    </suppress>

    <suppress>
        <notes><![CDATA[
   file name: mybatis-plus-annotation-3.4.1.jar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/com\.baomidou/mybatis\-plus\-annotation@.*$</packageUrl>
        <cve>CVE-2020-26945</cve>
    </suppress>

    <suppress>
        <notes><![CDATA[
   file name: log4j-api-2.17.1.jar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/org\.apache\.logging\.log4j/log4j\-api@.*$</packageUrl>
        <cve>CVE-2022-33915</cve>
    </suppress>

    <suppress>
        <notes><![CDATA[
   file name: log4j-web-2.17.1.jar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/org\.apache\.logging\.log4j/log4j\-web@.*$</packageUrl>
        <cve>CVE-2022-33915</cve>
    </suppress>

    <suppress>
        <notes><![CDATA[
   file name: spring-web-5.2.20.RELEASE.jar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/org\.springframework/spring\-web@.*$</packageUrl>
        <cve>CVE-2016-1000027</cve>
    </suppress>

    <suppress>
        <notes><![CDATA[
   file name: spring-boot-2.3.4.RELEASE.jar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/org\.springframework\.boot/spring\-boot@.*$</packageUrl>
        <vulnerabilityName>CVE-2022-27772</vulnerabilityName>
    </suppress>

    <suppress>
        <notes><![CDATA[
   file name: mysql-connector-java-8.0.21.jar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/mysql/mysql\-connector\-java@.*$</packageUrl>
        <vulnerabilityName>CVE-2022-21363</vulnerabilityName>
    </suppress>

    <suppress>
        <notes><![CDATA[
   file name: apm-toolkit-log4j-2.x-6.2.0.jar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/org\.apache\.skywalking/apm\-toolkit\-log4j\-2\.x@.*$</packageUrl>
        <cve>CVE-2020-9483</cve>
    </suppress>

    <suppress>
        <notes><![CDATA[
   file name: apm-toolkit-log4j-2.x-6.2.0.jar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/org\.apache\.skywalking/apm\-toolkit\-log4j\-2\.x@.*$</packageUrl>
        <cve>CVE-2022-33915</cve>
    </suppress>

    <suppress>
        <notes><![CDATA[
   file name: apm-toolkit-trace-6.2.0.jar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/org\.apache\.skywalking/apm\-toolkit\-trace@.*$</packageUrl>
        <cve>CVE-2020-9483</cve>
    </suppress>

    <suppress>
        <notes><![CDATA[
   file name: apm-toolkit-opentracing-6.2.0.jar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/org\.apache\.skywalking/apm\-toolkit\-opentracing@.*$</packageUrl>
        <cve>CVE-2020-9483</cve>
    </suppress>

    <suppress>
        <notes><![CDATA[
   file name: commons-httpclient-3.1.jar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/commons\-httpclient/commons\-httpclient@.*$</packageUrl>
        <cve>CVE-2012-5783</cve>
    </suppress>

    <suppress>
        <notes><![CDATA[
   file name: spring-cloud-netflix-ribbon-2.2.9.RELEASE.jar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/org\.springframework\.cloud/spring\-cloud\-netflix\-ribbon@.*$</packageUrl>
        <cve>CVE-2021-22053</cve>
    </suppress>

    <suppress>
        <notes><![CDATA[
   file name: spring-cloud-netflix-archaius-2.2.9.RELEASE.jar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/org\.springframework\.cloud/spring\-cloud\-netflix\-archaius@.*$
        </packageUrl>
        <cve>CVE-2021-22053</cve>
    </suppress>

    <suppress>
        <notes><![CDATA[
   file name: spring-security-crypto-5.3.9.RELEASE.jar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/org\.springframework\.security/spring\-security\-crypto@.*$</packageUrl>
        <cve>CVE-2022-22978</cve>
    </suppress>

    <suppress>
        <notes><![CDATA[
   file name: spring-security-crypto-5.3.9.RELEASE.jar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/org\.springframework\.security/spring\-security\-crypto@.*$</packageUrl>
        <cve>CVE-2021-22119</cve>
    </suppress>

    <suppress>
        <notes><![CDATA[
   file name: spring-security-crypto-5.3.9.RELEASE.jar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/org\.springframework\.security/spring\-security\-crypto@.*$</packageUrl>
        <cve>CVE-2022-22976</cve>
    </suppress>

    <suppress>
        <notes><![CDATA[
   file name: spring-security-crypto-5.3.9.RELEASE.jar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/org\.springframework\.security/spring\-security\-crypto@.*$</packageUrl>
        <vulnerabilityName>CVE-2020-5408</vulnerabilityName>
    </suppress>

    <suppress>
        <notes><![CDATA[
   file name: jackson-mapper-asl-1.9.13.jar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/org\.codehaus\.jackson/jackson\-mapper\-asl@.*$</packageUrl>
        <vulnerabilityName>CVE-2017-7525</vulnerabilityName>
    </suppress>

    <suppress>
        <notes><![CDATA[
   file name: okhttp-3.14.9.jar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/com\.squareup\.okhttp3/okhttp@.*$</packageUrl>
        <vulnerabilityName>CVE-2021-0341</vulnerabilityName>
    </suppress>

    <suppress>
        <notes><![CDATA[
   file name: bcprov-jdk15on-1.66.jar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/org\.bouncycastle/bcprov\-jdk15on@.*$</packageUrl>
        <vulnerabilityName>CVE-2020-0187</vulnerabilityName>
    </suppress>

    <suppress>
        <notes><![CDATA[
   file name: simple-xml-2.7.1.jar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/org\.simpleframework/simple\-xml@.*$</packageUrl>
        <vulnerabilityName>CVE-2017-14868</vulnerabilityName>
    </suppress>

    <suppress>
        <notes><![CDATA[
   file name: jakarta.annotation-api-1.3.5.jar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/jakarta\.annotation/jakarta\.annotation\-api@.*$</packageUrl>
        <cve>CVE-2022-31569</cve>
    </suppress>

    <suppress>
        <notes><![CDATA[
   file name: spring-cloud-commons-2.2.9.RELEASE.jar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/org\.springframework\.cloud/spring\-cloud\-commons@.*$</packageUrl>
        <cve>CVE-2022-31569</cve>
    </suppress>

    <suppress>
        <notes><![CDATA[
   file name: spring-security-rsa-1.0.9.RELEASE.jar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/org\.springframework\.security/spring\-security\-rsa@.*$</packageUrl>
        <cve>CVE-2022-31569</cve>
    </suppress>

    <suppress>
        <notes><![CDATA[
   file name: spring-cloud-netflix-hystrix-2.2.9.RELEASE.jar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/org\.springframework\.cloud/spring\-cloud\-netflix\-hystrix@.*$</packageUrl>
        <cve>CVE-2021-22053</cve>
    </suppress>

    <suppress>
        <notes><![CDATA[
   file name: spring-cloud-starter-netflix-hystrix-2.2.9.RELEASE.jar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/org\.springframework\.cloud/spring\-cloud\-starter\-netflix\-hystrix@.*$
        </packageUrl>
        <cve>CVE-2021-22053</cve>
    </suppress>

    <suppress>
        <notes><![CDATA[
   file name: spring-cloud-starter-netflix-archaius-2.2.9.RELEASE.jar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/org\.springframework\.cloud/spring\-cloud\-starter\-netflix\-archaius@.*$
        </packageUrl>
        <cve>CVE-2021-22053</cve>
    </suppress>

    <suppress>
        <notes><![CDATA[
   file name: seata-all-1.4.2.jar: druid.jar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/com\.alibaba/druid@.*$</packageUrl>
        <vulnerabilityName>CVE-2021-33800</vulnerabilityName>
    </suppress>

    <suppress>
        <notes><![CDATA[
   file name: junit-platform-engine-1.6.3.jar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/org\.junit\.platform/junit\-platform\-engine@.*$</packageUrl>
        <cve>CVE-2022-31514</cve>
    </suppress>

    <suppress>
        <notes><![CDATA[
   file name: junit-platform-commons-1.6.3.jar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/org\.junit\.platform/junit\-platform\-commons@.*$</packageUrl>
        <cve>CVE-2022-31514</cve>
    </suppress>

    <suppress>
        <notes><![CDATA[
   file name: com.sinitek.sinicube:sinitek-sinicube-attachment-by-minio:7.4.0-SNAPSHOT
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/com\.sinitek\.sinicube/sinitek\-sinicube\-attachment\-by\-minio@.*$
        </packageUrl>
        <cpe>cpe:/a:minio:minio</cpe>
    </suppress>

    <suppress>
        <notes><![CDATA[
   file name: spring-cloud-starter-netflix-ribbon-2.2.9.RELEASE.jar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/org\.springframework\.cloud/spring\-cloud\-starter\-netflix\-ribbon@.*$
        </packageUrl>
        <cve>CVE-2021-22053</cve>
    </suppress>

    <!-- snakeyaml作者认为是NVD的误报,暂按误报处理  -->
    <suppress>
        <notes><![CDATA[
   file name: snakeyaml-1.31.jar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/org\.yaml/snakeyaml@.*$</packageUrl>
        <vulnerabilityName>CVE-2022-38752</vulnerabilityName>
    </suppress>

    <suppress>
        <notes><![CDATA[
   file name: snakeyaml-1.31.jar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/org\.yaml/snakeyaml@.*$</packageUrl>
        <vulnerabilityName>CVE-2022-38751</vulnerabilityName>
    </suppress>

    <suppress>
        <notes><![CDATA[
   file name: commons-*.jar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/commons\-.*/commons\-.*@.*$</packageUrl>
        <cve>CVE-2021-37533</cve>
    </suppress>

    <suppress>
        <notes><![CDATA[
   file name: org-commons-*.jar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/org\.apache\.commons/commons\-.*@.*$</packageUrl>
        <cve>CVE-2021-37533</cve>
    </suppress>

    <suppress>
        <notes><![CDATA[
   file name: xml-apis-1.4.01.jar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/xml\-apis/xml\-apis@.*$</packageUrl>
        <cve>CVE-2021-37533</cve>
    </suppress>

    <suppress>
        <notes><![CDATA[
   file name: snakeyaml-1.33.jar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/org\.yaml/snakeyaml@.*$</packageUrl>
        <vulnerabilityName>CVE-2022-1471</vulnerabilityName>
    </suppress>

    <suppress>
        <notes><![CDATA[
   file name: jcl-over-slf4j-1.7.30.jar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/org\.slf4j/jcl\-over\-slf4j@.*$</packageUrl>
        <cve>CVE-2021-37533</cve>
    </suppress>

    <suppress>
        <notes><![CDATA[
   file name: junit-jupiter-engine-5.8.1.jar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/org\.junit\.jupiter/junit\-jupiter\-engine@.*$</packageUrl>
        <cve>CVE-2022-31514</cve>
    </suppress>

    <suppress>
        <notes><![CDATA[
   file name: sinitek-sinicube-utils-7.5.0-SNAPSHOT.jar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/com\.sinitek\.sinicube/sinitek\-sinicube\-utils@.*$</packageUrl>
        <cve>CVE-2021-4277</cve>
    </suppress>

    <suppress>
        <notes><![CDATA[
   file name: UserAgentUtils-1.20.jar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/eu\.bitwalker/UserAgentUtils@.*$</packageUrl>
        <cve>CVE-2021-4277</cve>
    </suppress>

    <suppress>
        <notes><![CDATA[
   file name: snakeyaml-1.33.jar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/org\.yaml/snakeyaml@.*$</packageUrl>
        <cve>CVE-2022-3064</cve>
    </suppress>

    <suppress>
        <notes><![CDATA[
   file name: snakeyaml-1.33.jar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/org\.yaml/snakeyaml@.*$</packageUrl>
        <cve>CVE-2021-4235</cve>
    </suppress>

    <suppress>
        <notes><![CDATA[
   file name: reactor-netty-0.9.20.RELEASE.jar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/io\.projectreactor\.netty/reactor\-netty@.*$</packageUrl>
        <vulnerabilityName>CVE-2022-31684</vulnerabilityName>
    </suppress>

    <suppress>
        <notes><![CDATA[
   file name: spring-cloud-gateway-server-2.2.10.RELEASE.jar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/org\.springframework\.cloud/spring\-cloud\-gateway\-server@.*$</packageUrl>
        <cve>CVE-2022-22947</cve>
    </suppress>

    <suppress>
        <notes><![CDATA[
   file name: spring-cloud-starter-gateway-2.2.10.RELEASE.jar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/org\.springframework\.cloud/spring\-cloud\-starter\-gateway@.*$</packageUrl>
        <cve>CVE-2022-22947</cve>
    </suppress>

    <suppress>
        <notes><![CDATA[
   file name: jackson-core-2.14.0-rc1.jar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/com\.fasterxml\.jackson\.core/jackson\-core@.*$</packageUrl>
        <cve>CVE-2022-45688</cve>
    </suppress>

    <suppress>
        <notes><![CDATA[
   file name: guava-30.0-jre.jar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/com\.google\.guava/guava@.*$</packageUrl>
        <vulnerabilityName>CVE-2020-8908</vulnerabilityName>
    </suppress>

    <suppress>
        <notes><![CDATA[
   file name: android-json-0.0.20131108.vaadin1.jar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/com\.vaadin\.external\.google/android\-json@.*$</packageUrl>
        <cve>CVE-2022-45688</cve>
    </suppress>

    <suppress>
        <notes><![CDATA[
   file name: json-path-2.4.0.jar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/com\.jayway\.jsonpath/json\-path@.*$</packageUrl>
        <cve>CVE-2022-45688</cve>
    </suppress>

    <suppress>
        <notes><![CDATA[
   file name: json-smart-2.4.8.jar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/net\.minidev/json\-smart@.*$</packageUrl>
        <cve>CVE-2022-45688</cve>
    </suppress>

    <suppress>
        <notes><![CDATA[
   file name: mybatis-plus-3.4.1.jar
      问题说明: 在mybatis-plus-3.5.3.1及之前的版本（等于全部版本都有该问题），多租户的tenant_id为直接拼接，当外部可传入tenant_id的情况下存在SQL注入风险
      漏洞解析说明: https://github.com/FCncdn/MybatisPlusTenantPluginSQLInjection-POC/blob/master/Readme.md
      解决方案: 考虑到全部版本有该问题（国家信息安全漏洞库的描述为翻译错误，并不是3.5.3.1之前才存在问题，最新的3.5.3.1版本也存在该问题并且mybatis-plus团队暂时不考虑修复），
        框架在com.sinitek.data.mybatis.tenant.SinicubeTenantHandler.getTenantId中对tenantId进行合法性验证，合法的tenant_id才能执行
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/com\.baomidou/mybatis\-plus@.*$</packageUrl>
        <cve>CVE-2023-25330</cve>
    </suppress>

    <suppress>
        <notes><![CDATA[
   file name: mybatis-plus-extension-3.4.1.jar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/com\.baomidou/mybatis\-plus\-extension@.*$</packageUrl>
        <cve>CVE-2023-25330</cve>
    </suppress>

    <suppress>
        <notes><![CDATA[
   file name: mybatis-plus-annotation-3.4.1.jar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/com\.baomidou/mybatis\-plus\-annotation@.*$</packageUrl>
        <cve>CVE-2023-25330</cve>
    </suppress>

    <suppress>
        <notes><![CDATA[
   file name: mybatis-plus-core-3.4.1.jar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/com\.baomidou/mybatis\-plus\-core@.*$</packageUrl>
        <cve>CVE-2023-25330</cve>
    </suppress>

    <suppress>
        <notes><![CDATA[
   file name: mybatis-plus-boot-starter-3.4.1.jar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/com\.baomidou/mybatis\-plus\-boot\-starter@.*$</packageUrl>
        <cve>CVE-2023-25330</cve>
    </suppress>

    <suppress>
        <notes><![CDATA[
   file name: snakeyaml-1.33.jar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/org\.yaml/snakeyaml@.*$</packageUrl>
        <cve>CVE-2023-2251</cve>
    </suppress>

    <suppress>
        <notes><![CDATA[
   file name: spring-boot-starter-2.3.12.RELEASE.jar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/org\.springframework\.boot/spring\-.*@.*$</packageUrl>
        <cve>CVE-2023-20883</cve>
    </suppress>

    <suppress>
        <notes><![CDATA[
   file name: spring-boot-2.3.12.RELEASE.jar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/org\.springframework\.boot/spring\-boot@.*$</packageUrl>
        <cve>CVE-2023-20883</cve>
    </suppress>

    <suppress>
        <notes><![CDATA[
   file name: spring-boot-starter-web-2.3.12.RELEASE.jar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/org\.springframework\.boot/spring\-boot\-starter\-web@.*$</packageUrl>
        <cve>CVE-2023-20883</cve>
    </suppress>

    <suppress>
        <notes><![CDATA[
   file name: okhttp-3.14.9.jar
   issue: https://github.com/square/okhttp/issues/7738
   原因: 描述上只有com.squareup.okhttp3:okhttp-brotli存在问题，目前没有依赖该包
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/com\.squareup\.okhttp3/okhttp@.*$</packageUrl>
        <cve>CVE-2023-3782</cve>
    </suppress>

    <suppress>
        <notes><![CDATA[
   file name: spring-boot-2.3.12.RELEASE.jar
   issue: https://spring.io/security/cve-2023-20873
   原因: 漏洞提出的条件都不满足
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/org\.springframework\.boot/spring\-boot@.*$</packageUrl>
        <cve>CVE-2023-20873</cve>
    </suppress>

    <suppress>
        <notes><![CDATA[
   file name: spring-boot-autoconfigure-2.3.12.RELEASE.jar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/org\.springframework\.boot/spring\-boot\-.*@.*$</packageUrl>
        <cve>CVE-2023-20873</cve>
    </suppress>

    <suppress>
        <notes><![CDATA[
   file name: netty-handler-4.1.94.Final.jar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/io\.netty/netty\-handler@.*$</packageUrl>
        <vulnerabilityName>CVE-2023-4586</vulnerabilityName>
    </suppress>

    <suppress>
        <notes><![CDATA[
   file name: quartz-2.3.2.jar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/org\.quartz\-scheduler/quartz@.*$</packageUrl>
        <cve>CVE-2023-39017</cve>
    </suppress>

    <suppress>
        <notes><![CDATA[
   file name: jackson-databind-2.14.0-rc1.jar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/com\.fasterxml\.jackson\.core/jackson\-databind@.*$</packageUrl>
        <cve>CVE-2023-35116</cve>
    </suppress>

    <suppress>
        <notes><![CDATA[
   file name: okhttp-3.14.9.jar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/com\.squareup\.okhttp3/okhttp@.*$</packageUrl>
        <cve>CVE-2023-0833</cve>
    </suppress>

    <suppress>
        <notes><![CDATA[
   file name: netty-transport-4.1.99.Final.jar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/io\.netty/netty\-.*@.*$</packageUrl>
        <cve>CVE-2023-4586</cve>
    </suppress>

    <suppress>
        <notes><![CDATA[
   file name: jackson-core-2.14.0-rc1.jar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/com\.fasterxml\.jackson\.core/jackson\-core@.*$</packageUrl>
        <cve>CVE-2023-5072</cve>
    </suppress>

    <suppress>
        <notes><![CDATA[
   file name: dom4j-2.1.3.jar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/org\.dom4j/dom4j@.*$</packageUrl>
        <cve>CVE-2023-45960</cve>
    </suppress>

    <suppress>
        <notes><![CDATA[
   file name: reactor-netty-0.9.20.RELEASE.jar
   该包只有gateway使用,同时该漏洞只有和Micrometer集成情况下才可能触发,我们不会使用Micrometer不满足触发条件。
   同时该依赖升级会导致gateway无法启动,暂不处理
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/io\.projectreactor\.netty/reactor\-netty@.*$</packageUrl>
        <vulnerabilityName>CVE-2023-34054</vulnerabilityName>
    </suppress>

    <suppress>
        <notes><![CDATA[
   file name: elasticsearch-7.17.14.jar
   该漏洞是说嗅探引擎搜索接口，没有遵循DLS或者FLS,导致配置了DLS或者FLS的已经授权的用户，调用API的时候，可能无法获得对文档的访问授权，推荐修复方案是升级kibana的版本到8.12.1，jar包这边不处理
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/org\.elasticsearch/elasticsearch@.*$</packageUrl>
        <vulnerabilityName>CVE-2024-23446</vulnerabilityName>
    </suppress>

    <suppress>
        <notes><![CDATA[
   file name: spring-web-5.2.24.RELEASE.jar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/org\.springframework/spring\-web@.*$</packageUrl>
        <vulnerabilityName>CVE-2024-22243</vulnerabilityName>
    </suppress>

    <suppress>
        <notes><![CDATA[
   误报,官方的说法是2.0以上的才存在该漏洞。说明: https://github.com/advisories/GHSA-xjp4-hw94-mvp5
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/commons\-configuration/commons\-configuration@.*$</packageUrl>
        <vulnerabilityName>CVE-2024-29131</vulnerabilityName>
    </suppress>

    <suppress>
        <notes><![CDATA[
   file name: commons-configuration-1.8.jar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/commons\-configuration/commons\-configuration@.*$</packageUrl>
        <vulnerabilityName>CVE-2024-29133</vulnerabilityName>
    </suppress>

    <suppress>
        <notes><![CDATA[
   file name: jjwt-api-0.12.5.jar。显示有漏洞的json-java库
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/io\.jsonwebtoken/jjwt\-api@.*$</packageUrl>
        <cve>CVE-2023-5072</cve>
    </suppress>

    <suppress>
        <notes><![CDATA[
   file name: jjwt-api-0.12.5.jar。显示有漏洞的hutool-json库
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/io\.jsonwebtoken/jjwt\-api@.*$</packageUrl>
        <cve>CVE-2022-45688</cve>
    </suppress>

    <suppress>
        <notes><![CDATA[
   file name: jjwt-impl-0.12.5.jar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/io\.jsonwebtoken/jjwt\-.*@.*$</packageUrl>
        <cve>CVE-2022-45688</cve>
    </suppress>

    <suppress>
        <notes><![CDATA[
   file name: jjwt-impl-0.12.5.jar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/io\.jsonwebtoken/jjwt\-.*@.*$</packageUrl>
        <cve>CVE-2023-5072</cve>
    </suppress>

    <suppress>
        <notes><![CDATA[
   file name: reactor-netty-core-1.0.32.jar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/io\.projectreactor\.netty/reactor\-netty\-core@.*$</packageUrl>
        <cve>CVE-2023-34054</cve>
    </suppress>

    <suppress>
        <notes><![CDATA[
   file name: lucene-core-8.11.3.jar
   Lucene Replicator模块才存在该漏洞，框架未依赖该模块
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/org\.apache\.lucene/lucene\-.*@.*$</packageUrl>
        <cve>CVE-2024-45772</cve>
    </suppress>

    <suppress>
        <notes><![CDATA[
   file name: spring-webflux-5.3.39.jar
   Spring5.3已停止维护，无法升级处理，该漏洞在使用tomcat部署时无法生效，如此误报
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/org\.springframework/spring\-.*@.*$</packageUrl>
        <vulnerabilityName>CVE-2024-38816</vulnerabilityName>
    </suppress>


    <suppress>
        <notes><![CDATA[
   file name: spring-core-5.3.39.jar
   Spring5.3已停止维护，无法升级处理，该漏洞危险程度低，如加入误报
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/org\.springframework/spring\-.*@.*$</packageUrl>
        <cve>CVE-2024-38820</cve>
    </suppress>

    <suppress>
        <notes><![CDATA[
   file name: spring-web-5.3.39.jar
   Spring5.3已停止维护，无法升级处理，该漏洞危险程度较低，如加入误报
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/org\.springframework/spring\-.*@.*$</packageUrl>
        <vulnerabilityName>CVE-2024-38828</vulnerabilityName>
    </suppress>

    <suppress>
        <notes><![CDATA[
   file name: spring-security-crypto-5.6.12.jar
   1、Spring5.3已停止维护，无法升级处理。 2、系统中不存在使用BCryptPasswordEncoder.matches方法，故此该漏洞也不存在。 综合两个原因加入误报
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/org\.springframework\.security/spring\-security\-crypto@.*$</packageUrl>
        <vulnerabilityName>CVE-2025-22228</vulnerabilityName>
    </suppress>

    <suppress>
        <notes><![CDATA[
   file name: spring-context-5.3.39.jar
   1、Spring5.3已停止维护，无法升级处理。 2、漏洞危害等级低，也较难触发。 综合两个原因加入误报
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/org\.springframework/spring\-context@.*$</packageUrl>
        <vulnerabilityName>CVE-2025-22233</vulnerabilityName>
    </suppress>

    <suppress>
        <notes><![CDATA[
   file name: mxparser-1.2.2.jar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/io\.github\.x\-stream/mxparser@.*$</packageUrl>
        <cpe>cpe:/a:xstream:xstream</cpe>
    </suppress>


    <suppress>
        <notes><![CDATA[
   file name: spring-web-5.3.39.jar
   1、该漏洞需要程序设置Content-Disposition响应头同时filename是取用户输入内容才满足漏洞的前提条件，
   我们系统中只有HttpUtils下载工具类使用到了Content-Disposition响应头，但fileName都是程序设置（比如附件表名称、后端配置文件中的名称），所以不满足漏洞前提
   2、Spring5.3已停止维护，无法升级处理
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/org\.springframework/spring\-web@.*$</packageUrl>
        <vulnerabilityName>CVE-2025-41234</vulnerabilityName>
    </suppress>

    <suppress>
        <notes><![CDATA[
   file name: mysql-connector-j-8.4.0.jar
   该漏洞是MySQL Server端的漏洞，而不是java版本的mysql客户端漏洞
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/com\.mysql/mysql\-connector\-j@.*$</packageUrl>
        <cpe>cpe:/a:oracle:mysql</cpe>
    </suppress>

    <suppress>
        <notes><![CDATA[
   file name: ojdbc-6.jar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/oracle\.jdbc\.OracleDriver/ojdbc@.*$</packageUrl>
        <cpe>cpe:/a:oracle:jdk</cpe>
    </suppress>

    <suppress>
        <notes><![CDATA[
   file name: txw2-2.3.8.jar
   ]]></notes>
        <packageUrl regex="true">^pkg:maven/org\.glassfish\.jaxb/txw2@.*$</packageUrl>
        <cve>CVE-2024-9329</cve>
    </suppress>
</suppressions>