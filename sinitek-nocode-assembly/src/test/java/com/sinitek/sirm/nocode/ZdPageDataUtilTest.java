package com.sinitek.sirm.nocode;

import com.sinitek.sirm.common.utils.JsonUtil;
import com.sinitek.sirm.nocode.form.dto.ZdImportFormHeaderFieldDTO;
import com.sinitek.sirm.nocode.form.dto.ZdParseResultDTO;
import com.sinitek.sirm.nocode.form.util.ZdPageDataUtil;
import com.sinitek.sirm.nocode.test.tool.ZdUnitTestBase;
import com.sinitek.sirm.nocode.test.tool.util.ZdTestFileUtil;
import java.util.List;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 * @date 2025-07-16 16:16
 */
public class ZdPageDataUtilTest extends ZdUnitTestBase {

    @Test
    public void testParsePageData() {
        String pageData = ZdTestFileUtil.readTxtFile(
            "json/test_parse_page_json/all_component.json");
        ZdParseResultDTO result = ZdPageDataUtil.parsePageData(
            JsonUtil.toMap(pageData));
        List<ZdImportFormHeaderFieldDTO> fields = result.getHeadFields();
        for (ZdImportFormHeaderFieldDTO field : fields) {
            System.out.println(JsonUtil.toJsonString(field));
        }
        this.shouldHasSize(fields, 11 + 4 + 1);
    }

    @Test
    @DisplayName("测试简单子表单解析")
    public void testParseSimpleChildFormPageData() {
        String pageData = ZdTestFileUtil.readTxtFile(
            "json/test_parse_page_data/test_parse_simple_child_form.json");
        ZdParseResultDTO result = ZdPageDataUtil.parsePageData(
            JsonUtil.toMap(pageData));
        List<ZdImportFormHeaderFieldDTO> fields = result.getHeadFields();
        for (ZdImportFormHeaderFieldDTO field : fields) {
            System.out.println(JsonUtil.toJsonString(field));
        }

        this.shouldHasSize(fields, 12);

        for (int i = 0; i < fields.size(); i++) {
            ZdImportFormHeaderFieldDTO field = fields.get(i);
            if (i == 0) {
                this.shouldBeEquals(field.getRef(), "id");
                this.shouldBeEquals(field.getParentRef(), null);
                this.shouldBeEquals(field.getComponentName(), null);
                this.shouldBeEquals(field.getChildFormItemFlag(), false);
            }
            if (i == 1) {
                this.shouldBeEquals(field.getRef(), "ZDInput_4522");
                this.shouldBeEquals(field.getParentRef(), null);
                this.shouldBeEquals(field.getComponentName(), "ZDInput");
                this.shouldBeEquals(field.getChildFormItemFlag(), false);
            }
            if (i == 2) {
                this.shouldBeEquals(field.getRef(), "id");
                this.shouldBeEquals(field.getParentRef(), "ZDChildForm_6533");
                this.shouldBeEquals(field.getParentLabel(), "子表单");
                this.shouldBeEquals(field.getChildFormItemFlag(), true);
            }
            if (i == 3) {
                this.shouldBeEquals(field.getRef(), "ZDInput_5344");
                this.shouldBeEquals(field.getParentRef(), "ZDChildForm_6533");
                this.shouldBeEquals(field.getComponentName(), "ZDInput");
                this.shouldBeEquals(field.getChildFormItemFlag(), false);
            }
            if (i == 4) {
                this.shouldBeEquals(field.getRef(), "ZDInput_3159");
                this.shouldBeEquals(field.getParentRef(), "ZDChildForm_6533");
                this.shouldBeEquals(field.getComponentName(), "ZDInput");
                this.shouldBeEquals(field.getChildFormItemFlag(), false);
            }
            if (i == 5) {
                this.shouldBeEquals(field.getRef(), "id");
                this.shouldBeEquals(field.getParentRef(), "ZDChildForm_52e6");
                this.shouldBeEquals(field.getParentLabel(), "子表单");
                this.shouldBeEquals(field.getChildFormItemFlag(), true);
            }
            if (i == 6) {
                this.shouldBeEquals(field.getRef(), "ZDInput_5252");
                this.shouldBeEquals(field.getParentRef(), "ZDChildForm_52e6");
                this.shouldBeEquals(field.getComponentName(), "ZDInput");
                this.shouldBeEquals(field.getChildFormItemFlag(), false);
            }
            if (i == 7) {
                this.shouldBeEquals(field.getRef(), "ZDInput_4226");
                this.shouldBeEquals(field.getParentRef(), "ZDChildForm_52e6");
                this.shouldBeEquals(field.getComponentName(), "ZDInput");
                this.shouldBeEquals(field.getChildFormItemFlag(), false);
            }
            if (i == 8) {
                this.shouldBeEquals(field.getRef(), "ZDInput_4f36");
                this.shouldBeEquals(field.getParentRef(), "ZDChildForm_52e6");
                this.shouldBeEquals(field.getComponentName(), "ZDInput");
                this.shouldBeEquals(field.getChildFormItemFlag(), false);
            }
            if (i == 9) {
                this.shouldBeEquals(field.getRef(), "creator");
                this.shouldBeEquals(field.getParentRef(), null);
                this.shouldBeEquals(field.getComponentName(), null);
                this.shouldBeEquals(field.getChildFormItemFlag(), false);
            }
            if (i == 10) {
                this.shouldBeEquals(field.getRef(), "createTimeStamp");
                this.shouldBeEquals(field.getParentRef(), null);
                this.shouldBeEquals(field.getComponentName(), null);
                this.shouldBeEquals(field.getChildFormItemFlag(), false);
            }
            if (i == 11) {
                this.shouldBeEquals(field.getRef(), "updateTimeStamp");
                this.shouldBeEquals(field.getParentRef(), null);
                this.shouldBeEquals(field.getComponentName(), null);
                this.shouldBeEquals(field.getChildFormItemFlag(), false);
            }
        }
    }
}
