package com.sinitek.sirm.nocode.test.tool.util;

import com.sinitek.sirm.common.utils.FileUtil;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 11/22/2021
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class ZdTestFileUtil {

    public static String readTxtFile(String filePath) {
        if (filePath.startsWith("txt")
            || filePath.startsWith("json")) {
            filePath = "/" + filePath;
        }
        File resource = new File(
            Objects.requireNonNull(ZdTestFileUtil.class.getResource(filePath))
                .getFile());
        return new String(FileUtil.getBytesFromFile(resource));
    }

    public static File readFile(String filePath) {
        if (filePath.startsWith("txt")
            || filePath.startsWith("json")) {
            filePath = "/" + filePath;
        }
        return new File(
            Objects.requireNonNull(ZdTestFileUtil.class.getResource(filePath))
                .getFile());
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CellDeal {

        private String name;
        private Function<String, Object> valueFunc;
        private Function<String, String> mapKeyFunc;

        public static CellDeal raw(String name) {
            return CellDeal.builder().name(name.trim()).build();
        }

        public static CellDeal underlineTosmallCamel(String name) {
            return CellDeal.builder()
                .name(name.trim())
                .mapKeyFunc(ZdNameStyleUtil::underlineTosmallCamel)
                .build();
        }

        public static CellDeal underlineTosmallCamelAndCastInter(String name) {
            return CellDeal.builder()
                .name(name.trim())
                .valueFunc(Integer::valueOf)
                .mapKeyFunc(ZdNameStyleUtil::underlineTosmallCamel)
                .build();
        }

        public static CellDeal underlineTosmallCamelAndCastLong(String name) {
            return CellDeal.builder()
                .name(name.trim())
                .valueFunc(Long::valueOf)
                .mapKeyFunc(ZdNameStyleUtil::underlineTosmallCamel)
                .build();
        }

        public static CellDeal rawAndCastInter(String name) {
            return CellDeal.builder()
                .name(name.trim())
                .valueFunc(Integer::valueOf)
                .build();
        }
    }

    @SuppressWarnings("squid:S2677")
    public static List<Map<String, Object>> readDBSnapshot(String filePath,
        Map<Integer, CellDeal> cellDealMap) throws IOException {
        List<Map<String, Object>> result = new ArrayList<>();

        // 使用 BufferedReader 读取文本文件
        try (BufferedReader br = new BufferedReader(
            new FileReader(ZdTestFileUtil.readFile(filePath)))) {
            String line;
            // 跳过表头
            br.readLine();
            br.readLine();
            while ((line = br.readLine()) != null) {
                // 解析每一行，使用 "|" 作为分隔符
                String[] parts = line.split("\\|");

                Map<String, Object> node = new HashMap<>();

                cellDealMap.forEach((index, dealHandler) -> {
                    String name = dealHandler.getName();
                    Function<String, Object> valueFunc = dealHandler.getValueFunc();
                    Function<String, String> mapKeyFunc = dealHandler.getMapKeyFunc();
                    String value = parts[index];

                    String mapKey = name;
                    if (Objects.nonNull(mapKeyFunc)) {
                        mapKey = mapKeyFunc.apply(name);
                    }

                    if (Objects.nonNull(valueFunc)) {
                        node.put(mapKey, valueFunc.apply(value.trim()));
                    } else {
                        node.put(mapKey, value.trim());
                    }

                });
                result.add(node);
            }
        }
        return result;
    }
}
