package com.sinitek.sirm.nocode.test.tool.util;

import com.baomidou.mybatisplus.core.toolkit.LambdaUtils;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.core.toolkit.support.SerializedLambda;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.property.PropertyNamer;

/**
 * <AUTHOR>
 * @date 07/25/2024 17:24
 */
@Slf4j
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class ZdLambdaUtil {

    public static String getFieldNameByLambda(SFunction<?, Object> valueGetter) {
        SerializedLambda lambda = LambdaUtils.resolve(valueGetter);
        return PropertyNamer.methodToProperty(lambda.getImplMethodName());
    }

}
