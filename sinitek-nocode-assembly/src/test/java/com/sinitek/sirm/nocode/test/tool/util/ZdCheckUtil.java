package com.sinitek.sirm.nocode.test.tool.util;

import static org.hamcrest.MatcherAssert.assertThat;

import cn.hutool.core.lang.func.Consumer3;
import com.baomidou.mybatisplus.core.toolkit.LambdaUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.core.toolkit.support.SerializedLambda;
import java.util.Collections;
import java.util.Map;
import java.util.Objects;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.ibatis.reflection.property.PropertyNamer;
import org.hamcrest.Matchers;

/**
 * <AUTHOR>
 * @date 07/25/2024 17:24
 */
@Slf4j
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class ZdCheckUtil {

    public static <T> void checkPropertyValue(T data, Map<String, Object> expectData,
        SFunction<T, Object> valueGetter) {
        Object actValue = valueGetter.apply(data);
        SerializedLambda lambda = LambdaUtils.resolve(valueGetter);
        String fieldName = PropertyNamer.methodToProperty(lambda.getImplMethodName());
        Object expectedValue = MapUtils.getObject(expectData, fieldName);
        shouldBeEquals(actValue, expectedValue);
    }

    /**
     * 对比快照时,无法区分null或者空字符串,这里null和空字符串视为一致
     */
    public static <T> void checkDBSnapshotPropertyValue(T data, Map<String, Object> expectData,
        SFunction<T, Object> valueGetter) {
        checkDBSnapshotPropertyValue(data, expectData, valueGetter, Collections.emptyMap());
    }

    public static <T> void checkDBSnapshotPropertyValue(T data, Map<String, Object> expectData,
        SFunction<T, Object> valueGetter,
        Map<String, Consumer3<T, Map<String, Object>, Object>> customCheck) {
        Object actValue = valueGetter.apply(data);
        String fieldName = ZdLambdaUtil.getFieldNameByLambda(valueGetter);
        // null 值会变成空字符串
        Object expectedValue = MapUtils.getObject(expectData, fieldName);
        Object compareExpectedValue = expectedValue;
        if (expectedValue instanceof String) {
            if (Objects.isNull(actValue) && StringUtils.isBlank((String) expectedValue)) {
                compareExpectedValue = null;
            }
        }

        Consumer3<T, Map<String, Object>, Object> objectConsumer = customCheck.get(fieldName);

        log.info("校验 {}", fieldName);
        if (Objects.nonNull(objectConsumer)) {
            objectConsumer.accept(data, expectData, actValue);
        } else {
            shouldBeEquals(actValue, compareExpectedValue);
        }
    }

    public static void shouldBeEquals(Object actValue, Object expectedValue) {
        assertThat(actValue, Matchers.equalTo(expectedValue));
    }

}
