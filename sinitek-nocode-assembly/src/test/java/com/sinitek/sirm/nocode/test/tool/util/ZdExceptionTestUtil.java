package com.sinitek.sirm.nocode.test.tool.util;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.instanceOf;
import static org.hamcrest.Matchers.is;

import com.sinitek.sirm.framework.exception.BussinessException;
import com.sinitek.sirm.nocode.test.tool.support.ZdDoAny;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * @date 06/28/2024 09:35
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class ZdExceptionTestUtil {

    @SuppressWarnings({"squid:ReturnMapCheck", "squid:S109", "squid:CatchCheck"})
    public static void shouldHasBussinessExcetion(ZdDoAny doAny, String code, String message) {
        try {
            doAny.doAny();
            assertThat(true, is(false));
        } catch (Exception e) {
            assertThat(e, instanceOf(BussinessException.class));
            BussinessException ex = (BussinessException) e;
            String exCode = ex.getCode();
            assertThat(exCode, equalTo(code));
            assertThat(ex.getMessage(), equalTo(message));
        }
    }

    @SuppressWarnings({"squid:ReturnMapCheck", "squid:S109", "squid:CatchCheck"})
    public static void shouldNoExcetion(ZdDoAny doAny) {
        try {
            doAny.doAny();
        } catch (Exception e) {
            assertThat(true, is(false));
            throw e;
        }
    }

}
