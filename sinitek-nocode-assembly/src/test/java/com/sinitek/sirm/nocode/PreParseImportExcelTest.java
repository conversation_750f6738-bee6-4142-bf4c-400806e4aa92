package com.sinitek.sirm.nocode;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.enums.CellExtraTypeEnum;
import com.alibaba.excel.metadata.CellExtra;
import com.sinitek.sirm.common.utils.JsonUtil;
import com.sinitek.sirm.nocode.form.dto.ZdFormFieldDTO;
import com.sinitek.sirm.nocode.form.dto.ZdImportFormHeaderFieldDTO;
import com.sinitek.sirm.nocode.form.dto.ZdParseResultDTO;
import com.sinitek.sirm.nocode.form.support.listener.ImportExcelPreParseListener;
import com.sinitek.sirm.nocode.form.util.ZdPageDataUtil;
import com.sinitek.sirm.nocode.test.tool.ZdUnitTestBase;
import com.sinitek.sirm.nocode.test.tool.util.ZdTestFileUtil;
import java.io.File;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 * @date 2025-07-16 17:42
 */
@Slf4j
public class PreParseImportExcelTest extends ZdUnitTestBase {

    @Test
    @DisplayName("测试复杂表单预解析")
    public void preParseExcel() {
        File file = ZdTestFileUtil.readFile("txt/clean_merged_data.xlsx");

        String pageData = ZdTestFileUtil.readTxtFile(
            "json/test_parse_page_json/sub-form.json");
        ZdParseResultDTO result = ZdPageDataUtil.parsePageData(
            JsonUtil.toMap(pageData));
        List<ZdImportFormHeaderFieldDTO> fields = result.getHeadFields();

        ImportExcelPreParseListener listener = new ImportExcelPreParseListener(fields);
        EasyExcel.read(file, listener)
            .extraRead(CellExtraTypeEnum.MERGE).sheet()
            .doRead();

        List<Map<String, Object>> parseResultList = listener.findParseResultList();

        log.info("表头行数: {}", listener.getHeadCount());
        log.info("数据行数: {}", listener.getDataCount());
        log.info("解析数据 {} 条", parseResultList.size());
        log.info("{}", JsonUtil.toJsonString(parseResultList));
        List<ZdFormFieldDTO> excelHeader = listener.findExcelHeader();
        log.info("{}", JsonUtil.toJsonString(excelHeader));
        Map<Integer, Map<Integer, CellExtra>> cellExtraData = (Map<Integer, Map<Integer, CellExtra>>) listener.getCellExtraData();
        log.info("{}", JsonUtil.toJsonString(cellExtraData));

        this.shouldBeEquals(listener.getDataCount(), 8);
        this.shouldBeEquals(listener.getHeadCount(), 2);
        this.shouldHasSize(parseResultList, 8);

        for (int i = 0; i < parseResultList.size(); i++) {
            Map<String, Object> data = parseResultList.get(i);
            if (Arrays.asList(0, 1, 2).contains(i)) {
                this.shouldBeEquals(data.get("0"), "1944699206199545857");
            } else if (Arrays.asList(3, 4).contains(i)) {
                this.shouldBeEquals(data.get("0"), "1944584728531308545");
            } else if (Arrays.asList(5, 6, 7).contains(i)) {
                this.shouldBeEquals(data.get("0"), "1944594235089096705");
            }
        }

        for (int i = 0; i < excelHeader.size(); i++) {
            ZdFormFieldDTO field = excelHeader.get(i);
            if (Arrays.asList(0, 1).contains(i)) {
                this.shouldBeEquals(field.getRef(), String.valueOf(i));
            } else if (i == 2) {
                this.shouldHasSize(field.getChildren(), 3);

                for (int i1 = 0; i1 < field.getChildren().size(); i1++) {
                    ZdFormFieldDTO child = field.getChildren().get(i1);
                    this.shouldBeEquals(child.getRef(), String.valueOf(i1 + 2));
                }
            } else if (i == 3) {
                this.shouldHasSize(field.getChildren(), 3);
                for (int i1 = 0; i1 < field.getChildren().size(); i1++) {
                    ZdFormFieldDTO child = field.getChildren().get(i1);
                    this.shouldBeEquals(child.getRef(), String.valueOf(i1 + 5));
                }
            } else if (Arrays.asList(4, 5, 6).contains(i)) {
                this.shouldBeEquals(field.getRef(), String.valueOf(i + 4));
            }
        }

    }

    @Test
    @DisplayName("测试预解析只保留前3条数据")
    public void preParseExcel2() {
        File file = ZdTestFileUtil.readFile("txt/clean_merged_data.xlsx");

        String pageData = ZdTestFileUtil.readTxtFile(
            "json/test_parse_page_json/sub-form.json");
        ZdParseResultDTO result = ZdPageDataUtil.parsePageData(
            JsonUtil.toMap(pageData));
        List<ZdImportFormHeaderFieldDTO> fields = result.getHeadFields();

        ImportExcelPreParseListener listener = new ImportExcelPreParseListener(fields, 3);
        EasyExcel.read(file, listener)
            .extraRead(CellExtraTypeEnum.MERGE).sheet()
            .doRead();

        List<Map<String, Object>> parseResultList = listener.findParseResultList();

        log.info("表头行数: {}", listener.getHeadCount());
        log.info("数据行数: {}", listener.getDataCount());
        log.info("解析数据 {} 条", parseResultList.size());
        log.info("{}", JsonUtil.toJsonString(parseResultList));
        List<ZdFormFieldDTO> excelHeader = listener.findExcelHeader();
        log.info("{}", JsonUtil.toJsonString(excelHeader));
        Map<Integer, Map<Integer, CellExtra>> cellExtraData = (Map<Integer, Map<Integer, CellExtra>>) listener.getCellExtraData();
        log.info("{}", JsonUtil.toJsonString(cellExtraData));

        this.shouldBeEquals(listener.getDataCount(), 1);
        this.shouldBeEquals(listener.getHeadCount(), 2);
        this.shouldHasSize(parseResultList, 1);

        for (int i = 0; i < parseResultList.size(); i++) {
            Map<String, Object> data = parseResultList.get(i);
            this.shouldBeEquals(data.get("0"), "1944699206199545857");
        }

        for (int i = 0; i < excelHeader.size(); i++) {
            ZdFormFieldDTO field = excelHeader.get(i);
            if (Arrays.asList(0, 1).contains(i)) {
                this.shouldBeEquals(field.getRef(), String.valueOf(i));
            } else if (i == 2) {
                this.shouldHasSize(field.getChildren(), 3);

                for (int i1 = 0; i1 < field.getChildren().size(); i1++) {
                    ZdFormFieldDTO child = field.getChildren().get(i1);
                    this.shouldBeEquals(child.getRef(), String.valueOf(i1 + 2));
                }
            } else if (i == 3) {
                this.shouldHasSize(field.getChildren(), 3);
                for (int i1 = 0; i1 < field.getChildren().size(); i1++) {
                    ZdFormFieldDTO child = field.getChildren().get(i1);
                    this.shouldBeEquals(child.getRef(), String.valueOf(i1 + 5));
                }
            } else if (Arrays.asList(4, 5, 6).contains(i)) {
                this.shouldBeEquals(field.getRef(), String.valueOf(i + 4));
            }
        }

        this.shouldHasSize(cellExtraData, 2);
        this.shouldHasSize(cellExtraData.get(0), 7);
        this.shouldHasSize(cellExtraData.get(2), 5);

    }
}

