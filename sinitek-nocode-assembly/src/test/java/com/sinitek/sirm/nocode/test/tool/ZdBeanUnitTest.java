package com.sinitek.sirm.nocode.test.tool;

import com.sinitek.sirm.nocode.SirmApplication;
import com.sinitek.sirm.nocode.test.tool.util.ZdSqlRunnerUtil;
import com.sinitek.sirm.nocode.test.tool.util.ZdTestDataUtil;
import java.io.IOException;
import java.sql.SQLException;
import javax.sql.DataSource;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSessionFactory;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.core.env.Environment;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @date 2023/5/23
 */
@Slf4j
@ActiveProfiles("local")
@RunWith(SpringRunner.class)
@SpringBootTest(classes = SirmApplication.class)
@AutoConfigureMockMvc
public class ZdBeanUnitTest extends ZdUnitTestBase {

    @Autowired
    protected SqlSessionFactory sqlSessionFactory;

    @Autowired
    protected Environment environment;

    protected ZdSqlRunnerUtil sqlRunner;

    protected void initSqlRrunner() {
        this.sqlRunner = new ZdSqlRunnerUtil(this.sqlSessionFactory);
    }

    protected DataSource getDataSource() {
        return this.sqlSessionFactory.getConfiguration().getEnvironment().getDataSource();
    }

    protected void runSqlScript(String resource) throws SQLException, IOException {
        ZdTestDataUtil.runScript(
            this.getDataSource(),
            resource);
    }


}
