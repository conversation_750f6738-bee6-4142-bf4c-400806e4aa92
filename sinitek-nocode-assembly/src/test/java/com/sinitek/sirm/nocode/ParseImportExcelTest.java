package com.sinitek.sirm.nocode;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.enums.CellExtraTypeEnum;
import com.sinitek.sirm.common.utils.JsonUtil;
import com.sinitek.sirm.nocode.form.dto.ZdFormFieldDTO;
import com.sinitek.sirm.nocode.form.dto.ZdImportFormHeaderFieldDTO;
import com.sinitek.sirm.nocode.form.dto.ZdParseResultDTO;
import com.sinitek.sirm.nocode.form.support.listener.ImportExcelParseListener;
import com.sinitek.sirm.nocode.form.util.ZdPageDataUtil;
import com.sinitek.sirm.nocode.test.tool.ZdUnitTestBase;
import com.sinitek.sirm.nocode.test.tool.util.ZdTestFileUtil;
import java.io.File;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 * @date 2025-07-16 17:42
 */
@Slf4j
public class ParseImportExcelTest extends ZdUnitTestBase {

    @Test
    @DisplayName("测试复杂表单解析")
    public void parseExcel() {
        File file = ZdTestFileUtil.readFile("txt/test_import_excel_parse.xlsx");

        String pageData = ZdTestFileUtil.readTxtFile(
            "json/test_parse_page_json/sub-form.json");
        ZdParseResultDTO result = ZdPageDataUtil.parsePageData(
            JsonUtil.toMap(pageData));
        List<ZdImportFormHeaderFieldDTO> fields = result.getHeadFields();
        ImportExcelParseListener listener = new ImportExcelParseListener(fields,
            Collections.emptyMap());
        EasyExcel.read(file, listener)
            .extraRead(CellExtraTypeEnum.MERGE).sheet()
            .doRead();

        List<Map<String, Object>> parseResultList = listener.findParseResultList();

        log.info("表头行数: {}", listener.getHeadCount());
        log.info("数据行数: {}", listener.getDataCount());
        log.info("解析数据 {} 条", parseResultList.size());
        log.info("{}", JsonUtil.toJsonString(parseResultList));

        this.shouldBeEquals(listener.getDataCount(), 8);
        this.shouldBeEquals(listener.getHeadCount(), 2);
        this.shouldHasSize(parseResultList, 3);

        for (int i = 0; i < parseResultList.size(); i++) {
            Map<String, Object> parseResult = parseResultList.get(i);
            if (i == 0) {
                this.shouldBeEquals(MapUtils.getString(parseResult, "id"), "1944699206199545857");
                this.shouldHasSize(
                    (List<Object>) MapUtils.getObject(parseResult, "ZDChildForm_4352"), 3);
                this.shouldHasSize(
                    (List<Object>) MapUtils.getObject(parseResult, "ZDChildForm_3641"), 2);
            } else if (i == 1) {
                this.shouldBeEquals(MapUtils.getString(parseResult, "id"), "1944584728531308545");
                this.shouldHasSize(
                    (List<Object>) MapUtils.getObject(parseResult, "ZDChildForm_4352"), 2);
                this.shouldHasSize(
                    (List<Object>) MapUtils.getObject(parseResult, "ZDChildForm_3641"), 2);
            } else {
                this.shouldBeEquals(MapUtils.getString(parseResult, "id"), "1944594235089096705");
                this.shouldHasSize(
                    (List<Object>) MapUtils.getObject(parseResult, "ZDChildForm_4352"), 3);
                this.shouldHasSize(
                    (List<Object>) MapUtils.getObject(parseResult, "ZDChildForm_3641"), 2);
            }

        }
    }

    @Test
    @DisplayName("测试excel表头比表单少")
    public void parseExcel2() {
        File file = ZdTestFileUtil.readFile("txt/test_excel_less_head.xlsx");

        String pageData = ZdTestFileUtil.readTxtFile(
            "json/test_parse_page_json/sub-form.json");
        ZdParseResultDTO result = ZdPageDataUtil.parsePageData(
            JsonUtil.toMap(pageData));
        List<ZdImportFormHeaderFieldDTO> fields = result.getHeadFields();

        ImportExcelParseListener listener = new ImportExcelParseListener(fields,
            Collections.emptyMap());
        EasyExcel.read(file, listener)
            .extraRead(CellExtraTypeEnum.MERGE).sheet()
            .doRead();

        List<Map<String, Object>> parseResultList = listener.findParseResultList();

        log.info("表头行数: {}", listener.getHeadCount());
        log.info("数据行数: {}", listener.getDataCount());
        log.info("解析数据 {} 条", parseResultList.size());
        log.info("{}", JsonUtil.toJsonString(parseResultList));

        this.shouldBeEquals(listener.getDataCount(), 8);
        this.shouldBeEquals(listener.getHeadCount(), 2);
        this.shouldHasSize(parseResultList, 3);
    }

    @Test
    @DisplayName("测试excel表头比表单多")
    public void parseExcel3() {
        File file = ZdTestFileUtil.readFile("txt/test_import_excel_parse.xlsx");

        String pageData = ZdTestFileUtil.readTxtFile(
            "json/test_parse_import_excel/less_data.json");
        List<ZdImportFormHeaderFieldDTO> fields = JsonUtil.toJavaObjectList(pageData,
            ZdImportFormHeaderFieldDTO.class);

        ImportExcelParseListener listener = new ImportExcelParseListener(fields,
            Collections.emptyMap());
        EasyExcel.read(file, listener)
            .extraRead(CellExtraTypeEnum.MERGE).sheet()
            .doRead();

        List<Map<String, Object>> parseResultList = listener.findParseResultList();

        log.info("表头行数: {}", listener.getHeadCount());
        log.info("数据行数: {}", listener.getDataCount());
        log.info("解析数据 {} 条", parseResultList.size());
        log.info("{}", JsonUtil.toJsonString(parseResultList));

        this.shouldBeEquals(listener.getDataCount(), 8);
        this.shouldBeEquals(listener.getHeadCount(), 2);
        this.shouldHasSize(parseResultList, 3);
    }

    @Test
    @DisplayName("获取Excel表头测试")
    public void parseExcel4() {
        File file = ZdTestFileUtil.readFile("txt/test_import_excel_parse.xlsx");

        String pageData = ZdTestFileUtil.readTxtFile(
            "json/test_parse_page_json/sub-form.json");
        ZdParseResultDTO result = ZdPageDataUtil.parsePageData(
            JsonUtil.toMap(pageData));
        List<ZdImportFormHeaderFieldDTO> fields = result.getHeadFields();

        ImportExcelParseListener listener = new ImportExcelParseListener(fields,
            Collections.emptyMap());
        EasyExcel.read(file, listener)
            .extraRead(CellExtraTypeEnum.MERGE).sheet()
            .doRead();

        List<Map<String, Object>> parseResultList = listener.findParseResultList();

        log.info("表头行数: {}", listener.getHeadCount());
        log.info("数据行数: {}", listener.getDataCount());
        log.info("解析数据 {} 条", parseResultList.size());
        log.info("{}", JsonUtil.toJsonString(parseResultList));

        List<ZdFormFieldDTO> excelHeader = listener.findExcelHeader();
        log.info("excelHeader size: {}", excelHeader.size());
        log.info("{}", JsonUtil.toJsonString(excelHeader));
        this.shouldHasSize(excelHeader, 7);

        ZdFormFieldDTO field = excelHeader.get(2);
        this.shouldHasSize(field.getChildren(), 3);

        field = excelHeader.get(3);
        this.shouldHasSize(field.getChildren(), 3);
    }
}

