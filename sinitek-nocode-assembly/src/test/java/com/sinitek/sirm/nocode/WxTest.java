package com.sinitek.sirm.nocode;

import com.sinitek.sirm.nocode.message.config.ZdWxCpConfig;
import com.sinitek.sirm.nocode.test.tool.ZdBeanUnitTest;
import com.sinitek.sirm.wxwork.config.WxCpProperties;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.cp.api.WxCpMessageService;
import me.chanjar.weixin.cp.api.WxCpService;
import me.chanjar.weixin.cp.api.impl.WxCpMessageServiceImpl;
import me.chanjar.weixin.cp.bean.message.WxCpMessage;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2025-08-08 14:45
 */
@Slf4j
public class WxTest extends ZdBeanUnitTest {

    @Autowired
    private WxCpProperties properties;

    @Test
    @DisplayName("测试消息发送")
    public void testSendMessage() {
        String appCode = "zhida";
        WxCpService cpService = ZdWxCpConfig.getCpService(appCode);

        WxCpMessageService wxCpMessageService = new WxCpMessageServiceImpl(cpService);

        WxCpProperties.AppConfig appConfig = this.properties.getAppConfigs().get(appCode);

        WxCpMessage message = WxCpMessage.TEXT().agentId(appConfig.getAgentId()).toUser("zs.liu")
                .content("Hello World").build();
        try {
            wxCpMessageService.send(message);
        } catch (Exception e) {
            log.error("发送消息失败 {}", e.getMessage(), e);
        }
    }

}
