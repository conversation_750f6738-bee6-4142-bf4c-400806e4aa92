package com.sinitek.sirm.nocode;

import cn.hutool.extra.spring.EnableSpringUtil;
import com.sinitek.cloud.base.support.ActionTrackInterceptor;
import com.sinitek.sirm.nocode.common.annotation.EnableApplicationToken;
import com.sinitek.sirm.nocode.utils.ApplicationStartUtil;
import java.util.Collections;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.loadbalancer.LoadBalanced;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.web.client.RestTemplate;

@SpringBootApplication
@ComponentScan(value = {"com.sinitek"})
@EnableFeignClients(basePackages = {"com.sinitek"})
@MapperScan("com.sinitek.**.mapper")
@EnableSpringUtil
@EnableAsync
@EnableApplicationToken
@EnableScheduling
public class SirmApplication {

    public static void main(String[] args) {
        ApplicationStartUtil.startBackendApplication(SirmApplication.class, args);
    }

    @Autowired
    private ActionTrackInterceptor actionTrackInterceptor;

    @Bean("restTemplate")
    @LoadBalanced
    public RestTemplate getRestTemplate() {
        RestTemplate restTemplate = new RestTemplate();
        restTemplate.setInterceptors(Collections.singletonList(actionTrackInterceptor));
        return restTemplate;
    }

}
