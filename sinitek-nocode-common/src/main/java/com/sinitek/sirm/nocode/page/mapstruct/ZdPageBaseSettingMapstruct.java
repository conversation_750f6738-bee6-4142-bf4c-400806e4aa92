package com.sinitek.sirm.nocode.page.mapstruct;

import com.sinitek.sirm.nocode.page.dto.ZdPageBaseSettingDTO;
import com.sinitek.sirm.nocode.page.entity.ZdPageBaseSetting;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

/**
 * <AUTHOR>
 * @version 2025.0711
 * @since 1.0.0-SNAPSHOT
 */
@Mapper(componentModel = "spring")
public interface ZdPageBaseSettingMapstruct {
    // 正向映射：ZdPageBaseSetting → ZdPageBaseSettingDTO
    @Mapping(target = "formCode", source = "pageCode")
    ZdPageBaseSettingDTO toDTO(ZdPageBaseSetting pageBaseSetting);

    @InheritInverseConfiguration(name = "toDTO")
    ZdPageBaseSetting toEntity(ZdPageBaseSettingDTO dto);


    @Mapping(target = "id", ignore = true)
    @Mapping(source = "formCode", target = "pageCode")
    void updateEntityFromDto(ZdPageBaseSettingDTO dto, @MappingTarget ZdPageBaseSetting entity);
}
