package com.sinitek.sirm.nocode.common.support.el;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.sinitek.sirm.framework.exception.BussinessException;
import com.sinitek.sirm.nocode.app.service.IZdAppManagerService;
import com.sinitek.sirm.nocode.app.service.IZdAppService;
import com.sinitek.sirm.nocode.form.service.IZdPageFormService;
import com.sinitek.sirm.nocode.page.dto.ZdPageAuthDTO;
import com.sinitek.sirm.nocode.page.enumerate.PageAuthTypeEnum;
import com.sinitek.sirm.nocode.page.service.IZdPageAuthService;
import com.sinitek.sirm.nocode.page.service.IZdPageService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 2025.0416
 * @description 页面el表达式
 * @since 1.0.0-SNAPSHOT
 */
@Component
public class AppRightEl {
    @Resource
    public IZdPageService pageService;
    @Resource
    public IZdPageAuthService pageAuthService;
    @Resource
    public IZdAppManagerService managerService;
    @Resource
    public IZdAppService appService;

    @Resource
    public IZdPageFormService formService;


    /**
     * 根据页面编码获取应用编码
     *
     * @param pageCode 页面编码
     * @return 应用编码
     */
    public String getAppCodeByPageCode(String pageCode) {
        return pageService.getAppCodeByCode(Collections.singletonList(pageCode));
    }

    public String getAppCodeByPageId(Long id) {
        return pageService.getAppCodeById(id);
    }

    /**
     * 根据页面编码获取应用编码
     *
     * @param pageCodeList 页面编码
     * @return 应用编码
     */
    public String getAppCodeByPageCode(List<String> pageCodeList) {
        return pageService.getAppCodeByCode(pageCodeList);
    }

    /**
     * 通过权限id 获取应用的code
     *
     * @param id 权限id
     * @return 应用编码
     */
    public String getAppCodeByPageAuthId(Long id) {
        return pageAuthService.getAppCodeByPageAuthId(id);
    }

    /**
     * 通过表单id 获取应用的code
     *
     * @param formId 表单id
     * @return 应用编码
     */
    public String getAppCodeByFormId(Long formId) {
        return formService.getAppCodeByFormId(formId);
    }

    /**
     * 判断是否有应用权限
     *
     * @param pageCode 页面编码
     * @param orgId    组织id
     * @return 是否有权限
     */
    public boolean hasAppAuthByPageCode(String pageCode, String orgId) {
        String appCode = getAppCodeByPageCode(pageCode);
        return managerService.hasAuth(appCode, orgId);
    }

    /**
     * 获取应用是否启用
     *
     * @param pageCode 页面编码
     * @return 是否启用
     */
    public boolean isEnable(String pageCode) {
        return appService.isEnable(pageCode);
    }

    public String getAppCodeByAppId(Long appId) {
        return appService.getCodeById(appId);
    }

    /**
     * 获取页面权限信息
     *
     * @param pageCode 页面编码
     * @param authType 权限类型（SUBMIT_AUTH: 提交权限, DATA_AUTH: 数据权限）
     * @param orgId    组织人员ID
     * @return 返回查询到的页面权限信息DTO
     * @throws BussinessException 当用户既没有直接页面权限，也没有应用管理员权限时抛出异常
     *                            <p>
     *                            逻辑说明：
     *                            1. 首先通过页面编码、权限类型和组织ID查询页面权限信息
     *                            2. 如果没有找到直接的页面权限：
     *                            a. 查询该页面所属的应用编码
     *                            b. 检查用户是否具有该应用的管理员权限
     *                            c. 如果有管理员权限则返回null（表示应用级权限已覆盖）
     *                            d. 如果没有管理员权限则抛出业务异常（3000005 错误码）
     */
    public ZdPageAuthDTO getPageAuth(String pageCode, PageAuthTypeEnum authType, String orgId) {
        String appCode = getAppCodeByPageCode(pageCode);
        if (StringUtils.isBlank(appCode)) {
            throw new BussinessException("3000022", pageCode);
        }
        ZdPageAuthDTO zdPageAuthDTO = pageAuthService.rightQuery(pageCode, authType, orgId);
        if (Objects.isNull(zdPageAuthDTO)) {
            if (managerService.hasAuth(appCode, orgId)) {
                return null;
            }
            throw new BussinessException("3000005");
        }
        return zdPageAuthDTO;
    }

}
