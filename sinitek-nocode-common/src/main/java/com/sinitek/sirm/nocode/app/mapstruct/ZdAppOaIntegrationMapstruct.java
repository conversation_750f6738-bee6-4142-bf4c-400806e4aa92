package com.sinitek.sirm.nocode.app.mapstruct;

import com.sinitek.sirm.nocode.app.dto.ZdAppOaIntegrationDTO;
import com.sinitek.sirm.nocode.app.entity.ZdAppOaIntegration;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;

/**
 * <AUTHOR>
 * @version 2025.0711
 * @since 1.0.0-SNAPSHOT
 */
@Mapper(componentModel = "spring")
public interface ZdAppOaIntegrationMapstruct {
    // 正向映射：ZdAppOaIntegration → ZdAppOaIntegrationDTO
    ZdAppOaIntegrationDTO toDTO(ZdAppOaIntegration appOaIntegration);
    
    @InheritInverseConfiguration(name = "toDTO")
    ZdAppOaIntegration toEntity(ZdAppOaIntegrationDTO dto);
}
