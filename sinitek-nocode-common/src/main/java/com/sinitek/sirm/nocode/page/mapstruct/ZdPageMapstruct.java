package com.sinitek.sirm.nocode.page.mapstruct;

import com.sinitek.sirm.nocode.page.dto.ZdPageDTO;
import com.sinitek.sirm.nocode.page.dto.ZdPageSquareDTO;
import com.sinitek.sirm.nocode.page.entity.ZdPage;
import com.sinitek.sirm.nocode.page.enumerate.PageTypeEnum;
import com.sinitek.sirm.nocode.page.po.ZdPageSquarePO;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2025.0711
 * @since 1.0.0-SNAPSHOT
 */
@Mapper(componentModel = "spring")
public interface ZdPageMapstruct {
    // 正向映射：ZdPage → ZdPageDTO
    @Mapping(target = "children", ignore = true)
    ZdPageDTO toDTO(ZdPage page);

    List<ZdPageDTO> toDTOList(List<ZdPage> page);

    @InheritInverseConfiguration(name = "toDTO")
    ZdPage toEntity(ZdPageDTO dto);


    ZdPageSquareDTO toSquareDTO(ZdPageSquarePO po);

    default PageTypeEnum toPageTypeEnum(Integer type) {
        return PageTypeEnum.fromValue(type);
    }

    List<ZdPageSquareDTO> toSquareDTOList(List<ZdPageSquarePO> page);


}
