package com.sinitek.sirm.nocode.page.mapstruct;

import com.sinitek.sirm.common.utils.JsonUtil;
import com.sinitek.sirm.nocode.form.dto.ZdFormDataScopeCustomConditionDTO;
import com.sinitek.sirm.nocode.page.dto.ZdPageAuthDTO;
import com.sinitek.sirm.nocode.page.entity.ZdPageAuth;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 2025.0711
 * @since 1.0.0-SNAPSHOT
 */
@Mapper(componentModel = "spring")
public interface ZdPageAuthMapstruct {
    // 正向映射：ZdPageAuth → ZdPageAuthDTO
    @Mapping(target = "formCode", source = "pageCode")
    ZdPageAuthDTO toDTO(ZdPageAuth pageAuth);

    @InheritInverseConfiguration(name = "toDTO")
    ZdPageAuth toEntity(ZdPageAuthDTO dto);

    List<ZdPageAuthDTO> toDTOList(List<ZdPageAuth> pageAuthList);


    @Mapping(target = "id", ignore = true)
    @Mapping(source = "formCode", target = "pageCode")
    void updateEntityFromDto(ZdPageAuthDTO dto, @MappingTarget ZdPageAuth entity);


    /**
     * 将自定义条件对象转换为JSON字符串
     * 此方法用于序列化查询构建器中的条件结构，以便存储
     *
     * @param zdFormDataScopeCustomConditionDTO 自定义条件对象实例
     * @return 返回转换后的JSON字符串，如果输入对象为null则返回null
     */
    default String stringToCondition(ZdFormDataScopeCustomConditionDTO zdFormDataScopeCustomConditionDTO) {
        // 检查输入对象是否非空，以决定是否进行序列化
        if (Objects.nonNull(zdFormDataScopeCustomConditionDTO)) {
            // 使用JsonUtil工具类将自定义条件对象转换为JSON字符串
            return JsonUtil.toJsonString(zdFormDataScopeCustomConditionDTO);
        }
        // 如果输入对象为null，则直接返回null
        return null;
    }

    /**
     * 将JSON字符串转换为自定义条件对象
     * 此方法用于反序列化存储的条件字符串，重建查询构建器所需的条件结构
     *
     * @param condition 包含条件数据的JSON字符串
     * @return 返回转换后的自定义条件对象实例，如果输入字符串为null则返回null
     */
    default ZdFormDataScopeCustomConditionDTO stringToCondition(String condition) {
        // 检查输入字符串是否非空，以决定是否进行反序列化
        if (Objects.nonNull(condition)) {
            // 使用JsonUtil工具类将JSON字符串转换为自定义条件对象
            return JsonUtil.toJavaObject(condition, ZdFormDataScopeCustomConditionDTO.class);
        }
        // 如果输入字符串为null，则直接返回null
        return null;
    }
}
