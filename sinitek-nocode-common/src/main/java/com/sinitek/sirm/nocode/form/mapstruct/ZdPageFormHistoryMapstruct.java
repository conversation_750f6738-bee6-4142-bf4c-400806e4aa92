package com.sinitek.sirm.nocode.form.mapstruct;

import com.sinitek.sirm.nocode.form.dto.ZdPageFormHistoryDTO;
import com.sinitek.sirm.nocode.form.entity.ZdPageFormHistory;
import com.sinitek.sirm.nocode.form.po.ZdPageFormHistoryPO;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2025.0711
 * @since 1.0.0-SNAPSHOT
 */
@Mapper(componentModel = "spring")
public interface ZdPageFormHistoryMapstruct {
    // 正向映射：ZdPageFormHistory → ZdPageFormHistoryDTO
    ZdPageFormHistoryDTO toDTO(ZdPageFormHistory pageFormHistory);

    List<ZdPageFormHistoryDTO> toDTOList(List<ZdPageFormHistory> pageFormHistory);

    @InheritInverseConfiguration(name = "toDTO")
    ZdPageFormHistory toEntity(ZdPageFormHistoryDTO dto);


    ZdPageFormHistoryDTO poToDTO(ZdPageFormHistoryPO pageFormHistory);

    List<ZdPageFormHistoryDTO> poToDTOList(List<ZdPageFormHistoryPO> pageFormHistory);
}
