package com.sinitek.sirm.nocode.common.utils;

import cn.hutool.core.lang.Assert;
import cn.hutool.http.HttpException;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2025.0717
 * @since 1.0.0-SNAPSHOT
 */

public class ZdHttpUtil {
    private ZdHttpUtil() {
    }

    public static Map<String, String> getParams(HttpServletRequest request) {
        Map<String, String[]> parameterMap = request.getParameterMap();
        return parameterMap.entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, e -> e.getValue()[0]));
    }

    /**
     * 拼接参数
     *
     * @param url   url
     * @param param 参数
     * @return 拼接后的数据
     */
    public static String makeParamURL(String url, Map<String, String> param, Predicate<String> predicate) {
        if (StringUtils.isNotBlank(url) && param != null && !param.isEmpty()) {
            List<String> stringList = new ArrayList<>();
            param.forEach((k, v) -> {
                if (predicate != null && !predicate.test(k)) {
                    return;
                }
                stringList.add(k + "=" + v);
            });
            String point = url.contains("?") ? "&" : "?";
            url += point + String.join("&", stringList);
        }
        return url;
    }

    /**
     * 拼接参数
     *
     * @param url   url
     * @param param 参数
     * @return 拼接后的数据
     */
    public static String makeParamURL(String url, Map<String, String> param) {
        return makeParamURL(url, param, null);
    }

    /**
     * 拼接参数
     *
     * @param url   url
     * @param name  参数名
     * @param value 参数值
     * @return 拼接后的数据
     */
    public static String makeParamURL(String url, String name, String value) {
        Map<String, String> param = new HashMap<>();
        param.put(name, value);
        return makeParamURL(url, param, null);
    }


    public static String download(String url, OutputStream out, int timeout) {
        HttpResponse httpResponse = requestDownload(url, timeout);
        httpResponse.writeBody(out, true, null);
        return httpResponse.getFileNameFromDisposition(null);
    }

    private static HttpResponse requestDownload(String url, int timeout) {
        Assert.notBlank(url, "[url] is blank !");

        final HttpResponse response = HttpUtil.createGet(url, true)
                .timeout(timeout)
                .executeAsync();

        if (response.isOk()) {
            return response;
        }

        throw new HttpException("Server response error with status code: [{}]",
                response.getStatus());
    }


}
