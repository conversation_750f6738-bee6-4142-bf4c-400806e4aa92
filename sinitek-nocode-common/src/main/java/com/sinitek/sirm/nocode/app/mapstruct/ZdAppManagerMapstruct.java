package com.sinitek.sirm.nocode.app.mapstruct;

import com.sinitek.sirm.nocode.app.dto.ZdAppManagerDTO;
import com.sinitek.sirm.nocode.app.entity.ZdAppManager;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2025.0711
 * @since 1.0.0-SNAPSHOT
 */
@Mapper(componentModel = "spring")
public interface ZdAppManagerMapstruct {
    // 正向映射：ZdAppManager → ZdAppManagerDTO
    ZdAppManagerDTO toDTO(ZdAppManager appManager);
    
    @InheritInverseConfiguration(name = "toDTO")
    ZdAppManager toEntity(ZdAppManagerDTO dto);

    List<ZdAppManagerDTO> toDTOList(List<ZdAppManager> appManager);
}
