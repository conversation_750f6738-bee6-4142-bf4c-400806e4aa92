package com.sinitek.sirm.nocode.form.mapstruct;

import com.sinitek.sirm.nocode.form.dto.ZdPageFormShowConfigDTO;
import com.sinitek.sirm.nocode.form.entity.ZdPageFormShowConfig;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;

/**
 * <AUTHOR>
 * @version 2025.0711
 * @since 1.0.0-SNAPSHOT
 */
@Mapper(componentModel = "spring")
public interface ZdPageFormShowConfigMapstruct {
    // 正向映射：ZdPageFormShowConfig → ZdPageFormShowConfigDTO
    ZdPageFormShowConfigDTO toDTO(ZdPageFormShowConfig pageFormShowConfig);
    
    @InheritInverseConfiguration(name = "toDTO")
    ZdPageFormShowConfig toEntity(ZdPageFormShowConfigDTO dto);
}
