package com.sinitek.sirm.nocode.app.mapstruct;

import com.sinitek.sirm.nocode.app.dto.ZdAppDTO;
import com.sinitek.sirm.nocode.app.entity.ZdApp;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2025.0711
 * @since 1.0.0-SNAPSHOT
 */
@Mapper(componentModel = "spring")
public interface ZdAppMapstruct {
    // 正向映射：User → UserDTO
    //@Mapping(source = "id", target = "userId")
    //@Mapping(source = "name", target = "userName")
    //@Mapping(source = "createTime", target = "createTimeStr", dateFormat = "yyyy-MM-dd HH:mm")
    ZdAppDTO toDTO(ZdApp app);


    @Mapping(target = "id", ignore = true)
    @Mapping(target = "creatorId", ignore = true)
    @Mapping(target = "createTimeStamp", ignore = true)
    @Mapping(target = "version", ignore = true)
    @Mapping(target = "appSecret", ignore = true)
    @Mapping(target = "code", ignore = true)
    @Mapping(target = "url", ignore = true)
    @InheritInverseConfiguration(name = "toDTO")
    ZdApp toEntity(ZdAppDTO dto);


    @Mapping(target = "id", ignore = true)
    @Mapping(target = "creatorId", ignore = true)
    @Mapping(target = "createTimeStamp", ignore = true)
    @Mapping(target = "version", ignore = true)
    @Mapping(target = "appSecret", ignore = true)
    @Mapping(target = "code", ignore = true)
    @Mapping(target = "url", ignore = true)
    void updateEntityFromDto(ZdAppDTO dto, @MappingTarget ZdApp entity);

    List<ZdAppDTO> toDTOList(List<ZdApp> app);
}
