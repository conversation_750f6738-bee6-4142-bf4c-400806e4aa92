package com.sinitek.sirm.nocode.page.mapstruct;

import com.sinitek.sirm.nocode.page.dto.ZdPageSceneDTO;
import com.sinitek.sirm.nocode.page.entity.ZdPageScene;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

/**
 * <AUTHOR>
 * @version 2025.0711
 * @since 1.0.0-SNAPSHOT
 */
@Mapper(componentModel = "spring")
public interface ZdPageSceneMapstruct {
    // 正向映射：ZdPageScene → ZdPageSceneDTO
    @Mapping(target = "formCode", source = "pageCode")
    ZdPageSceneDTO toDTO(ZdPageScene pageScene);

    @InheritInverseConfiguration(name = "toDTO")
    ZdPageScene toEntity(ZdPageSceneDTO dto);

    @Mapping(target = "id", ignore = true)
    @Mapping(source = "formCode", target = "pageCode")
    void updateEntityFromDto(ZdPageSceneDTO dto, @MappingTarget ZdPageScene entity);
}
