package com.sinitek.sirm.nocode.common.support.interceptor;

import com.sinitek.sirm.nocode.common.utils.TokenFilterUtil;
import com.sinitek.sirm.nocode.common.web.RequestContext;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 2025.0812
 * @since 1.0.0-SNAPSHOT
 */
@Slf4j
@Component
public class CustomClientTokenInterceptor implements RequestInterceptor {
    @Resource
    private TokenFilterUtil tokenFilterUtil;
    /**
     * 认证授权
     */
    @Value("${nocode.auth.authorization:}")
    private String authorization;

    @Override
    public void apply(RequestTemplate requestTemplate) {
        String token = RequestContext.getAccessToken();
        if (StringUtils.isNotBlank(token)) {
            String cookie = tokenFilterUtil.getTokenName() + "=" + token;
            requestTemplate.header(tokenFilterUtil.getTokenName(), token);
            requestTemplate.header("Cookie", cookie);
        }
        if (StringUtils.isNotBlank(authorization)) {
            requestTemplate.header("Authorization", authorization);
        }
    }
}
