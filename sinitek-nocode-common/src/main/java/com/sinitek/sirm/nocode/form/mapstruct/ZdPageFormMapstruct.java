package com.sinitek.sirm.nocode.form.mapstruct;

import com.sinitek.sirm.nocode.form.dto.ZdPageFormDTO;
import com.sinitek.sirm.nocode.form.entity.ZdPageForm;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2025.0711
 * @since 1.0.0-SNAPSHOT
 */
@Mapper(componentModel = "spring")
public interface ZdPageFormMapstruct {
    // 正向映射：ZdPageForm → ZdPageFormDTO
    ZdPageFormDTO toDTO(ZdPageForm pageForm);

    @InheritInverseConfiguration(name = "toDTO")
    ZdPageForm toEntity(ZdPageFormDTO dto);

    List<ZdPageFormDTO> toDTOList(List<ZdPageForm> pageFormList);
}
