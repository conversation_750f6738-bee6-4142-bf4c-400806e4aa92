package com.sinitek.sirm.nocode.app.mapstruct;

import com.sinitek.sirm.nocode.app.dto.ZdAppAiSettingDTO;
import com.sinitek.sirm.nocode.app.entity.ZdAppAiSetting;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2025.0729
 * @since 1.0.0-SNAPSHOT
 */
@Mapper(componentModel = "spring")
public interface ZdAppAiSettingMapstruct {

    ZdAppAiSettingDTO toDTO(ZdAppAiSetting appManager);

    ZdAppAiSetting toEntity(ZdAppAiSettingDTO dto);


    List<ZdAppAiSettingDTO> toDTOList(List<ZdAppAiSetting> appManager);
}
