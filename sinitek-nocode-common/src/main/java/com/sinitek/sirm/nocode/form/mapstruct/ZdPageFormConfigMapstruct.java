package com.sinitek.sirm.nocode.form.mapstruct;

import com.sinitek.sirm.nocode.form.dto.ZdPageFormConfigDTO;
import com.sinitek.sirm.nocode.form.entity.ZdPageFormConfig;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2025.0711
 * @since 1.0.0-SNAPSHOT
 */
@Mapper(componentModel = "spring")
public interface ZdPageFormConfigMapstruct {
    // 正向映射：ZdPageFormConfig → ZdPageFormConfigDTO
    ZdPageFormConfigDTO toDTO(ZdPageFormConfig pageFormConfig);

    @InheritInverseConfiguration(name = "toDTO")
    ZdPageFormConfig toEntity(ZdPageFormConfigDTO dto);


    List<ZdPageFormConfigDTO> toDTOList(List<ZdPageFormConfig> list);
}
