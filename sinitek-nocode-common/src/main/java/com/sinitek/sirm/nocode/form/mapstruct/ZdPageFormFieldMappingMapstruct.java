package com.sinitek.sirm.nocode.form.mapstruct;

import com.sinitek.sirm.nocode.form.dto.ZdPageFormFieldMappingDTO;
import com.sinitek.sirm.nocode.form.entity.ZdPageFormFieldMapping;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;

/**
 * <AUTHOR>
 * @version 2025.0711
 * @since 1.0.0-SNAPSHOT
 */
@Mapper(componentModel = "spring")
public interface ZdPageFormFieldMappingMapstruct {
    // 正向映射：ZdPageFormFieldMapping → ZdPageFormFieldMappingDTO
    ZdPageFormFieldMappingDTO toDTO(ZdPageFormFieldMapping pageFormFieldMapping);
    
    @InheritInverseConfiguration(name = "toDTO")
    ZdPageFormFieldMapping toEntity(ZdPageFormFieldMappingDTO dto);
}
